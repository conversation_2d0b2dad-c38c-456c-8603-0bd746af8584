{"buildFiles": ["/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/StudioProjects/soho-souk/android/app/.cxx/Debug/5cw3x2c6/armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/StudioProjects/soho-souk/android/app/.cxx/Debug/5cw3x2c6/armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}