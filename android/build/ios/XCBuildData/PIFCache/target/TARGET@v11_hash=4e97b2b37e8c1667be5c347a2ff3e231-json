{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ecc5c4a6c8a7db24b519711faecddb0a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/TOCropViewController/TOCropViewController-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/TOCropViewController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/TOCropViewController/TOCropViewController.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "TOCropViewController", "PRODUCT_NAME": "TOCropViewController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980191519d1162d3188cc0841d95c15063", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d5e553e7ebe30f82e0936dca8ba72d46", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/TOCropViewController/TOCropViewController-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/TOCropViewController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/TOCropViewController/TOCropViewController.modulemap", "PRODUCT_MODULE_NAME": "TOCropViewController", "PRODUCT_NAME": "TOCropViewController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f9c9bbef9e4a1e75da50798c5c0ccdca", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d5e553e7ebe30f82e0936dca8ba72d46", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/TOCropViewController/TOCropViewController-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/TOCropViewController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/TOCropViewController/TOCropViewController.modulemap", "PRODUCT_MODULE_NAME": "TOCropViewController", "PRODUCT_NAME": "TOCropViewController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ebd574632a13f8506ed74ee2c9cf08b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c3069a7b08bd851f7c3564c5b9c5e549", "guid": "bfdfe7dc352907fc980b868725387e980a4dfaa604a5c973bd4cb5802c9855ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f787b46bf9b90daa2f787ce59da80834", "guid": "bfdfe7dc352907fc980b868725387e9863499551374c1c9ce8b5b4772d95cd02", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c03282a0f8bb5c69b70e1668e7706734", "guid": "bfdfe7dc352907fc980b868725387e98729b04462a09cdeae6d1711d1cca87d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872113a1011e29fda1b2311e7dfbe51ba", "guid": "bfdfe7dc352907fc980b868725387e98d7cc822e6ff7acf3dda3e6f4e7a05fe2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830726463d1e71fd87e75d0b53e3f8a09", "guid": "bfdfe7dc352907fc980b868725387e9869aa9e264a1406d02e21ec0e12782b90", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ad1aeb3d8fac95f5d5550705dca20ac", "guid": "bfdfe7dc352907fc980b868725387e988c61a099ce5bda3b8c423bda9a71d3fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e21a8746257461083467111e79e7bc56", "guid": "bfdfe7dc352907fc980b868725387e98969de66fdf5f5ba640fa0b4fd2b1983d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d32c0adfc5bf6dd6914f35f656fc2ad3", "guid": "bfdfe7dc352907fc980b868725387e9892aac1b38c1207e05c8972ff02b5577d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988502d7b5b12ed5a49b63669010707936", "guid": "bfdfe7dc352907fc980b868725387e98a105d635d93b75cace7bbd27b86b8e73", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981afb9f74d4e22e60e4a65b83fd06d643", "guid": "bfdfe7dc352907fc980b868725387e98fe2d3380e9a9e674f15a2ea4eea9bba1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c41add94f993c44a6679100d3bcb7d6a", "guid": "bfdfe7dc352907fc980b868725387e98311b7b43caef2eb83b6f84d293bffd52", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989823e64e9e00d6500db27cd56bb64766", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981ae73d9e65fc8a49a18eb05a3450ac7e", "guid": "bfdfe7dc352907fc980b868725387e98f2854e2e6c4b0efdadd510b4c7df08e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981df9ebd1a42a5e0c667b4e5e44e11c30", "guid": "bfdfe7dc352907fc980b868725387e98862f204c2217ec1fd0f6c2541cb1d7d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851b21e50e3fe29555feb3731f233f98e", "guid": "bfdfe7dc352907fc980b868725387e98c4192ed237fd06694e27828b8f6eaa20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a770ed7a3832f58b83d73c141ca19def", "guid": "bfdfe7dc352907fc980b868725387e98314f445b68467d09bc944f6977d6a1e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bba6692ead7e2c4fd3ec8fed1f36dd02", "guid": "bfdfe7dc352907fc980b868725387e9899e94270ee0a9fc2990fe6311d9e930c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6bd1f49595d768a77313a6a81703cbf", "guid": "bfdfe7dc352907fc980b868725387e9874865e6e3e9d40fd69956302ea6a16b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad8f91ccb135ef7725d87e2f219bdd97", "guid": "bfdfe7dc352907fc980b868725387e9830df155f1ccf9cf1ff06feda787776e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846cbae2b750f2da6512c5df12af694bd", "guid": "bfdfe7dc352907fc980b868725387e98a9f944ecbfbd6276045c1acd677fdc87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d23e5b38cb62730c20d568179f6360d", "guid": "bfdfe7dc352907fc980b868725387e9834639a5bdb4f50681ddcc792d8385313"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851715d24a2f8da37d32270b6e4ef7ab3", "guid": "bfdfe7dc352907fc980b868725387e983da8a6c162fb34e1a67bb7ab1d4b8cc7"}], "guid": "bfdfe7dc352907fc980b868725387e98b973c71a5c8d9944a2711af778a2c3b6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9839d638a8f2faf23e558185f2272427fb", "guid": "bfdfe7dc352907fc980b868725387e98269f2208e6af9ec57440cdb8e9a1fdb7"}], "guid": "bfdfe7dc352907fc980b868725387e988710db881c4cd7a5ab2e4ff5e3783e93", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981ebf3826f47dbb7fd582b4ad06af832b", "targetReference": "bfdfe7dc352907fc980b868725387e986dbfa2df59ddcae0f992dedaee8f3553"}], "guid": "bfdfe7dc352907fc980b868725387e98561889aae53f203fe8d20bd552b4212b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e986dbfa2df59ddcae0f992dedaee8f3553", "name": "TOCropViewController-TOCropViewControllerBundle"}], "guid": "bfdfe7dc352907fc980b868725387e987a4af56e2729cecfad7b13d62a9a5fa4", "name": "TOCropViewController", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9874cf314b58ac20a75c1512ceb8885e00", "name": "TOCropViewController.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}