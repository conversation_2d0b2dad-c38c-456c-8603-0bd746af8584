{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987660036f6d1b69847b098ad89cfb5b6a", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKPhotoGallery", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKPhotoGallery", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/ResourceBundle-DKPhotoGallery-DKPhotoGallery-Info.plist", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98a9faed75447cfb83a077875841e2b3a8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9859510259f5a34f4c3081f398c5626e99", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKPhotoGallery", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKPhotoGallery", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/ResourceBundle-DKPhotoGallery-DKPhotoGallery-Info.plist", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e980a10b7b7566301e9a7875ba94cebf887", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9859510259f5a34f4c3081f398c5626e99", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKPhotoGallery", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKPhotoGallery", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/ResourceBundle-DKPhotoGallery-DKPhotoGallery-Info.plist", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98c1f4d0bf01948bd6065cf76b7f0ad06c", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98d53f7feba3eb2d711aba0ab331aa0ca7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98add096ebb5ededed95a0cdd86a76f55c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834532531c956dd33d57b6c158b449c31", "guid": "bfdfe7dc352907fc980b868725387e981c54c2f9a3bee1dfeccd8989fe8f612c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98243d9c8fcb32b5d7c8b53eed98e54a8d", "guid": "bfdfe7dc352907fc980b868725387e98de738761a1ee386266059fac273122c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e29351648007406b07ce97dacd38677", "guid": "bfdfe7dc352907fc980b868725387e98616d93e9a0aca34d53cf5f3dfa4c333c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985dafd06fdfdf836eb05f372f3c187d6d", "guid": "bfdfe7dc352907fc980b868725387e98c9364032335631d8b356f799be8d02db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f09451bdd39f2697b93b2d2603137347", "guid": "bfdfe7dc352907fc980b868725387e98d75551aeb2ea18cc9c055fc18c7a9430"}], "guid": "bfdfe7dc352907fc980b868725387e989ee1964c7a9b684aa74a527e532da0c6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9845db4417944723d8b91f4a6c67e94d3c", "name": "DKPhotoGallery.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}