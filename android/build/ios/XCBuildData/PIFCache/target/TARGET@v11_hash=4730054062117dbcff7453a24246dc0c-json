{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98836f8bf92de6531a543cecc0308d2be3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/image_picker_ios/image_picker_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/image_picker_ios/image_picker_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/image_picker_ios/image_picker_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "image_picker_ios", "PRODUCT_NAME": "image_picker_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989daecbeb58b40f1bcfa4d139d6ad2b9e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98193b2e70a5bd7624acba509b3a51d54e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/image_picker_ios/image_picker_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/image_picker_ios/image_picker_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/image_picker_ios/image_picker_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "image_picker_ios", "PRODUCT_NAME": "image_picker_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a998bdd3e45b0ef62db804b76ae33a5e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98193b2e70a5bd7624acba509b3a51d54e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/image_picker_ios/image_picker_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/image_picker_ios/image_picker_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/image_picker_ios/image_picker_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "image_picker_ios", "PRODUCT_NAME": "image_picker_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9896a3d85e8c4649703f6a94e133f01d07", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bc19cfff842a9ac9c503427c4c6255ce", "guid": "bfdfe7dc352907fc980b868725387e980e6d553b111c496af3173e9c49a30e28", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f95712c2a652bd576691d0af7fad27c", "guid": "bfdfe7dc352907fc980b868725387e9890a1a3581905d20b30a1b82290fef116", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb56cae99bf25474795f047bc7d480d1", "guid": "bfdfe7dc352907fc980b868725387e98b62751aa7cdd6a8ce5fd729301a0a000", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988aaa00e1230e8fdf80640cc38c81a323", "guid": "bfdfe7dc352907fc980b868725387e9870487d13c4405f0d34e50cb59114e83b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987df1b284e2a4e92b4bd2d013630ec359", "guid": "bfdfe7dc352907fc980b868725387e98c9826d3b961aacc15504e9b0337dc0f8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98556eb0e148f349ea6834b8316b234417", "guid": "bfdfe7dc352907fc980b868725387e987e0b31e4700b18a1a5ec43eaf8df94be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b0c10e6d790d36556ef1286d9113df2", "guid": "bfdfe7dc352907fc980b868725387e9884b18546de5ad3b48b3d5ab257ea82b1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987db95503a8a29d64ec577dc59be08c7c", "guid": "bfdfe7dc352907fc980b868725387e98bc6f037b040b19357acdb3226b1b6a20", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e2d9a7d2df2c62b67f94e96f56479e4f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985a0b7cd397a7199961198866dbef6dfc", "guid": "bfdfe7dc352907fc980b868725387e984ef76018083286e5e91e7d2c43f00d63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839ff2a18ec4830b2176493e18a76bbc6", "guid": "bfdfe7dc352907fc980b868725387e983c8bf518e78f01b1c65dc35663adb288"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877a299bff14b5e89cec2a4f93356173f", "guid": "bfdfe7dc352907fc980b868725387e986d4ed2b93961f1a0426eb87327bed5d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810b4c5168b37bd48824156acb9a372ec", "guid": "bfdfe7dc352907fc980b868725387e98989f81bfca531bc7b281d1a8250eb0bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0c3ae9f4071913376e534009a01c373", "guid": "bfdfe7dc352907fc980b868725387e987d2c0f3ff2049d29d103a5ec27f27eaf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98692067aadea33116340271e2e38389ff", "guid": "bfdfe7dc352907fc980b868725387e98e6366aad00e4793b2433c9ef2a35fdf3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f96ce2720b90443da57120a3dddc08c", "guid": "bfdfe7dc352907fc980b868725387e980d54ee79324e83433b0d4496ad72f860"}], "guid": "bfdfe7dc352907fc980b868725387e989840e511dfdf6a3f4b2aed6486f0e664", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9839d638a8f2faf23e558185f2272427fb", "guid": "bfdfe7dc352907fc980b868725387e983727b0d044be61efe5e23e3c04d805b1"}], "guid": "bfdfe7dc352907fc980b868725387e985e5a22f8a8b0575957086b10ff09a4a3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e663c4dbf4cc8678d6aa67a178cb6e60", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e981f000f066404b97b12e9c4ca84d38d0f", "name": "image_picker_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988e06e8c3685b7c12032d8059f412f4cb", "name": "image_picker_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}