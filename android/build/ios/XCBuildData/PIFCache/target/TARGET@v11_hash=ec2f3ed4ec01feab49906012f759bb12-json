{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9853a68b66bcf516b4b3e1652838af8e26", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981001c0730be0a885eab6b83c72d40b98", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98976e99c7b56021940afd65e49b0a4120", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d9efd836eb8d8fb9a46783a19b6a57ca", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98976e99c7b56021940afd65e49b0a4120", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ee04e967556afe0403f317cf2c9df461", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982423ab778b7668f3ffbfd5d990492258", "guid": "bfdfe7dc352907fc980b868725387e98b1aa4156ac660a8caecfcb51a259d93b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8a6fb018363b2303d8eec4103adb3d4", "guid": "bfdfe7dc352907fc980b868725387e988c4b6609fcb53a55c890e33b8980d335", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6de9513d215aa7c576ca64b3865db79", "guid": "bfdfe7dc352907fc980b868725387e9836434b74bd7a63bd5ac89a866ea6b457", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820afc435c798098bb7bb42a4ecc17f08", "guid": "bfdfe7dc352907fc980b868725387e98ff41269993d98cd265029cea2cf8d1a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98439e8fa11bbfd63f652e74e5655f2af7", "guid": "bfdfe7dc352907fc980b868725387e98b66fe0287bef81c271e18478e9acf399", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bcf3732c065a6d5c28853ebdb3d4d37", "guid": "bfdfe7dc352907fc980b868725387e9879015afd2696530737b6741d25b20ed9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb38f8d1eff5c107c7db0b6b03af678b", "guid": "bfdfe7dc352907fc980b868725387e98bf5ee787e019b277858b8bd5d8f533cd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b17ac3668819c19fabaf07b8d0e79142", "guid": "bfdfe7dc352907fc980b868725387e98a5b941646230429ea62032341409a7f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f1360a364f0f31eb0b39a3d0051d642", "guid": "bfdfe7dc352907fc980b868725387e981c2fe602f1c6d7ef94c6fe2966dc8aff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5cab91ed60e5b56a8fc5717c20d1149", "guid": "bfdfe7dc352907fc980b868725387e98acb5cff6b70c6e36ea474f13172a1602", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809bc1979d379b38286be7fe0ef5d4088", "guid": "bfdfe7dc352907fc980b868725387e98d3f7069a336d6c25b2dc78fefcd3889a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a689a54c8559a09ed9bd0a2c97fd3154", "guid": "bfdfe7dc352907fc980b868725387e986a470a56ee5fdb12c372906de32342ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4a8a72eb943a1ceb4de3a87fe2f8f26", "guid": "bfdfe7dc352907fc980b868725387e98798e3b9730dfb43129c253950ae6a07d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98427c427b64bfabc3dd60d2c3f30c6d6f", "guid": "bfdfe7dc352907fc980b868725387e98bd368e5d6fd8aeb9e74c1cb920282be1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f865d2614a31eb26b01dc57d32dde010", "guid": "bfdfe7dc352907fc980b868725387e98a42a6e9847300549e19d9b9d79fe7f99", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98058a75b198ad11da8aecce4b1a725e5e", "guid": "bfdfe7dc352907fc980b868725387e987765423b86b07c842e97d9e7da50f091", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f9185080c3b36a24db552508898b80f", "guid": "bfdfe7dc352907fc980b868725387e9813826d989b03479943c895eecb8b30f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98986b3e92ff70cf42d41264d85bbb6339", "guid": "bfdfe7dc352907fc980b868725387e988ffab619e2f679bcb537657037fdb020", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfa95f6f58d8a1ed21c37f32ce011eea", "guid": "bfdfe7dc352907fc980b868725387e98a34562f0f3512f21518928ea37963e8a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba7c4ef2fb6674f0ae093a3e044c7878", "guid": "bfdfe7dc352907fc980b868725387e9825275747600a60253f942ec99082e14f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825ea936ecc533edf75df8f61073887be", "guid": "bfdfe7dc352907fc980b868725387e98546bccc8b6e07fe28288d20b58a795fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a41d83b44f7f81e6fa55657d4cb4cb7f", "guid": "bfdfe7dc352907fc980b868725387e9863806957c4d26a834a50841c841b8fd3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf63906e52d6e16486c55dcca63591b6", "guid": "bfdfe7dc352907fc980b868725387e9840540f20a718da18f7371b212a1047a1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855d6b668fada993f9ae3bf5eeb338ea3", "guid": "bfdfe7dc352907fc980b868725387e9861328ce12cb8cc63265738c2ecab302b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98559e88a7a4a490955653685b18e42f65", "guid": "bfdfe7dc352907fc980b868725387e9801e552e5987e675ed112d33668945661", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aedddfad77eaaeaef40d67055f985da2", "guid": "bfdfe7dc352907fc980b868725387e98e4dac0b185bec62b302adf4399c87f0a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa741ff1d2b191fd6286a27c5ead1817", "guid": "bfdfe7dc352907fc980b868725387e9847690eb71602a4c6bb2147d12a61d7b1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98431f0a4c6979a76dd717d8f320e12737", "guid": "bfdfe7dc352907fc980b868725387e98c9df32208931a5c496e868d6075ba4a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98813b34e63ef015ad951e2b54a8650acf", "guid": "bfdfe7dc352907fc980b868725387e980df590ec4e0a953c63fe15f0f9f97657", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888a6ea47265a531f915dc6d48af9f303", "guid": "bfdfe7dc352907fc980b868725387e9845bbb587605ed34da4353caa63b0ddcc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9884c23cf05f0eee5227e70339dfa80c0b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834fcd9d5d7b8e73672e34ee9953a935e", "guid": "bfdfe7dc352907fc980b868725387e984738a2d71a07b0ef506d92a0f6ece0a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc213741564f3d53e3d2630d3eb417d2", "guid": "bfdfe7dc352907fc980b868725387e98795ac97d5b1ddc321bb258353841bf21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820bf36bd6936052e3af15384a4741aea", "guid": "bfdfe7dc352907fc980b868725387e98ecf817fa12d5a6ab1ecf2289d447c131"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98daacc29c42904baace40ab5f96975cd2", "guid": "bfdfe7dc352907fc980b868725387e989562374bd3ba684676a21d7606ae04cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f7c21caf781448fa4a7e134ef48d739", "guid": "bfdfe7dc352907fc980b868725387e9822f726dcf0bdca9c3553113fce7722a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f8e688d982a397e57e3d5fb9f5b1361", "guid": "bfdfe7dc352907fc980b868725387e98dd808dbbc84d73779b4e14337c75b390"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98456340ee8d1ee4783cde06bf8abbb013", "guid": "bfdfe7dc352907fc980b868725387e98e89f4858b70cce253d805c5ba47912ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98799d0aa6f24820c03dfa9f6f0a3f8ef0", "guid": "bfdfe7dc352907fc980b868725387e985d7468692f85d35e8146db746a9b3eec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800d6bc0e6074ed97debeda003f3e44e7", "guid": "bfdfe7dc352907fc980b868725387e987a7da07bd35429a29d1518c677962268"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98139244b00dc0187928bbd9d4a486f436", "guid": "bfdfe7dc352907fc980b868725387e98f63862055db146eb26b35116520d2a1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a28a6f4172d31a036acd921e2ea5cb15", "guid": "bfdfe7dc352907fc980b868725387e9888f59b92c214a7abd3a0f78beec8a166"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a99de79775bc0faa3a28574b1ed84d2b", "guid": "bfdfe7dc352907fc980b868725387e980c0b72651146c8e000aaf6ff10e18ef2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879af273943dc9e9c900ab32af9588b4a", "guid": "bfdfe7dc352907fc980b868725387e98537e35024771759c72a3215f1afd1ee4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827645232df563abc025e4d650fca4c5a", "guid": "bfdfe7dc352907fc980b868725387e98c2fa6b4d0017d5268cce73fa0c34d932"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98230334e4453683743ec782cf6078ec80", "guid": "bfdfe7dc352907fc980b868725387e98fa8dd20746cb25ae9e7fdb961aeb1a4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6592625e2c4e791423aa22ca9d0af1d", "guid": "bfdfe7dc352907fc980b868725387e989ac2f3455efb9138520df32e49c9bdec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a7fb748e54aa308bdb979b2c4d0cd29", "guid": "bfdfe7dc352907fc980b868725387e981cc7a56abc54d7b7b1a905725966100e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f640d06cfcf820fecd4f84ce3c8d198", "guid": "bfdfe7dc352907fc980b868725387e98500b3f9d0460aaef3cdf8dbe65dda62b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c4fea0ebf5639fbc014914429bccca4", "guid": "bfdfe7dc352907fc980b868725387e98f9e808454d7cdf88e25b42c21042ccce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a194a2223c164e0c66e128dacf8500e", "guid": "bfdfe7dc352907fc980b868725387e9803ba2caedc1a5a5201743df91b29863e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dea8808c75ac26b7e97f5ee015eb641e", "guid": "bfdfe7dc352907fc980b868725387e989ab7ec910f7f320c0a4810b827963989"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98739feb4dad415958507251ec43b9d7be", "guid": "bfdfe7dc352907fc980b868725387e98665ebcb01990b0b51fa4b05643a3cc38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988619f39d96bd07bc1d88c5e461d33a2b", "guid": "bfdfe7dc352907fc980b868725387e98e68e0ed02b3b6981f8a9244f5bf3f1d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e39ae7fd8e74459ef94b38eede62b35", "guid": "bfdfe7dc352907fc980b868725387e988c7fbce6f0f97770dd6bd9e2bd404ae9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823e6345936cacc0752483e69863201a2", "guid": "bfdfe7dc352907fc980b868725387e98a6bf2bdf92c927a69633f5e1cbcde9d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984693dd2ccc7812d306b28822a90a42ab", "guid": "bfdfe7dc352907fc980b868725387e985c06a06fe01e256b3a261772d69f223e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4a99931efec271437a8931188160b39", "guid": "bfdfe7dc352907fc980b868725387e986e347f28af2291cf9b2f28c155df9d76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873fcdf7d3e980236829b231995ea1331", "guid": "bfdfe7dc352907fc980b868725387e989dd86741fd0b7ca86d4fbf0c08d83670"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca3ef76fca6cdafe1c2900a72bd3377f", "guid": "bfdfe7dc352907fc980b868725387e98cdcf2ea2269cb19dfff1b8e59a12ffe6"}], "guid": "bfdfe7dc352907fc980b868725387e98185c5e77a9122fe6da067114f54a4780", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9839d638a8f2faf23e558185f2272427fb", "guid": "bfdfe7dc352907fc980b868725387e987ea2a709db7783abeef742700773ba2b"}], "guid": "bfdfe7dc352907fc980b868725387e98b181a6bdad6f68b830f688e3553b7e79", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98431b6951e75ab2969ea347937f934558", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e98a95ee7d3b1381217b195dad4e747d567", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}