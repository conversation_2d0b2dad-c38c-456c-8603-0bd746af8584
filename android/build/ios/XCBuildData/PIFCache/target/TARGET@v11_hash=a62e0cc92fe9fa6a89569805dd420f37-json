{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e3e0338072968ed0ba9136b7450a6de2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9878434fabb165aa34e93b326066fb0358", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98813795f5d25f02450f8e75c70272e39a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bfc5284e9e3d782e81764b1fc1100789", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98813795f5d25f02450f8e75c70272e39a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98defc86c4eea3b5384bf82f395e467112", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ffe61aa10c08eccef7624c941c4027a3", "guid": "bfdfe7dc352907fc980b868725387e9834997802b337ab5329f07cab06198049"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841cdec67d045735bed2b488c2b67d1da", "guid": "bfdfe7dc352907fc980b868725387e984ca3237448ec12815e276864e05fcffb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b62f2700e5b051d539098f1a6e9ac2d5", "guid": "bfdfe7dc352907fc980b868725387e98c37443f001dd308ce96a0494e2f6fd8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b5db51c1924f2edb021b61adb834112", "guid": "bfdfe7dc352907fc980b868725387e98de37a182cb2cef67708b10f82594a012"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bed1eca33bcc6a75c99dc76bd590eff6", "guid": "bfdfe7dc352907fc980b868725387e9841df6ed3cf2efde405898752af21f1f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9e5ac8acd6bab9fd424ca1b6093fbba", "guid": "bfdfe7dc352907fc980b868725387e98f3928bd8ac216ce171ba28c6f052c124"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981183f7c6b49767df1d534f01a9e79eca", "guid": "bfdfe7dc352907fc980b868725387e980ad63c3a903d534ece7754b23af4bca8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879e52fecbb7e36cf75150b9eb2d0b927", "guid": "bfdfe7dc352907fc980b868725387e9842d8f039ea025e89fb587ce7f136b2af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ff3f043f1e13e6a8a498fd96c266df6", "guid": "bfdfe7dc352907fc980b868725387e98c5c9fd4b2f2f590c83ac04b32ba476d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a61ac65bf4199f80df6fc52f031fdc6b", "guid": "bfdfe7dc352907fc980b868725387e9838bfa8e3767cb2beb10c1b478e619121"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e087b0987a5df9b0e1a6015a769eed5", "guid": "bfdfe7dc352907fc980b868725387e98d96c43c2100eaaa236d59a4af6fea6cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986478c37c6750361200fd9c9ab2dc4fd4", "guid": "bfdfe7dc352907fc980b868725387e98d3d1125fcb995cd65be936f54eed51d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c9c277577008176a9ad1fcd8e66b1fb", "guid": "bfdfe7dc352907fc980b868725387e987cbac57628c3adc1e3b423ba15a576b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b29ca0343af4ec1e5343d892b3158f1", "guid": "bfdfe7dc352907fc980b868725387e98b7cface0e93bb4353ca09ba1dedd115e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c019fdbb9784715c2814f9ed8dad1a7", "guid": "bfdfe7dc352907fc980b868725387e9824735aa7790637ea60d3c6583bb12e0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4b570f1f3db3ca7859527478f75825d", "guid": "bfdfe7dc352907fc980b868725387e98661a560fad3a1d41183c70324436f82b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d91fd3caa74450f8b3b1cccb5df409bb", "guid": "bfdfe7dc352907fc980b868725387e98bf11e498ce294d752ccf39b93def5fa7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804101849fd2b6c8932cc562fd6ae2b21", "guid": "bfdfe7dc352907fc980b868725387e9823abff0fd107435f4839cfdd0d6145a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980975a41e4528300f19dcd92d992b6f29", "guid": "bfdfe7dc352907fc980b868725387e989da7e604348796072f0fbaa62b5945a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aac18dd621e3c3b511dbc0850691fb5c", "guid": "bfdfe7dc352907fc980b868725387e98a5922f6e848ea4de78b77ee93ba3688c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f4b7290de227494fcb2d36fd6f98d5b", "guid": "bfdfe7dc352907fc980b868725387e9896f61225862cf454f167ce2de422c99b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aaa3c0c10ca86f9b2187ed42ae722558", "guid": "bfdfe7dc352907fc980b868725387e98a776d0d0538234298d664b50f016a4de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a229cfb546c5607cfeae89053820fea", "guid": "bfdfe7dc352907fc980b868725387e985feb5e4fa9ac67b0663586cb1c20d5f6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9874435b74402ee00898fa536bf51d80c1", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988bf6077820591f706774f64ce937b357", "guid": "bfdfe7dc352907fc980b868725387e98a8c2b115cc9618b644a770515ae13202"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a34527a7fd786517584b72f75e061699", "guid": "bfdfe7dc352907fc980b868725387e98d139dbbc41c434590dc93b444831b047"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800c7923a023175401f6ae9d53e778feb", "guid": "bfdfe7dc352907fc980b868725387e987af0afa2e4e7fc809e47cd7fb2d6c86b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0502e31e6d2f96e3b7a6c2ebeeab6a8", "guid": "bfdfe7dc352907fc980b868725387e983099c1776806afd43099a03b3bc737ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849d3d36993299a35e761511246ba6851", "guid": "bfdfe7dc352907fc980b868725387e98bb155ff92b9482e5e27f63e439f34c47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840b1a04d6c0adb0be45fc07e0df37bc4", "guid": "bfdfe7dc352907fc980b868725387e98caa83273137282486ded3414bfdaaeb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980460223b552236abd0b2ceeefc409f32", "guid": "bfdfe7dc352907fc980b868725387e9803101eefe00d81d21c256a5591bb64a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3e40e2964b8738e78c2899dd96e37ba", "guid": "bfdfe7dc352907fc980b868725387e981388685df542ab4b7fdbf99bb0f1d072"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fd9453acbec41f576bbcc560be9d6de", "guid": "bfdfe7dc352907fc980b868725387e98890201b8061f8342424c8ec943729a1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892ea37480f0f406c2c4f3601b15ee94d", "guid": "bfdfe7dc352907fc980b868725387e98ab742b7d101216b1514689c1c6c8a566"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989800861834ca1f42c0bcefab10662453", "guid": "bfdfe7dc352907fc980b868725387e98f7f170db86d5ff2e25b0ff33670ad53a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e152e2e6664ee76a603191a43686f5d8", "guid": "bfdfe7dc352907fc980b868725387e9873a321f6c49328bd324403d3d886ff80"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cda6c2fdd79e0d7f6dcf8e4ceffb2bd", "guid": "bfdfe7dc352907fc980b868725387e9870c4e90b009350e87f838167ab25133b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bf0e63ff5bc7828e04a40063f943a2a", "guid": "bfdfe7dc352907fc980b868725387e98b0acb2063db548ac9ed01ca334cac3a8"}], "guid": "bfdfe7dc352907fc980b868725387e98048037717dd76cb5af49e10684201279", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9839d638a8f2faf23e558185f2272427fb", "guid": "bfdfe7dc352907fc980b868725387e9876dba49efaec95f35a5f204b20ba62f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2688dd06c00e6fc8953aa2042df31e1", "guid": "bfdfe7dc352907fc980b868725387e98e389adbf1ce8d0ce4850af227d3187a7"}], "guid": "bfdfe7dc352907fc980b868725387e988ecf8cf8b24157c76e6b542841e4d849", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98626baf862cd4085ae432fefa7dab1c8d", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e986dc690ec7fb5186c9763bd9e9d840a54", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}