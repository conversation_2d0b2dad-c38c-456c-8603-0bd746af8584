{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d7c399f06cd52250b0f3e3212a3ea5e3", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98a1384fdb7fd614e9eb86bd296d364ac2", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a1dab4372cdb69d5118d34c4efb86951", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98ec2cd031d43b3dfc703bd2d8109ac08e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a1dab4372cdb69d5118d34c4efb86951", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e984f1625e7f46d7fc207e7af9197db8ea3", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e980b6c9c63505da3b0d7e4947f617068b2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e985ccbbb08782a8cbcc074c207bb641167", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9832d0f2f1455c6ef734b0fb7b7e34102b", "guid": "bfdfe7dc352907fc980b868725387e98aa0b4a7b33651b3c2fa210de85fe2c30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e12f8128a14c40200dd3da2b1730be54", "guid": "bfdfe7dc352907fc980b868725387e981fe7e472770a8d1b62d6877c8869d93f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f7e0f7f96e82b73760b3eda0bffd11f", "guid": "bfdfe7dc352907fc980b868725387e98032f1af10fc9ace830194bef154ba0aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982747b596a348f97b83540f1cb1370c4c", "guid": "bfdfe7dc352907fc980b868725387e980f3b788a135239baa51a1b3a6ff8230e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3e127123bb09a01a0fcdf16ec40cfee", "guid": "bfdfe7dc352907fc980b868725387e98c330de3a4f0808304f0d2696288ccc74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e0d6f6a19985a645aa69eb6b0506b90", "guid": "bfdfe7dc352907fc980b868725387e989fc45af42e3ae34667c12e97cab06194"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b586f7b160e10b936d9eb1b1691ddf70", "guid": "bfdfe7dc352907fc980b868725387e980d49f68266e5c46a02d869273ab27058"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0af765c0a7b53de183fd1f0cd990d5d", "guid": "bfdfe7dc352907fc980b868725387e984e40e76ddb4ecb850f8b22bf11ed6ff5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98773430cbad1836e02eb02009973eb54b", "guid": "bfdfe7dc352907fc980b868725387e985949a9d720497c69ed0428cabd851b84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d01329d87849399afde8caf8d1cee530", "guid": "bfdfe7dc352907fc980b868725387e98ffb40f578500c2279a52ee8f2d4be561"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0ce5fdc66f865d6f4c0965a33209e0b", "guid": "bfdfe7dc352907fc980b868725387e98ee37544474301b52709864ae2ab5ebf6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813e476e60fc07061eff07a341284d413", "guid": "bfdfe7dc352907fc980b868725387e9898bfa978add313bc69d98877b1e7aa25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b772894daa7ec7bb3beec0210c2217be", "guid": "bfdfe7dc352907fc980b868725387e98c829f3a3a91b6790535d9b0eb817ab3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aca8c926a1694bc3b0673c425a38dcb1", "guid": "bfdfe7dc352907fc980b868725387e98cbf0381b0504504bf700d6a6c838e2a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba9b7204597260f9cd5334d80e1c4462", "guid": "bfdfe7dc352907fc980b868725387e984f79215885cfa6aefdcad048c669ccb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865b8c3e5391c09424fe06ceeaa1c5056", "guid": "bfdfe7dc352907fc980b868725387e98e42016dd3028134fa3afccb3bdf02800"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c9b8cb38f59a481a4f19afd36c50875", "guid": "bfdfe7dc352907fc980b868725387e98896aac47ff75edd4e2cb9b336299e947"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877c5f1637b73e8fa7e5c1b893e18272c", "guid": "bfdfe7dc352907fc980b868725387e9839110289d6aefb96d5c1e6b00a0b9d0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efad2ce745afed5954d6160a4ba03ea2", "guid": "bfdfe7dc352907fc980b868725387e983c57e4978c73ffa1f41626a7208446a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fbb27997625a181cf42bdd1d52352d4", "guid": "bfdfe7dc352907fc980b868725387e980b76fcd20445c1a01492d62065af7394"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3abf0398e23be17b316055fdb1f4c7e", "guid": "bfdfe7dc352907fc980b868725387e987e42b800115236970555fefa300e2d52"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ade3201e19dce1032517b7ca51cc9b6", "guid": "bfdfe7dc352907fc980b868725387e98459afbf892f9a43b14e537c7b96bb033"}], "guid": "bfdfe7dc352907fc980b868725387e98bed6294670a9a7a158fbfd936a3ee277", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}