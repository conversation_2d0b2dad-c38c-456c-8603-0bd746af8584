{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985fe7b419b84ba48e0563d03d7e0fbc55", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988739fe12e2029ec33474d51e1c1de486", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984fd52aa849e30f352c19257d771c23b1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e7e194b3c7a0dcadb37a08a8959714bb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984fd52aa849e30f352c19257d771c23b1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ad06093d4c8fed0ad01765d765863350", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a1d299d6830d7c27943e1d2993e4bc67", "guid": "bfdfe7dc352907fc980b868725387e982f7568700678161c9dd366d7739bf413", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b283dc6f2aeed34387cbe7946c71e97d", "guid": "bfdfe7dc352907fc980b868725387e98d2cfda2b91f7d3c62d5d58ac0720631b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ccf2b37be4fe15ce17e888716a14b8e", "guid": "bfdfe7dc352907fc980b868725387e98da83a98fd24fc42cbdeb9d8e3cd4b528", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ec3002c2ada61c921c2d6624078a7be", "guid": "bfdfe7dc352907fc980b868725387e98b0273cfa1e83809c29cb1611e317ae58", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f828b2f4d08a7bfd86d36775ed33e8ee", "guid": "bfdfe7dc352907fc980b868725387e9882a0056e1d6c46c652ad936bd1dcb041", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987721c292bd8f2c7b5c0718b94c4495c4", "guid": "bfdfe7dc352907fc980b868725387e98690acf0eb0f2e5d0a7c2fd8d02831f79", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a00765c707f4882af120654e340eb541", "guid": "bfdfe7dc352907fc980b868725387e983b2088b54c956e72af1bcc04e5323e36", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98891040ebede2324384701bb1e3486756", "guid": "bfdfe7dc352907fc980b868725387e98d251b8a29e72bf69c64010adaf2ff5b8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833b5879859919b21899fce85a7445c34", "guid": "bfdfe7dc352907fc980b868725387e98ead8bd5ca01d3399fbf2f64cfcf44cda", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983075703f2da5b3c40f97d9830b3dad02", "guid": "bfdfe7dc352907fc980b868725387e982d1885ee461a4a86d551263d37a65684", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810b4d5d396bcdc3e384af67cfe2c0bdc", "guid": "bfdfe7dc352907fc980b868725387e9883a0243e7283fc84dd94da3e3c60550b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98366f4d1731d2bd27499146da26955239", "guid": "bfdfe7dc352907fc980b868725387e980e2457fb8da8474f3b6b35b3af9dc4e9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983de3f18154daf12cc5b65c587722294e", "guid": "bfdfe7dc352907fc980b868725387e988eb289063e1dc2a8bf6ac20428b9be7e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4107b78d02d6ad8d6330d206d7f69cb", "guid": "bfdfe7dc352907fc980b868725387e98d3df0ffc1eab92385cb1b2fb93b48b51", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98527db3c40639cc6759f0e8015001fafc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982156cb512ff038847c4942f1b1b52e57", "guid": "bfdfe7dc352907fc980b868725387e98475fc8e8b3ec4b7dd01534402dce2e25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836aa289a490d96f854c95b71325e072b", "guid": "bfdfe7dc352907fc980b868725387e981ed00ab0a67e08601b5d2121a7c282ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837338fe06f8fa7c3419dec2275690eb0", "guid": "bfdfe7dc352907fc980b868725387e98eacb0640eff12ef3f474b6b4ff18f2b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814e1260a5b0f5cddc9dcf6a6137d09fc", "guid": "bfdfe7dc352907fc980b868725387e987ca466c509960c76f504aa440f959111"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d53586db6e6460ca9f18415983782664", "guid": "bfdfe7dc352907fc980b868725387e98b34c2f90415593f69a4bae88b460d148"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac20840afbd0566de512b0096954f441", "guid": "bfdfe7dc352907fc980b868725387e98173e6ab4b6a80494595b9cff87cc4975"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fed006db853eaa5bd3fcabbdd2515990", "guid": "bfdfe7dc352907fc980b868725387e9894682098529ecc10317a9a2829230e90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f31a50af3739df1d6b177d4c6d1ecce", "guid": "bfdfe7dc352907fc980b868725387e988d6723e642ebce2b2a4d15713133361c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d59fede8e65b78d8ab5fd567e518eb0e", "guid": "bfdfe7dc352907fc980b868725387e9816e87d999b8e723090cdf84363282a2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cabc24af5a2c4967b2ff3a08334ed1e", "guid": "bfdfe7dc352907fc980b868725387e98251eed0b456a9dc27a662e68172b1268"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb8ed39b337465ecad9c3ea31b9cd62b", "guid": "bfdfe7dc352907fc980b868725387e986cd27923800027000021ecd3a1885955"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fff3832d2e968526083ac530370a6324", "guid": "bfdfe7dc352907fc980b868725387e9822c2ef0d2e264ade7da5fb2ed8fbdd19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888f14bc95feb4d754cc4d6da48841d1b", "guid": "bfdfe7dc352907fc980b868725387e986184f9969efe2064146dc2b51ba47b08"}], "guid": "bfdfe7dc352907fc980b868725387e98445c13b134f6717c552fb845dd07cf6c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9839d638a8f2faf23e558185f2272427fb", "guid": "bfdfe7dc352907fc980b868725387e987208fa6207d938fe537d0e8359bf5699"}], "guid": "bfdfe7dc352907fc980b868725387e98ee8cf58f2eaf1185d54859fdaf1a4231", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e1b65cad46427b34676adbcb934284e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}