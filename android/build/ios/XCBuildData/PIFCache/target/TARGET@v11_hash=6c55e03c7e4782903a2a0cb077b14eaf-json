{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988bb1bbcd7795e79cf90e327f01de0c61", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98815aaae0c71dcaf8f97be95827d240ea", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bd620ef1b8ab1560adbaee9f2d4e4df4", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984435b780288ae753f55f2db9ecd580dd", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bd620ef1b8ab1560adbaee9f2d4e4df4", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d2e3be2c56f23d9dadd8a70d4a319a31", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98be1aa0e3f6c1ca44de5611e1a5d44ddd", "guid": "bfdfe7dc352907fc980b868725387e98edd7b8e355de4ac6aeeb13ae26257fe7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae36b6b8b636b741fc3084cc401b11c1", "guid": "bfdfe7dc352907fc980b868725387e98c56e03ec7548a864770f9e0aac05a321", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae818644e119183774aa9853c6028902", "guid": "bfdfe7dc352907fc980b868725387e9807cc5c5f0178741fe2928843258e9050", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e701e7bbfd8027a761e6984ef69cd78", "guid": "bfdfe7dc352907fc980b868725387e988221cadfb36ee1b0e66842ef21b61fd2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98439c630a216c36933f73f6d52880c8b2", "guid": "bfdfe7dc352907fc980b868725387e98c3514f1dd9fbb79fd7faba4e7e2c5cb5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98097a6607168290e3c8714a53f03456cc", "guid": "bfdfe7dc352907fc980b868725387e98c5c3339ed2d1e16401784bcdd44bf07e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851111a466b0d71b47c16f6ea24805628", "guid": "bfdfe7dc352907fc980b868725387e988be3799c355071ff3bd244385963ef9f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981796849105fef3357f19fe8441d0662c", "guid": "bfdfe7dc352907fc980b868725387e98c6191f5df47baf3533a8f1e89904146b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfb440ecb8b8dbbd488f89a4bcefcc85", "guid": "bfdfe7dc352907fc980b868725387e98c8608849239095cfbb14a24344929ede", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98958602b9acc7cb04f3e240fe0233f8d8", "guid": "bfdfe7dc352907fc980b868725387e984348958b998d5a4238530f66e7c1f194", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2bd7923801dab25f04dbfb24c0db317", "guid": "bfdfe7dc352907fc980b868725387e98f8c7e8ea43e94709668c247ebb9d045a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cb7ed9448e41f336cdb61b6198a2ed4", "guid": "bfdfe7dc352907fc980b868725387e989c89b6fd8f5b1675c7a481f689d05a2c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d5965e0d7457d6ca4b2c7b57a6ad551", "guid": "bfdfe7dc352907fc980b868725387e98ab7832eab7307af294e42b52894be7ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98221053aae590b0ab4c9f96d3b75a16a2", "guid": "bfdfe7dc352907fc980b868725387e98054c10c2033de245cf6632aeaed45877", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db892d0918199561f3e5c7f1b5617d3c", "guid": "bfdfe7dc352907fc980b868725387e9826c07d1f7b34bd669f37fa70e8ea4512", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec7d2613cd8de7766619669dddf9dc47", "guid": "bfdfe7dc352907fc980b868725387e98993336eed6752d146417bf19b505d548", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98832873c0c52ea162932f21550664e4a7", "guid": "bfdfe7dc352907fc980b868725387e986484dcdbf1c32683f8c123df82134cda", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878318f75b9cd2184e4c919afdce4fb8c", "guid": "bfdfe7dc352907fc980b868725387e98ba816b92b4ffe00659c04a0389c10525", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98267fc9ce2da1198b49ec31f5fef6c78a", "guid": "bfdfe7dc352907fc980b868725387e98e1ec4b1094ac16922ff9dbe2446c4586", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f497dfafe6c4bd322a14abc7519d4c5c", "guid": "bfdfe7dc352907fc980b868725387e98ddc32698b36f168ab9f73cee0eda7606", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e33b8431cdcf5b97ba541c4f0923d84a", "guid": "bfdfe7dc352907fc980b868725387e985d1518224cf26ef46964a8257c92db63", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987aca135b701a32dbe73cabcfc9f5aa4e", "guid": "bfdfe7dc352907fc980b868725387e985f820d5faa2f4271077dd4008e64c6b8", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98f1b3637dbe03466a2ff48ff09ab00367", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9828578340896e1d7d7a1cf30cbf6fce15", "guid": "bfdfe7dc352907fc980b868725387e9892b707ddd3d2750cf2567ff99229a4e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b90e8f6b517c8b77e0d051735a4885e5", "guid": "bfdfe7dc352907fc980b868725387e983c6bce2792fdbb384f25cf3785c22e56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e5ab8a256a2145d1e0b7f21e8e423d7", "guid": "bfdfe7dc352907fc980b868725387e988a63967da9c3c29b0e68084896ed308a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f43b9ac1a5120eac14d66fc5ea43a1b", "guid": "bfdfe7dc352907fc980b868725387e98df3447ab4b1499d3da3bb56364018a90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807d7d669e1eef78d36d69c6194fa5817", "guid": "bfdfe7dc352907fc980b868725387e983de1cd3fe62ed523185beb28afbe8427"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8a4daa496271294e59adfe64987833f", "guid": "bfdfe7dc352907fc980b868725387e98ee812f311f09154187aeef8a1aabbf68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d8df7a37109367337d05e2edc4b336d", "guid": "bfdfe7dc352907fc980b868725387e98e06fe19b2c6c9f1bc29503dcce489ab5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfc759bd2fa6c16ae2991f00b002cd5e", "guid": "bfdfe7dc352907fc980b868725387e981db506510350fe8b90b0dd9787bfdf1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885cada529b5d755c7f5d97fc5369623c", "guid": "bfdfe7dc352907fc980b868725387e98181ac998bf69d0999e69121bb086a9b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d142a8b235b674c68b540ee004a96e92", "guid": "bfdfe7dc352907fc980b868725387e98e53334221417a07f3a607f02137cc793"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da6266ab79663476c302e1a40140104d", "guid": "bfdfe7dc352907fc980b868725387e98e09bdfbe6fab0b24f0f0ca39ad969020"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878f821be68812dbb3951fe5ecbdd7770", "guid": "bfdfe7dc352907fc980b868725387e98650b2977c684ac34f8ed1f8539c87db7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e206d1fc35faf4ee15835352b9a2c1a", "guid": "bfdfe7dc352907fc980b868725387e984e3ebc35df684f487207b97275c6953b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a08503223c9818840b8e2b994013ca1b", "guid": "bfdfe7dc352907fc980b868725387e98f3bf431d5910e02890c30db83980d95c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc0aa92aef89ef34f0424863fc0740c4", "guid": "bfdfe7dc352907fc980b868725387e9882468d4689b6c640365e553f56e3f085"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e57bb9c9d088cdab791c60ae69199224", "guid": "bfdfe7dc352907fc980b868725387e98baa3edf9b4660a00438a8ff3ff69a9b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb15f109648e79e76821c184a33c7ca6", "guid": "bfdfe7dc352907fc980b868725387e98bf174e53f89480947220498b0c3bc9e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ed40dd34ceae2c258aa083b0c55e521", "guid": "bfdfe7dc352907fc980b868725387e9868000830a8532e7bf8c0117ee8a5f149"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eec44c10736208857162d58f30c51819", "guid": "bfdfe7dc352907fc980b868725387e98ba9f93d4211ced596a736a4bb1e0866a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b4a57c06f7a96b2d8b7c3d59d2bb437", "guid": "bfdfe7dc352907fc980b868725387e9876ba11741f4205acfdd63946534a2370"}], "guid": "bfdfe7dc352907fc980b868725387e98937da57ea189d4d93e5daf307896f0bb", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9839d638a8f2faf23e558185f2272427fb", "guid": "bfdfe7dc352907fc980b868725387e980701c5515d2e696204f947e98f85ef53"}], "guid": "bfdfe7dc352907fc980b868725387e987e008564a9f1bfea6f6e489e2852a7dd", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98912e8330fb1647330c8624572ee50689", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e980342fbdbab16975fe10cc367cf262c52", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}