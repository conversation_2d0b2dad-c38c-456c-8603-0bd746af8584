{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981f9cbb7dc7a92bb2cc23efd31f9e5a69", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a2197e115869f98f4a7da4a193dd1a67", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9887083b3f302025310f60a3bda76830b1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f7c31083254d26ab832a4e4b4be82489", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9887083b3f302025310f60a3bda76830b1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988bb60af75fa95450a43838d0853b1e1c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9881fd9b4b8133a5abab39e90cca8462c0", "guid": "bfdfe7dc352907fc980b868725387e98f4b53cc6a5dd081f3761c115b6f8e247", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989e5442994eaa389e8e1a983b1d745014", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981eb671d35f1b1525745c6cc76cf1fce3", "guid": "bfdfe7dc352907fc980b868725387e98c9b82e7f3875f3d42ea119f6c25b657c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c583fa56512fe9f687696f3d6071ae89", "guid": "bfdfe7dc352907fc980b868725387e98b10f98c789d7359656dd39798f9ba1ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98044eebfef0a35a330b405b0a9537f1b6", "guid": "bfdfe7dc352907fc980b868725387e984e27363b2df967f832dbc8390711df5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98515824f55043930bd97535279949d930", "guid": "bfdfe7dc352907fc980b868725387e981b5bc27ea08ea44d9480d66d80f46795"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c70f7b66dc48da7110e2910bf63d4dd0", "guid": "bfdfe7dc352907fc980b868725387e98ddd9f1193712a2496cbdc88af98688d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98627fdb6b97c0b25c91ca54a5d5808a77", "guid": "bfdfe7dc352907fc980b868725387e985f1c846612d34a3d1a432c607e998551"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8ccb03665341ef14946eb8a034852b0", "guid": "bfdfe7dc352907fc980b868725387e9828e126d2ab3792c230ffe190888f84a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1859443429defe0d070101511c8c221", "guid": "bfdfe7dc352907fc980b868725387e9847a22075faee534caa6f5d8fa5d6966b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98042f499b44f5a7cfafa4c19d7375ba78", "guid": "bfdfe7dc352907fc980b868725387e98b24e9ae15d80db0759ee4b01a1c4f3e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed1cc55fe63b9eb3a21dcd5bc1197a5c", "guid": "bfdfe7dc352907fc980b868725387e98eb1d7b9122221168aeea3c11d60f64c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3596f8bf37d9d4af73706a3245ec620", "guid": "bfdfe7dc352907fc980b868725387e981ab22442b9f24168ed03189128374064"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a2be2e1c4f37d41ed312c17d4e3ecfb", "guid": "bfdfe7dc352907fc980b868725387e98286abeb8c9e3d6a6bc1113469ac1944b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f31eb7a5c5684810aee33007d552fac4", "guid": "bfdfe7dc352907fc980b868725387e988d74e0017ea6005ba0cdb03f9cc869d4"}], "guid": "bfdfe7dc352907fc980b868725387e98470fe90f9e97a900bf0a7a235c688250", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9839d638a8f2faf23e558185f2272427fb", "guid": "bfdfe7dc352907fc980b868725387e98ec499b8aeb665071a77fb3f1655869ae"}], "guid": "bfdfe7dc352907fc980b868725387e98c3f844747eda452f8497aa520804d15c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9819121f0d77a756beaa12b33e0e7e8ebf", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98456b40de57a8b00842728449d08a6619", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}