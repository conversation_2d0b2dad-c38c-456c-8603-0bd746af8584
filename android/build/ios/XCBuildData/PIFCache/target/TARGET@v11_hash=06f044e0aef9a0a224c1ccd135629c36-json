{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d853245fbe0610cc87c519db816fcdb8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f866c24bb06c100071162a70e4808177", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dfcf14be25be245992a6861cc0c0939a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989f3bbd0ebbd730ecc642c2496fd16d62", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dfcf14be25be245992a6861cc0c0939a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9816955cafcfefa725a1a4c9ef4ef6b85c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d9019feba7a6483551b80a32231746ca", "guid": "bfdfe7dc352907fc980b868725387e989280b6d05a5c9a5a795674f7a065292c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c644f0fd99a3941bd455c3f90f2311b6", "guid": "bfdfe7dc352907fc980b868725387e985d94b36afd2d821a2c677c1519ed11c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983710883ee97cc9a294a8eddbbef931a6", "guid": "bfdfe7dc352907fc980b868725387e98db111f5dc5ef42c69a1535f6ade9d621"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c98c57150530e3a101e9b0cd3ef7a422", "guid": "bfdfe7dc352907fc980b868725387e985609eaf854008e9df4c46742c77728d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985da3c316dcbe6d38f1888705b247ab3c", "guid": "bfdfe7dc352907fc980b868725387e98045bad5e4212a44de7e0c8d66e323c42", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e46d3fad96a8d50351eba3d331d07879", "guid": "bfdfe7dc352907fc980b868725387e98e251bbd588d4b6c28fcf15bb86d1781c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e502022fad102fc51b7d12cdee354f1", "guid": "bfdfe7dc352907fc980b868725387e984c65c7d4202c2299f96b0aa2f9430962", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6f3fe44eb949ac8291485392f162eaf", "guid": "bfdfe7dc352907fc980b868725387e988e20e0d18e8aeebc162a275a2d7971ee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0a4697c62ed17570123db77857b594f", "guid": "bfdfe7dc352907fc980b868725387e983dc8f09c62512477302de6f91304b2cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d51cb2fb68182048a475df4046bc0978", "guid": "bfdfe7dc352907fc980b868725387e98ea4b0cab298b9f6b6afe107809843b3c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3a44b63fea8fd58c7071d6553958098", "guid": "bfdfe7dc352907fc980b868725387e986475556417a86ef29337b1833241c287", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886916b1a283982c511494a5772120ed6", "guid": "bfdfe7dc352907fc980b868725387e98eb654fe4a36ca52be452e0ce37e1ae85", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fd6ef4db00d6c3026f2ee07fc43e89a", "guid": "bfdfe7dc352907fc980b868725387e981ae9d5a98a144f9e62bd4f276adfa0ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981472670b998229519d2089c73c86a176", "guid": "bfdfe7dc352907fc980b868725387e986b9588e15af061089850ac8fc60674db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98326d7b6d865d6f51946284c926375267", "guid": "bfdfe7dc352907fc980b868725387e983df69216b37b8842af00a730654cd187"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d52da5fd8401a6048104e893ee06577e", "guid": "bfdfe7dc352907fc980b868725387e98e796eae6c35b6fba7239183d8c1ac7ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98016d22ea940dfbf61108f96092e8048f", "guid": "bfdfe7dc352907fc980b868725387e98743c745c93323ee62191c92f1f551e3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987aab5b7293736ad66a0718b8763ce12b", "guid": "bfdfe7dc352907fc980b868725387e98cfacc4f338c694bfc23b174b9ae9e523", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea7b6d0740d6b52ca7e1a44953fc9ae4", "guid": "bfdfe7dc352907fc980b868725387e98e26d8a946311ee6f603ba10b3b877fe1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877544dc15bcbe291f5beb5bcaed96764", "guid": "bfdfe7dc352907fc980b868725387e98227f5416dc06c87df867b13fa53fc9b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a151d9531ef6653df54b7daa7620019", "guid": "bfdfe7dc352907fc980b868725387e98b468992667edf17ae8c607da03bb0c10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebd0c9878aa75962da0c72c9d3d94acd", "guid": "bfdfe7dc352907fc980b868725387e98b202fe770d9147730151a4bac9841fe0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5b819f295229703cb917c00fcf70520", "guid": "bfdfe7dc352907fc980b868725387e985b3b3366c33612843f6d3ce2bc516639", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c29f80d6a8ef5fb6812c9affb7fa86d9", "guid": "bfdfe7dc352907fc980b868725387e98173a0521b29319406335f144cfac74a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb2842abb2cd9e69f74e8f7096395b8b", "guid": "bfdfe7dc352907fc980b868725387e98e7c93f68471b974db13aacb642a98be6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa86cb40be0a29477330d73a203f3700", "guid": "bfdfe7dc352907fc980b868725387e98593ab125263deb8bd8e9c3519638078e"}], "guid": "bfdfe7dc352907fc980b868725387e98d401a2984cabbf6e889ed4dc25510de3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988421c65b169641fc630c07d7d2060b16", "guid": "bfdfe7dc352907fc980b868725387e98dc3327d33bdaae84fbd0cbed1e10011e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ca2ed6be9e3a89881659f8145d9f80b", "guid": "bfdfe7dc352907fc980b868725387e98d939e057cc4d7478a73d40de35b32bdb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fe14476adb174dcd0120aa0146246e5", "guid": "bfdfe7dc352907fc980b868725387e9829e3afbe33c2f477fef9812427e083d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6c4a1453e4e67e81188612d826d32b1", "guid": "bfdfe7dc352907fc980b868725387e9807ab8a30702fed35ea003a49f491ecd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a85f24d758eda3080362c0accbb564cb", "guid": "bfdfe7dc352907fc980b868725387e98a04de838d25787216fc15fe2b13d11c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc5afd22187ae461ab7c20a2092019fe", "guid": "bfdfe7dc352907fc980b868725387e988bd72cf30fb183aa809a2da827be321b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877985f1912606960eb4be26ed0aa6bc2", "guid": "bfdfe7dc352907fc980b868725387e98116b07b8aec06fea892ff12636272b4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d11f4837a2fc3b171f2cf93d365e70ec", "guid": "bfdfe7dc352907fc980b868725387e989eefeaa92c7ce43e343e1fa825749a3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef5c5dee7e10226f524cdad59bbf2923", "guid": "bfdfe7dc352907fc980b868725387e9846917590e04e2f7fa48073bb2c418b66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be0d4647f8a43c44d868aa57f2aa4d10", "guid": "bfdfe7dc352907fc980b868725387e98c29fc111321d6ba9434feaeb4fa07d4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834681dc333a998e5f72397da0b3958ff", "guid": "bfdfe7dc352907fc980b868725387e987bc1ab62d427d343fc921bab32404344"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98471a7a7d1e0b5db9cf0ea4b0703557f3", "guid": "bfdfe7dc352907fc980b868725387e98fbedec81432c10c49a8f3f4ce0c5fd73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc7bf5ab16d7576d1212763a22c96cbe", "guid": "bfdfe7dc352907fc980b868725387e9853c12cad057ce9f3bfc097a645d17830"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef672a7c6b2b3c1f5919292476f71805", "guid": "bfdfe7dc352907fc980b868725387e98da669b8e606f3fbf1987d3b62b8adf30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803e545b712640a720c05e6d96db74bad", "guid": "bfdfe7dc352907fc980b868725387e98848f57bc8c2e82cc74b227abe6f0d79d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986964ed554d288bffee0b5902b4af75f8", "guid": "bfdfe7dc352907fc980b868725387e987a86c1e02077fd9cf278ea32dceb8d07"}], "guid": "bfdfe7dc352907fc980b868725387e981a17c7c5e939fdbc6a0c8c177f4b2b18", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9839d638a8f2faf23e558185f2272427fb", "guid": "bfdfe7dc352907fc980b868725387e982463861460cf8174f3ad7c99455ab456"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d31edecd71336b583e35a476ca5d7e6c", "guid": "bfdfe7dc352907fc980b868725387e98f9494dd448d73c25983f8c4cd55dfc39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d34951b0fac4df05cb634758da13455", "guid": "bfdfe7dc352907fc980b868725387e98483f77bd3b4aa30d03f38f441969c691"}], "guid": "bfdfe7dc352907fc980b868725387e98cb80df206dfed8819d3f399a031cc94e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e985665c6aed7a1377f80cd40d840c09f16", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e987b7a99df146372db53eff8800f955793", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}