{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ecc5c4a6c8a7db24b519711faecddb0a", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/TOCropViewController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "TOCropViewController", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/ResourceBundle-TOCropViewControllerBundle-TOCropViewController-Info.plist", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "TOCropViewControllerBundle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e982ca93da4b03d6a70fd7ad946e09a82ad", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d5e553e7ebe30f82e0936dca8ba72d46", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/TOCropViewController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "TOCropViewController", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/ResourceBundle-TOCropViewControllerBundle-TOCropViewController-Info.plist", "PRODUCT_NAME": "TOCropViewControllerBundle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9878ee75e5624ab32624989152d06691b5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d5e553e7ebe30f82e0936dca8ba72d46", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/TOCropViewController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "TOCropViewController", "INFOPLIST_FILE": "Target Support Files/TOCropViewController/ResourceBundle-TOCropViewControllerBundle-TOCropViewController-Info.plist", "PRODUCT_NAME": "TOCropViewControllerBundle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98c19afcbe63a279c7089768c5db523b0d", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e986d1018a4fd6590097a843809fbf2e29c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98b3e02daec2ebe6235a981d08bea56510", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d6a186ddf9105d18d723ba3c0c64827c", "guid": "bfdfe7dc352907fc980b868725387e98a3abc097732becaac36a62cd84775a38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983be017b33ead40797f2ad14e9b82804f", "guid": "bfdfe7dc352907fc980b868725387e98c7fc9f6c481db8723e7e4f6cfa0daeb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abce5d77c4ad68b9e8220452e807026d", "guid": "bfdfe7dc352907fc980b868725387e9817a2215ce4b5ef86006bf43d5870cfe2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd44af099273f1e2c9bf95b07062af39", "guid": "bfdfe7dc352907fc980b868725387e989b2984482fbac9f7bac6768cc5d9173b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98471e6a8ced38d436edab73d1a21f20c8", "guid": "bfdfe7dc352907fc980b868725387e98f7329f7e735b9053ae10365556dc0829"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809663f5e78e7da88ef838a28cb5be563", "guid": "bfdfe7dc352907fc980b868725387e98d97d128590b086af48837ee1e46fccad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870f9529456391e9db604976b99e09ce3", "guid": "bfdfe7dc352907fc980b868725387e98dfaecc168053728d8b1e0d05b0a4ad4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858fa0ed16a290b03ae1fb649a74db6c6", "guid": "bfdfe7dc352907fc980b868725387e98f17541664992dc758acb8ea59223ec0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a2ecbf0727ca1f314516f1c86f25ecb", "guid": "bfdfe7dc352907fc980b868725387e9868a7e5ef7d20e5b8d53874bd2ce39d29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c1558b4c09e667e3bda8f5d633b50e8", "guid": "bfdfe7dc352907fc980b868725387e98a07fa343cba8219b3750758a5095ad83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d384021ffd900a1cc85a6dd5a7d7e14", "guid": "bfdfe7dc352907fc980b868725387e980786835d6240257561af286a38e2dbff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c729ee3ae308e4e783e0c60bfa4c390", "guid": "bfdfe7dc352907fc980b868725387e981dd69fa90a8771f7108e668bb4376653"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811fb7e6b7e76214e5b091e909ffb3ef0", "guid": "bfdfe7dc352907fc980b868725387e98f3d13dff6e0a5efe0bec233582e2f3d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fb4f1d609e9ce162b79536a9730c510", "guid": "bfdfe7dc352907fc980b868725387e985a5d547c395a5e058d501762095c5393"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e05c4f4a6c9581ce3e87d3f53ea03314", "guid": "bfdfe7dc352907fc980b868725387e980be71d734a0fcd2b99de241acb0ed13a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983261ceaca9ca7f3123d0cd44e9fcacef", "guid": "bfdfe7dc352907fc980b868725387e98636940de849201cdba6c4856f9821ee8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dd07d31635c2b74002eccdc7fb08046", "guid": "bfdfe7dc352907fc980b868725387e9863551e00f3d723e4019bf10ccedabd18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f40a9aa8d106560576a8c89b0b44b43", "guid": "bfdfe7dc352907fc980b868725387e98a0f90519b0f61db804ca4751e63b4d3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98730507840da1ed5216db0a5cbc498535", "guid": "bfdfe7dc352907fc980b868725387e989fe337c637834d1fdbd6ac5514b12efd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835c9670beb52abaced4d31e08f36438a", "guid": "bfdfe7dc352907fc980b868725387e9890d80eb650b32d611a5ac6ca3f3552c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989380dc66f4ef07214811b78b6bc0e36e", "guid": "bfdfe7dc352907fc980b868725387e987e1168d1b581575008dc5aa846c6498b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f15ebe40c67104ed60cf7d309f5a0391", "guid": "bfdfe7dc352907fc980b868725387e98bc2b5f4c0cbe0ede37d9315291c96db1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888fea0451ab8c3f003b4bec72429e0b6", "guid": "bfdfe7dc352907fc980b868725387e98e4284c9100963029795739588014b1dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98726779f1c4fe3870399d450ca3062120", "guid": "bfdfe7dc352907fc980b868725387e98daf380803b4fa57bd2829bf6d67b9d71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0f95ec4c80ca701e45f02806f0338cb", "guid": "bfdfe7dc352907fc980b868725387e98282f9e3fb5b9ce7fb85ca2a874d5b765"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987aac2eafcac1eb974cc0455d214b2682", "guid": "bfdfe7dc352907fc980b868725387e984bb01f5a83a367ed9c41d9df3b2fc410"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986888b24ebd4da8bb891284af58bc460f", "guid": "bfdfe7dc352907fc980b868725387e987e3e6241433887030db4a7c99cb04f19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879d42d837cc4e546897b730319acb3e3", "guid": "bfdfe7dc352907fc980b868725387e9899cdc095e73baae7684bed7317b9fa35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98178f8828b647331d24fdc9eb9f5258a7", "guid": "bfdfe7dc352907fc980b868725387e98c939b99356d95d982e988c1c0f5e9892"}], "guid": "bfdfe7dc352907fc980b868725387e9898b3e934467fd6d2e48791cd6d9dcb78", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e986dbfa2df59ddcae0f992dedaee8f3553", "name": "TOCropViewController-TOCropViewControllerBundle", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980e69b2358d36eb6c2616a1dbbe45f585", "name": "TOCropViewControllerBundle.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}