{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987660036f6d1b69847b098ad89cfb5b6a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986c69b2ed06dccbd4f4692d674d5642ec", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9859510259f5a34f4c3081f398c5626e99", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9890956442d81b806461d723d3c8e3ff73", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9859510259f5a34f4c3081f398c5626e99", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c26dcd335c1e461ce67fbbaccf14be34", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9835873ccd9de8ac860af3a4f258948ba7", "guid": "bfdfe7dc352907fc980b868725387e988a350578d04e2bee425b0c05780d5802", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989030d11434878e5294809e7a86e6da78", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a0f4c95d179d4c32427773927d393452", "guid": "bfdfe7dc352907fc980b868725387e98a0a4b95d2b1dd232f5fc16071ca46440"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885ba891d3f9b7098d237d19de5cca50d", "guid": "bfdfe7dc352907fc980b868725387e980409f9f371213829a33e032790d65b3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ce1fc21eebeb0380bdd9c16f5debbe1", "guid": "bfdfe7dc352907fc980b868725387e98fc8d9d95e2eb02d0ef9f45f47369dba7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823d3f71f78280091c468967dc78546f4", "guid": "bfdfe7dc352907fc980b868725387e98038b7d8d5424bc070bd1d7d724798d58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee23021857d4d5025889a7ca8f2c3188", "guid": "bfdfe7dc352907fc980b868725387e98c71c200476d36026ce39c9593a70326e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98520646cff5c0220b9e12476d182e11ae", "guid": "bfdfe7dc352907fc980b868725387e98ceaf7cb8cb3c9e6ee480b7e205b0fed8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898bee6d92737d27e4a8394e2dd1f3ae2", "guid": "bfdfe7dc352907fc980b868725387e986b762e2789472ffd4d42d95e1b7d7724"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db871427cc7b35b4837e2612eeb91e8c", "guid": "bfdfe7dc352907fc980b868725387e98760611924c6135bd128e54b531fb5884"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873556c9076801dd10cff181511cdbe6c", "guid": "bfdfe7dc352907fc980b868725387e983a7e6eb5184c68c385ff217269c680f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b39b2c88998045769c85ea8d616194b9", "guid": "bfdfe7dc352907fc980b868725387e982bbbb113db25728b95b5ff5325e83ec2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c97472c2c2510a02d0729c943f5f96a", "guid": "bfdfe7dc352907fc980b868725387e9885b13b3036fb61a80099a60a105aac8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf76764d806cf8b6e941cab4cae742b6", "guid": "bfdfe7dc352907fc980b868725387e98d3d4c332b86b44560b6554e59a63d3fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d588b11d1643defeb5715d6221b6025", "guid": "bfdfe7dc352907fc980b868725387e98e2d66090a15d51807e61f8f75aa8d3bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f60cdde7aeff36225c650f333bd14cc", "guid": "bfdfe7dc352907fc980b868725387e9824526b4a7ca2852b94b8e1b5c7c7d56e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b14f6fece5b092a78a0864243cfa33ba", "guid": "bfdfe7dc352907fc980b868725387e98ee8d0303fcc5e7e416fa2d62c61a707c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a79cd28cc9e3ff3f9cb6827ae87d7234", "guid": "bfdfe7dc352907fc980b868725387e98232e5cdff1542b948adef52449f54dca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a44aedb8be87505b3f0e8b50c585198", "guid": "bfdfe7dc352907fc980b868725387e9886df461e4a98d8d6ad0bf48f87c51357"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98062dafb8d75020f0cd1681aa2903c532", "guid": "bfdfe7dc352907fc980b868725387e98990347dfde0768cb0b37f3b86ad21c96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b51611fd371a2a8d0d54bffff65b38a2", "guid": "bfdfe7dc352907fc980b868725387e98249b8556fc08a02d29c417aecf84bd28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98845cf7a1f283678301177933adf7b641", "guid": "bfdfe7dc352907fc980b868725387e98f787aafd3a931dd2d1589325e3542995"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980709b89677f0f124efa34059b2ac4361", "guid": "bfdfe7dc352907fc980b868725387e9827b0ea851541b07cfb2b84d9274ab994"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98267a0ae135bb5f35c2859b5f9decf3b6", "guid": "bfdfe7dc352907fc980b868725387e98879ba207741836152988de40861ab23a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822fe6e09fab08b39aafbac0c71ad2e8d", "guid": "bfdfe7dc352907fc980b868725387e980775a3500637579acd8076824fd2844b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ff57d1f028709d990283bf16a9d950f", "guid": "bfdfe7dc352907fc980b868725387e98f0c64a1a231322896ef38d6b52c1a722"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989eb53674be52ad360abafbcee5f3fea5", "guid": "bfdfe7dc352907fc980b868725387e98f9cbf8b78424857189b5d13f80545052"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b610051712a99e3c914ce121eadc8279", "guid": "bfdfe7dc352907fc980b868725387e98e48b09eee55b1dad1ed8e69d76239a7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98868f9d9d863bc546f8b5130479def97a", "guid": "bfdfe7dc352907fc980b868725387e9826cdb43146d20df9c7a639d6ab2124e6"}], "guid": "bfdfe7dc352907fc980b868725387e989b474164b701d61dd12be41c760df80e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f7d6397f1db071eca73cbdebfc7631f6", "guid": "bfdfe7dc352907fc980b868725387e98cdf2b905c33e89023a42c65b5a2e6ba9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98865ad60e69c6d8afff9d88cf91491376", "guid": "bfdfe7dc352907fc980b868725387e982e27871a65cde3d153734bf5ba7e7f9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839d638a8f2faf23e558185f2272427fb", "guid": "bfdfe7dc352907fc980b868725387e98771ca22dbc3815306880f0181af01315"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c8b9502700317dbda9db1039796d400", "guid": "bfdfe7dc352907fc980b868725387e98f2e558d5ad0a2a4cb78d84f297f6bb9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2688dd06c00e6fc8953aa2042df31e1", "guid": "bfdfe7dc352907fc980b868725387e9853ee98c854c980a8e069b5908e173787"}], "guid": "bfdfe7dc352907fc980b868725387e98272aeb37927437165b2a1fc13f9edeee", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9888c97b9648264a3810c780ce20eb3781", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e98917720607e4660b489332dfa136f1b21", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}