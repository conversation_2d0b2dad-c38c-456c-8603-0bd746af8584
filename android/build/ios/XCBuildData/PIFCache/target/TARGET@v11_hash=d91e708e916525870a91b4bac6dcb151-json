{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981d77af40dd8e70999e5f66ce12ca83ee", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980d3926596ad3cd9c175f9e28b372b54b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f5a94c6c42043316fe02f7b451b70e22", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98288f36904c064e8ffee3307684f0adf4", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f5a94c6c42043316fe02f7b451b70e22", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ad21424d04da79e5b6b09fe13bb9d64", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982000c6fbd50fc6d66369ac38587bf374", "guid": "bfdfe7dc352907fc980b868725387e98fdd60d0f664135d2bcbe45feaa2bdb28", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec1bef1ed0a8c91c7f0f351701b72ee0", "guid": "bfdfe7dc352907fc980b868725387e982b43eb4dc4862317b942fbc5ed879d2d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d06bf622cb4c2a2827697d7a3e6b12ba", "guid": "bfdfe7dc352907fc980b868725387e9859cfa3b771f3a5a81ac0e192651c59ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985069b6ed9bcd50fefd111d509e7db7ae", "guid": "bfdfe7dc352907fc980b868725387e987654fdc4242c10774aea5ca6fba0cea3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862cae7e58d6dd27244be362f8d4551b7", "guid": "bfdfe7dc352907fc980b868725387e987517fd384f0f14c5edb189fea17fac74", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d850eac28c355c33b3186b304154a4e", "guid": "bfdfe7dc352907fc980b868725387e98a5557fae9f5b893e1e3519a66f40f44a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1250da5df55f0591225e8aaf4794cf9", "guid": "bfdfe7dc352907fc980b868725387e98066a477d78922083e84e89a060893a0e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b7d53cc8c5504ef991bac63348492e9", "guid": "bfdfe7dc352907fc980b868725387e982ee6eccbd7530e9dfbe6af31fab74ac3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aadded386bcae802cad35fd12772b789", "guid": "bfdfe7dc352907fc980b868725387e98632a13842362049e00d756506a50fb54", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f94266a7d58d51279a6c221a1718e0d", "guid": "bfdfe7dc352907fc980b868725387e9869472e4abf613663b3a8a3bcc618fae4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa81132318a2c4a7e9ead45a8a7fe40e", "guid": "bfdfe7dc352907fc980b868725387e983feee9c6cf8f1c241bfa04f74a24e8a4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fc70eecf5d901488023748c61768442", "guid": "bfdfe7dc352907fc980b868725387e98f1eb8e0924859f0b3a3062e258d912d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880972ab7eb6b9f5f52ee635c5748a646", "guid": "bfdfe7dc352907fc980b868725387e988f60df2ce0d0ed0c54b7c6dd1afeaf80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b37a1a85fa45c6893e3206682d92cbb2", "guid": "bfdfe7dc352907fc980b868725387e988ea922b2c2aa3866c17dec1b6e547764", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850957e0aa134d458020c7baa025e9b84", "guid": "bfdfe7dc352907fc980b868725387e98000059bb5117743e983e30734692f79e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e9237d1f0e54d8d754e2d02cfc82cd6", "guid": "bfdfe7dc352907fc980b868725387e98e3a2e43c3cd650db9d251daf669c5bd0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98887d2199d103c93f8f11c40d8fe8a2e8", "guid": "bfdfe7dc352907fc980b868725387e981943d8276f2c0cd571bd0f0ce70b3ec8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b1b8a95be940c1f01246bc3b7d7d7cf", "guid": "bfdfe7dc352907fc980b868725387e98f425f3d083fc7b072230cc77d4a54d7b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98347fae2f18c1bd32451ab506a31a32b1", "guid": "bfdfe7dc352907fc980b868725387e9864cca987cf38a33a5d1871f0a28f03c4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980adaa9729213c327e2af306b48cad902", "guid": "bfdfe7dc352907fc980b868725387e98b00290961bb0a7339eb1ffaf264dd1e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b9d149c95634333a2a003018a0cc3d3", "guid": "bfdfe7dc352907fc980b868725387e983203289e9d016f9f4b4325037a4e71ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c16faf7b7835d46de0747a53d04dd108", "guid": "bfdfe7dc352907fc980b868725387e9855b4dc629f946d8ab71d8bb88982beb2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899ca3693e39607f30d331d119d463348", "guid": "bfdfe7dc352907fc980b868725387e987fa99523622033a86d3abce5d5794ecb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98256e510c1d6aea4b3c515711804f0433", "guid": "bfdfe7dc352907fc980b868725387e98092782bef84f333a33de27f5d57ab915", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981664dfc7329363764fdacd5d07a0c8e8", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989cf685af19c551f9f430f2f259317e00", "guid": "bfdfe7dc352907fc980b868725387e98dbd847e2ace5139b27466c62715e4015"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98961bf6523719753b6a8a75dd4b9ff30b", "guid": "bfdfe7dc352907fc980b868725387e98bfc970a823ac21b98354b9b1571f5b81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad548a8f80aa00ca22545c05c090fb9d", "guid": "bfdfe7dc352907fc980b868725387e981b3006abe64ebbdbdc23d341fd2013da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d62724b30193870d91a1dd4515dd842f", "guid": "bfdfe7dc352907fc980b868725387e98272b43eb182ba6782418f847be95f0fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d46e37ba41290d17df954ae71d27150e", "guid": "bfdfe7dc352907fc980b868725387e983aace035b3631636e452bb8e343ff745"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872534c087c0f0d30ec607cf66800a871", "guid": "bfdfe7dc352907fc980b868725387e98b50904c074e225db0463ca5242265375"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8ea7a864869af7fb434b03ebac4e479", "guid": "bfdfe7dc352907fc980b868725387e983578e6fbe67d4763f12faeffbb718ef9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98922561408c03f9a1a6ad9b0d8fa19e96", "guid": "bfdfe7dc352907fc980b868725387e9879fdfe5431355aa38cbbf8c0a1728460"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f2a53577dad67b11181cff13a47618f", "guid": "bfdfe7dc352907fc980b868725387e985aba136aa668a7271f66a1d090630b2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98214590fb9ea6ae7bf09ee1ec06b56bae", "guid": "bfdfe7dc352907fc980b868725387e984ddfdb2045dff1c4569b679d3d09b617"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e82c04b517c8db6b4efa7240e6289ed", "guid": "bfdfe7dc352907fc980b868725387e986c54e9f3f65fae4237ba44418a78ba71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd3924beb9477bdd30f90db7e54f3b26", "guid": "bfdfe7dc352907fc980b868725387e98217f80265a58ebf10371bd168c837bcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8582455fb4efc2835f4e8b8e2828f03", "guid": "bfdfe7dc352907fc980b868725387e98db1a716f5e2aa7c1bec0409ec1930def"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2158fd32fe3d513459a4a4b76d7a73e", "guid": "bfdfe7dc352907fc980b868725387e980b3e727958a9ccc9b974efb86c0e3bd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988da7470cd47194ac3dee77ea59f38f3e", "guid": "bfdfe7dc352907fc980b868725387e9817e7142a8b3397a7b2a9cfbea5a02704"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cebb2ce5a389cf38a616c3edec61f81d", "guid": "bfdfe7dc352907fc980b868725387e987f1e452434ea0e0cb927e5702cf445ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc9a53b9bff822423ccc415ad95b4ee7", "guid": "bfdfe7dc352907fc980b868725387e98389774e7604c87c9d631500cbd5db1d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98371f9fbf841c53ac7af0df462246b545", "guid": "bfdfe7dc352907fc980b868725387e98401a2d2c59277fb92bf9bdc2e46482c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d37801c4b649b96af0d6edaee2202d6e", "guid": "bfdfe7dc352907fc980b868725387e985fcdd0ec44f159f1c7f3d05a03d8ec33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d65d5d05a0f6e26487152683f354ab3", "guid": "bfdfe7dc352907fc980b868725387e98a3cdda96a3819ebfbb6ad48c5b5732d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98267edd5aea5d37b92817a8af6af8e282", "guid": "bfdfe7dc352907fc980b868725387e980c249dad96a03a034f9e80900ec21d6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4632c83d7b1630613d8f888235d49bb", "guid": "bfdfe7dc352907fc980b868725387e98e7887e9cf16c104235c47ade5443867f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b098a446efd79e3235721329bb884d89", "guid": "bfdfe7dc352907fc980b868725387e9830d50d3b8dada408fbd5ac3ca1c576d1"}], "guid": "bfdfe7dc352907fc980b868725387e98339f3c5c18cf261c37731f1ff9cd3c6b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9839d638a8f2faf23e558185f2272427fb", "guid": "bfdfe7dc352907fc980b868725387e98f76f428393514513ee220ee3cd45b4b7"}], "guid": "bfdfe7dc352907fc980b868725387e98cf9e1d75d6fd474380329cbf47d24d45", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98911b19d9ae38fc82e6a73bfd4cf29ba7", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e98f5ee52bb1ad32275d40a0a37ff2d7e9c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}