{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981a634bdf8795bc095d3a34509d14c21a", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b2f4b25a8d90636f47ae32244238e77c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9872ef09a85ed94a2ef64d0fe0a6f2b725", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984e4d86d70890c21ed16cf87923a2cf2b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9872ef09a85ed94a2ef64d0fe0a6f2b725", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987b7fb43e5cb40c6c38d1a3e45c7031a6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9874213b1c93327561fe74eb223ae1ce2d", "guid": "bfdfe7dc352907fc980b868725387e981f6cc9ba376564cc4e242af7b0b45a32", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfcdb037566b043507c3747c29291875", "guid": "bfdfe7dc352907fc980b868725387e983ffa8cb1763161c9e979cda397938e17", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ea506131cab551cdf73ef4d1bd474a2", "guid": "bfdfe7dc352907fc980b868725387e98d2405f3b89fba3d620c6b99fdec7cba7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b6386a608a314a536e0160c08fbd95e", "guid": "bfdfe7dc352907fc980b868725387e98dd9a6f15ec8c974b4f1375850e6544a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b12907ffded9338edfc0a049009eb18", "guid": "bfdfe7dc352907fc980b868725387e981982dce0595d07b4ce08c5cbdaac13ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98010c91b048f829cd67f8e8d914cd9956", "guid": "bfdfe7dc352907fc980b868725387e98caff27028902c9d6b06d8bae86e934b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b3c0e4d4718e9516455fb7d872bb9fe", "guid": "bfdfe7dc352907fc980b868725387e986b626a4ef2a8ef8fa22b533b334138fc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d874c664901c8f483294856db45c5b54", "guid": "bfdfe7dc352907fc980b868725387e987a065af2610f73e182d8aef95321d80e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818885b5ca5b124a7adb44efb47570b4e", "guid": "bfdfe7dc352907fc980b868725387e9833250aa97c6950a6a95e41b052268137", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de0c42dbde3d2ca355195e8a545630e8", "guid": "bfdfe7dc352907fc980b868725387e9844cd995050169df79e6bb415df88b1ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986df58a58873a96484892c4353e7207ec", "guid": "bfdfe7dc352907fc980b868725387e9800134652db4a14c9b54a60775d3cd766", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9d7dd99f75667164fac0c0c59a2d768", "guid": "bfdfe7dc352907fc980b868725387e981dbdba46066d382822ed87363b2508a2", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b1c6e0dac1c0f3288cdfd038dfd59299", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9879ff65a34a4b3cd8ea1ca0dbf55078a1", "guid": "bfdfe7dc352907fc980b868725387e987ec5d3a7027491b0853202db00b1f5d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875506485d4b50461419e1f0e74957521", "guid": "bfdfe7dc352907fc980b868725387e9849836eca253a3d81d47ec37b68b493ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983885cc824ea020e561059f2a7d282152", "guid": "bfdfe7dc352907fc980b868725387e98252c126dacc5ff494aecd2ce735c45d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8383845c66bcad44aa87cefd64cae76", "guid": "bfdfe7dc352907fc980b868725387e980fcd9ee71d497e180aaddba0459520e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d3503f1dbc8cb29820e0ea90f3383d4", "guid": "bfdfe7dc352907fc980b868725387e9815cfa6f8be316fd779227d8158cf27ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891501ceb33813b3306bc5333dcffd561", "guid": "bfdfe7dc352907fc980b868725387e9801c6cc1227cfade99ed6d5e2199542f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815660a6f6970defa158438043876768c", "guid": "bfdfe7dc352907fc980b868725387e98fae72231ecec79976bb0e42e803fbd30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d4771c943b11afff10ed322b17dbb88", "guid": "bfdfe7dc352907fc980b868725387e98b48d635eb12a83e92dded002cc742c7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af623232ba4c895942cdbeb2066ab99b", "guid": "bfdfe7dc352907fc980b868725387e9854aeea76dd39e7d7289716b838772749"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a0465a43e8588d42551b351c25d5026", "guid": "bfdfe7dc352907fc980b868725387e9840bc7d43bdb5608ee4d14d90ffd3f980"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98356eb4ead95f308896ab66b102946273", "guid": "bfdfe7dc352907fc980b868725387e984666650d7b13fa61edc18a0bbc64724d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0aa8870c15240d68ee72b5d0348723b", "guid": "bfdfe7dc352907fc980b868725387e989ec6c056940ceac336198585f710d943"}], "guid": "bfdfe7dc352907fc980b868725387e984828d07df21b03575280a435412b2bd2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9839d638a8f2faf23e558185f2272427fb", "guid": "bfdfe7dc352907fc980b868725387e984dff9b9164ea7278ab55766135fcbf91"}], "guid": "bfdfe7dc352907fc980b868725387e98e02b84aa74e21e5d0824e135f776cc74", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9898f5fd0d63861b94769f137ac6982066", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98f1ed40c0d488e7d17a2574221f5de571", "name": "just_audio", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f09d8f416b4b8830579237c0f3add196", "name": "just_audio.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}