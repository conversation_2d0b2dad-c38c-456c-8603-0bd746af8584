{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9807fc9e9e4b9fa41c63be97d4e9ac69b6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ab3f3b709c336bc0db73b96f31eeb82d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986191aa6b47cab6f8046d65f206e0bf34", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bd779e49dc571ee169793df70b043568", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986191aa6b47cab6f8046d65f206e0bf34", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98adf42fa88f13425d1e39eb4ba77fc066", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98442bb03322b23268006693a9c4bf9597", "guid": "bfdfe7dc352907fc980b868725387e98d3d903e4f6a32ebdbb419987bf6373c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a1f0edeec94f7d22039865e54e6da77", "guid": "bfdfe7dc352907fc980b868725387e988467d2a56c4e9a4be8ec61eb443f88b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d581449ca35d3728ad40c98140003532", "guid": "bfdfe7dc352907fc980b868725387e9856188c1326976b27540eed1872fe62db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4dd5f84b8cee46c8cb22e5d1d9581ce", "guid": "bfdfe7dc352907fc980b868725387e9851e8c7dc9151c7fcd20cb1d9d7c0444e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8b34427832fb9c12a8ccd4a96403609", "guid": "bfdfe7dc352907fc980b868725387e98703ee22a1b24774f8f0594a2a7894fbb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dd2f279baadf221743ba7efe06e1512", "guid": "bfdfe7dc352907fc980b868725387e980a2616209ab8314ddfe0cc43abcd0f49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854c2b3143fc21995087f757b25f2be83", "guid": "bfdfe7dc352907fc980b868725387e9871a55f1cb7cfa68cab36163d9efc3994", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8732235f3121f6c4aa1096c26be44fa", "guid": "bfdfe7dc352907fc980b868725387e98b15288d1e4924ea1ccabb2d07f757ab3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ff418878bd262816bde7d9f26948fef", "guid": "bfdfe7dc352907fc980b868725387e9880fbf0b1252188f7f0a9c1b64f07267f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98133125bc31bd91d4d26cb312bd2f84f7", "guid": "bfdfe7dc352907fc980b868725387e98521f43089b09d5b856c310f9007b3d35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98233d148308f119ae8c03809c94892708", "guid": "bfdfe7dc352907fc980b868725387e986d05e3ac4ee4dce85c8c31255336748e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987aeb7a2692628e1a66a7299054733a34", "guid": "bfdfe7dc352907fc980b868725387e9861f619815cc7ed45eb366c3f00394e8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833fc6f77d14fbb7ced05af5131822688", "guid": "bfdfe7dc352907fc980b868725387e988d66269823fa93c66c36936059682173", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98459e09ff769b313c5e1a64e8fb0c7666", "guid": "bfdfe7dc352907fc980b868725387e983f3a8523b76136812abc893b90024aa4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98103daeda0daa0b8198ae43f3488a65ae", "guid": "bfdfe7dc352907fc980b868725387e98d6512e190dc9b1b30764ad3a43aa0e06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4b7b30b55b909dd428bbbf048a32333", "guid": "bfdfe7dc352907fc980b868725387e9861b5b9bc9de76fea292f052b2a2a1c56", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d2aec81b04d7a0c91d7b49b29945d1a", "guid": "bfdfe7dc352907fc980b868725387e986463fff500d0161ff0879000ae30f848"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fac0a94c9fd9a1bc6e430a5c6cc85e30", "guid": "bfdfe7dc352907fc980b868725387e98723cc75c7cdc9bdb42ae2abfa05a3e7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc8826aa72b865fec9a453da4e426f3a", "guid": "bfdfe7dc352907fc980b868725387e98245dc0054b08cf3085c84caf2a1cc0c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6e67e254195c33c69d8da1c490170b4", "guid": "bfdfe7dc352907fc980b868725387e9801a44f817483eae6e4ea3b07cf69fb90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af83c2f1fb6eb8b44f2168b4bfc44613", "guid": "bfdfe7dc352907fc980b868725387e983fae5103565730a205450a1a4f34f43d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840a42773d3c5f9faf37c58e9ff51fd57", "guid": "bfdfe7dc352907fc980b868725387e9829c576dff272000d786dda317449ecf6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f70b523c8f2524fba8d0cdf780a3598a", "guid": "bfdfe7dc352907fc980b868725387e98c6876b9e1f5683b3bc97d36d342d6360"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983aaade1d0406809144043f9225b75e70", "guid": "bfdfe7dc352907fc980b868725387e98564b800d2260878fdc48b1ecaefaca42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e5d470527d19bf3222694505d4bca11", "guid": "bfdfe7dc352907fc980b868725387e982fc5f1fcf53a55e73c38201221480a2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a422ddb223d91122fafc42a8b0be8547", "guid": "bfdfe7dc352907fc980b868725387e98bf43332840d6127379aa388fe6d6c3e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db0b0b39380307b4a52fe07015eaa301", "guid": "bfdfe7dc352907fc980b868725387e980f366ad686d9998f8a1b40e6da2f2057"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab3d3ceed68afa2e1245ea39759d613e", "guid": "bfdfe7dc352907fc980b868725387e984185ce48942f9b08cdb740e79c2d7fc7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd63ee82dfdd5c8b553b092f535e3b14", "guid": "bfdfe7dc352907fc980b868725387e98ca1169c58c12e99efa63698e3bfdf951"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a64f0a777a366295ba80679b9d44084", "guid": "bfdfe7dc352907fc980b868725387e9882c99960eec317492f2f919a3ba1d630"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98581d73879661fea4a063e494941a30fd", "guid": "bfdfe7dc352907fc980b868725387e98877f56e42e224a1c3451a7a76bdaed3b"}], "guid": "bfdfe7dc352907fc980b868725387e9811dd11a25c3869ea702e4b721ecd7399", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982aea34e47bd0aa2d126581c1e42246a2", "guid": "bfdfe7dc352907fc980b868725387e9892baf435abc0bc9dc64ab3ea28b4f931"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b16de7edda31a87b02e3655150ffc0b1", "guid": "bfdfe7dc352907fc980b868725387e98697f663c29491fdf04af6ffd45ed56b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d4ba9d71b38258b8552aab2e7b95a4e", "guid": "bfdfe7dc352907fc980b868725387e98699cfeb30d0a0cff8bc9ad2e878f51f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b997529dad9140336c963f9a41fb645", "guid": "bfdfe7dc352907fc980b868725387e9897a3ec66032f4b2a58ce191fe87d7996"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7b8692e1cfc48e0dc3c930bed6da2d1", "guid": "bfdfe7dc352907fc980b868725387e988d669ea7a337d22258f42a059410cc40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e8a645f81169cbe4ffbdcfc4c576ea5", "guid": "bfdfe7dc352907fc980b868725387e987132aa0f310f21f91e0dfcc2692db4d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb949498c29991824a8e72cc75d7d3ad", "guid": "bfdfe7dc352907fc980b868725387e981677752eb46747b51758764f01f77c98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2c2b686866b29a55962b13ab133d955", "guid": "bfdfe7dc352907fc980b868725387e986e1383aa83470808b7d2eb8329aa149b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d4252c9cd33705afa267e407850f373", "guid": "bfdfe7dc352907fc980b868725387e9876763e230174b46f3c46713778ae6849"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a454330606d733b92c52668d4f50848e", "guid": "bfdfe7dc352907fc980b868725387e985b690c43771b5c2c685b67ac164c904a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d198bd4ac91c4e9f8e510c60b579ff54", "guid": "bfdfe7dc352907fc980b868725387e98151d7701081743acc2ad422c21044805"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e44577739dbe332a9e725a1299097cc", "guid": "bfdfe7dc352907fc980b868725387e986da9a2d83daa370602661cced177911d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989098ad83e1427b12da77ae7189f42731", "guid": "bfdfe7dc352907fc980b868725387e984890b4e4e6052d4cfc3b0b65e7c8e539"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2e0f9f235ab5f2f90a3eef85214479b", "guid": "bfdfe7dc352907fc980b868725387e988fd870c74c163396cace8e175ca71548"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988756e93055142952c5e9f41dffb914c0", "guid": "bfdfe7dc352907fc980b868725387e98943cc9cfcaf8f7562a617fbcd0425e66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879f60d8372a88a7c8c3f1cef716ea2d1", "guid": "bfdfe7dc352907fc980b868725387e9804cceee259abce59768b9fb8108c1d28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3739c97b8fc36e499593d6b59cf3bb5", "guid": "bfdfe7dc352907fc980b868725387e981ac27cdb665bd688b5b111e1239c9116"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ea990a90e44bb524444dc525a31552a", "guid": "bfdfe7dc352907fc980b868725387e98849f9d69a3c3a240416a1321269c0543"}], "guid": "bfdfe7dc352907fc980b868725387e9802d648d76f34df619e586e516a3164cb", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9839d638a8f2faf23e558185f2272427fb", "guid": "bfdfe7dc352907fc980b868725387e98ebb6b3c0e106e16b8ee8210e4247955a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d31edecd71336b583e35a476ca5d7e6c", "guid": "bfdfe7dc352907fc980b868725387e98267d9e97b059077c6f987b107996369d"}], "guid": "bfdfe7dc352907fc980b868725387e98ceafc777d4d033966b81f08975e9ee86", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98628e6b21559d087b9538ade42dad350a", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e982aad72eb347fb3dae6547bb5505aac99", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}