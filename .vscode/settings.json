{"dart.flutterSdkPath": "", "dart.sdkPath": "", "dart.debugExternalPackageLibraries": false, "dart.debugSdkLibraries": false, "dart.previewFlutterUiGuides": true, "dart.previewFlutterUiGuidesCustomTracking": true, "dart.flutterOutline": true, "dart.closingLabels": true, "editor.formatOnSave": true, "editor.formatOnType": true, "editor.rulers": [80], "editor.selectionHighlight": false, "editor.suggest.snippetsPreventQuickSuggestions": false, "editor.suggestSelection": "first", "editor.tabCompletion": "onlySnippets", "editor.wordBasedSuggestions": "off", "files.associations": {"*.dart": "dart"}, "dart.enableSdkFormatter": true, "dart.lineLength": 80, "dart.showTodos": true, "dart.runPubGetOnPubspecChanges": true, "dart.warnWhenEditingFilesOutsideWorkspace": true}