# VS Code Debug Troubleshooting Guide

## Issue: Command 'workbench.action.debug.run' not found

This error typically occurs when the Flutter/Dart extension is not properly loaded or configured in VS Code.

## Solutions to Try:

### 1. Reload VS Code Window
- Press `Cmd+Shift+P` (macOS) to open Command Palette
- Type "Developer: Reload Window" and press Enter
- This will reload VS Code and reinitialize all extensions

### 2. Check Flutter/Dart Extension
- Go to Extensions (Cmd+Shift+X)
- Search for "Flutter" and "Dart"
- Ensure both extensions are installed and enabled
- If installed, try disabling and re-enabling them

### 3. Restart Dart Analysis Server
- Press `Cmd+Shift+P` to open Command Palette
- Type "Dart: Restart Analysis Server" and press Enter
- Wait for the analysis to complete

### 4. Alternative Ways to Run Flutter App

#### Using Command Palette:
- Press `Cmd+Shift+P`
- Type "Flutter: Launch Emulator" or "Flutter: Run Flutter Doctor"
- Then type "Flutter: Run Flutter App"

#### Using Terminal:
- Open Terminal in VS Code (Ctrl+`)
- Run: `flutter run -d chrome` for web
- Run: `flutter run` for mobile (if emulator is running)

#### Using Tasks:
- Press `Cmd+Shift+P`
- Type "Tasks: Run Task"
- Select "flutter: run (web)" or "flutter: run"

### 5. Check Flutter SDK Configuration
- Press `Cmd+Shift+P`
- Type "Flutter: Change SDK"
- Ensure the correct Flutter SDK path is selected

### 6. Clean and Rebuild
```bash
flutter clean
flutter pub get
flutter run -d chrome
```

### 7. Reset VS Code Settings (Last Resort)
- Close VS Code
- Delete `.vscode` folder in your project
- Reopen VS Code and let it recreate the configuration

## Updated Configuration Files

The following files have been updated with better configurations:
- `.vscode/settings.json` - Enhanced Dart/Flutter settings
- `.vscode/tasks.json` - Added Flutter run tasks
- `.vscode/launch.json` - Debug configurations

## Quick Test

To verify everything works, try running:
```bash
flutter doctor
flutter run -d chrome --web-port 8080
```

If the app runs successfully in the terminal, the issue is specifically with VS Code's debug integration.