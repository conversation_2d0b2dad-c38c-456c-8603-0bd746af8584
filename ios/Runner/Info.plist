<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>soho souk</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleLocalizations</key>
	<array>
		<string>en</string>
		<string>ar</string>
	</array>
	<key>FirebaseAppDelegateProxyEnabled</key>
	<false/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>CFBundleURLTypes</key>
	<array>
		<!-- Apple Sign-In URL Scheme -->
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.apple.signin</string>
			</array>
		</dict>
		<!-- Google Sign-In URL Scheme -->
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.62532318057-aqa6te1n4qhlpc0vti65bcqdjc80hlsd</string>
			</array>
		</dict>
	</array>

	<key>CFBundleName</key>
	<string>SOHO SOUK</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>sohosouk.com</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>sohosouk</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>FlutterDeepLinkingEnabled</key>
	<true/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>googlemaps</string>
		<string>http</string>
		<string>https</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This allows SOHO SOUK to access your photo library for uploading images to ads and saving photos to your camera roll.</string>
	<key>NSCameraUsageDescription</key>
	<string>This allows SOHO SOUK to access the camera for capturing photos or videos and using them in your ads.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>This app requires microphone access to support audio features.</string>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
</dict>
</plist>
