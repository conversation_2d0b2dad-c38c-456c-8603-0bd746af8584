import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:soho_souk/home_tabs/profile_tab/profile_tab_widget.dart';
import 'package:soho_souk/pages/choose_categories_page/choose_child_categories_page_widget.dart';
import 'package:soho_souk/pages/choose_categories_page/choose_second_child_category.dart';
import '../classified_app_util.dart';
import '/index.dart';
export 'package:go_router/go_router.dart';
export 'serialization_util.dart';

const kTransitionInfoKey = '__transition_info__';

class AppStateNotifier extends ChangeNotifier {
  AppStateNotifier._();

  static AppStateNotifier? _instance;
  static AppStateNotifier get instance => _instance ??= AppStateNotifier._();

  bool showSplashImage = true;

  void stopShowingSplashImage() {
    showSplashImage = false;
    notifyListeners();
  }
}

GoRouter createRouter(AppStateNotifier appStateNotifier) => GoRouter(
      initialLocation: '/',
      debugLogDiagnostics: true,
      refreshListenable: appStateNotifier,
      errorBuilder: (context, state) => SplashPageWidget(),
      routes: [
        FFRoute(
          name: '_initialize',
          path: '/',
          builder: (context, _) => SplashPageWidget(),
        ),
        FFRoute(
          name: 'SplashPage',
          path: '/splashPage',
          builder: (context, params) => SplashPageWidget(),
        ),
        FFRoute(
          name: 'IntroPage',
          path: '/introPage',
          builder: (context, params) => IntroPageWidget(),
        ),
        FFRoute(
          name: 'LoginPage',
          path: '/loginPage',
          builder: (context, params) => LoginPageWidget(),
        ),
        FFRoute(
          name: 'SignupPage',
          path: '/signupPage',
          builder: (context, params) => SignupPageWidget(),
        ),
        FFRoute(
          name: 'ForgotPasswordPage',
          path: '/forgotPasswordPage',
          builder: (context, params) => ForgotPasswordPageWidget(),
        ),
        FFRoute(
          name: 'VerificationPage',
          path: '/verificationPage',
          builder: (context, params) => VerificationPageWidget(),
        ),
        FFRoute(
          name: 'ResetPasswordPage',
          path: '/resetPasswordPage',
          builder: (context, params) => ResetPasswordPageWidget(),
        ),
        FFRoute(
          name: 'LocationPage',
          path: '/locationPage',
          builder: (context, params) => LocationPageWidget(),
        ),
        FFRoute(
          name: 'AddManuallyAddressPage',
          path: '/addManuallyAddressPage',
          builder: (context, params) => AddManuallyAddressPageWidget(),
        ),
        FFRoute(
          name: 'HomePage',
          path: '/homePage',
          builder: (context, params) => HomePageWidget(),
        ),
        FFRoute(
          name: 'CategoriesPage',
          path: '/categoriesPage',
          builder: (context, params) => CategoriesPageWidget(
            barter: params.getParam('barter', ParamType.bool),
          ),
        ),
        FFRoute(
          name: 'PopularItemsPage',
          path: '/popularItemsPage',
          builder: (context, params) => PopularItemsPageWidget(),
        ),
        FFRoute(
          name: 'LatestVenderPage',
          path: '/latestVenderPage',
          builder: (context, params) => LatestVenderPageWidget(),
        ),
        FFRoute(
          name: 'RecommendedForYouPage',
          path: '/recommendedForYouPage',
          builder: (context, params) => RecommendedForYouPageWidget(),
        ),
        FFRoute(
          name: 'TopRatedSellersPage',
          path: '/topRatedSellersPage',
          builder: (context, params) => TopRatedSellersPageWidget(),
        ),
        FFRoute(
          name: 'RecentUploadPage',
          path: '/recentUploadPage',
          builder: (context, params) => RecentUploadPageWidget(),
        ),
        FFRoute(
          name: 'FeaturedProductPage',
          path: '/featuredProductPage',
          builder: (context, params) => FeaturedProductPageWidget(),
        ),
        FFRoute(
          name: 'FilterPage',
          path: '/filterPage',
          builder: (context, params) => FilterPageWidget(),
        ),
        FFRoute(
          name: 'ProductDetailPage',
          path: '/productDetailPage',
          builder: (context, params) {
            final post = params.getParam('post', ParamType.PostModel);
            final related = params.getParam('related', ParamType.bool) ??
                false; // Default to false if not provided
            return ProductDetailPageWidget(post: post, related: related);
          },
        ),
        FFRoute(
          name: 'BlogDetailPage',
          path: '/blogDetailPage',
          builder: (context, params) => BlogDetailPage(
            blog: params.getParam('blog', ParamType.BlogModel),
          ),
        ),
        FFRoute(
          name: 'ContactUsPage',
          path: '/ContactUsPage',
          builder: (context, params) => ContactUsPage(),
        ),
        FFRoute(
          name: 'ShopPointPage',
          path: '/shopPointPage',
          builder: (context, params) => ShopPointPageWidget(),
        ),
        FFRoute(
          name: 'ChatsPage',
          path: '/chatsPage',
          builder: (context, params) => ChatsPageWidget(),
        ),
        FFRoute(
          name: 'ChatDetailsPage',
          path: '/chatDetailsPage',
          builder: (context, params) => ChatDetailsPageWidget(
            vendor_id: params.getParam('vendor_id', ParamType.int),
            vendor_name: params.getParam('vendor_name', ParamType.String),
          ),
        ),
        FFRoute(
          name: 'FavouritePage',
          path: '/favouritePage',
          builder: (context, params) => FavouritePageWidget(),
        ),
        FFRoute(
          name: 'ProfilePage',
          path: '/profilePage',
          builder: (context, params) => ProfileTabWidget(),
        ),
        FFRoute(
          name: 'MyProfilePage',
          path: '/myProfilePage',
          builder: (context, params) => MyProfilePageWidget(),
        ),
        FFRoute(
          name: 'EditProfilePage',
          path: '/editProfilePage',
          builder: (context, params) => EditProfilePageWidget(),
        ),
        FFRoute(
          name: 'MyProductsPage',
          path: '/myProductsPage',
          builder: (context, params) => MyProductsPageWidget(),
        ),
        FFRoute(
          name: 'SettingsPage',
          path: '/settingsPage',
          builder: (context, params) => SettingsPageWidget(),
        ),
        FFRoute(
          name: 'TermsAndConditionPage',
          path: '/termsAndConditionPage',
          builder: (context, params) => TermsAndConditionPageWidget(),
        ),
        FFRoute(
          name: 'AboutUsPage',
          path: '/aboutUsPage',
          builder: (context, params) => AboutUsPageWidget(),
        ),
        FFRoute(
          name: 'PrivacyPolicyPage',
          path: '/privacyPolicyPage',
          builder: (context, params) => PrivacyPolicyPageWidget(),
        ),
        FFRoute(
          name: 'ProfileProductDetailPage',
          path: '/profileProductDetailPage',
          builder: (context, params) => ProfileProductDetailPageWidget(),
        ),
        FFRoute(
          name: 'SubscriptionPage',
          path: '/subscriptionPage',
          builder: (context, params) => SubscriptionPageWidget(),
        ),
        FFRoute(
          name: 'PaymentMethodPage',
          path: '/paymentMethodPage',
          builder: (context, params) => PaymentMethodPageWidget(),
        ),
        FFRoute(
          name: 'ReviewSummaryPage',
          path: '/reviewSummaryPage',
          builder: (context, params) => ReviewSummaryPageWidget(),
        ),
        FFRoute(
          name: 'AddProductPage',
          path: '/addProductPage',
          builder: (context, params) => AddProductPageWidget(),
        ),
        FFRoute(
          name: 'ChooseCategoriesPage',
          path: '/chooseCategoriesPage',
          builder: (context, params) => ChooseCategoriesPageWidget(),
        ),
        FFRoute(
          name: 'ChooseSubCategoriesPage',
          path: '/chooseSubCategoriesPage',
          builder: (context, params) => ChooseChildCategoriesPageWidget(),
        ),
        FFRoute(
          name: 'ChooseSecondSubCategoriesPage',
          path: '/chooseSecondSubCategoriesPage',
          builder: (context, params) => ChooseSecondChildCategoriesPageWidget(),
        ),
        FFRoute(
          name: 'EditProductPage',
          path: '/editProductPage',
          builder: (context, params) => EditProductPageWidget(),
        ),
        FFRoute(
          name: 'HomeBottomBarPage',
          path: '/homeBottomBarPage',
          builder: (context, params) => HomeBottomBarPageWidget(),
        ),
        FFRoute(
          name: 'VendorDetailsPage',
          path: '/vendorDetailsPage',
          builder: (context, params) => VendorDetailsPageWidget(),
        ),
        FFRoute(
          name: 'SubCategoriesPage',
          path: '/subCategoriesPage',
          builder: (context, params) => SubCategoriesPageWidget(
            title: params.getParam('title', ParamType.String),
            image: params.getParam('image', ParamType.String),
            index: params.getParam('index', ParamType.int),
            barter: params.getParam('barter', ParamType.bool),
          ),
        ),
        FFRoute(
          name: 'CategoryProductPage',
          path: '/categoryProductPage',
          builder: (context, params) => CategoryProductPageWidget(
            title: params.getParam('title', ParamType.String),
            index: params.getParam('index', ParamType.int),
            isMaincategory: params.getParam('isMaincategory', ParamType.bool),
            barter: params.getParam('barter', ParamType.bool),
          ),
        ),
        FFRoute(
          name: 'DiscountItemsPage',
          path: '/discountItemsPage',
          builder: (context, params) => DiscountItemsPageWidget(),
        ),
        FFRoute(
          name: 'BlogListPage',
          path: '/blogListPage',
          builder: (context, params) => BlogListPage(),
        ),
        FFRoute(
          name: 'ProductProfilePage',
          path: '/productProfilePage',
          builder: (context, params) => ProductProfilePageWidget(),
        ),
        FFRoute(
          name: 'NotificationPage',
          path: '/notificationPage',
          builder: (context, params) => NotificationPageWidget(),
        ),
        FFRoute(
          name: 'SearchPage',
          path: '/searchPage',
          builder: (context, params) => SearchPageWidget(),
        ),
        FFRoute(
          name: 'EditPost',
          path: '/EditPost',
          builder: (context, params) => EditPost(
            id: params.getParam('id', ParamType.int),
          ),
        )
      ].map((r) => r.toRoute(appStateNotifier)).toList(),
    );

extension NavParamExtensions on Map<String, String?> {
  Map<String, String> get withoutNulls => Map.fromEntries(
        entries
            .where((e) => e.value != null)
            .map((e) => MapEntry(e.key, e.value!)),
      );
}

extension NavigationExtensions on BuildContext {
  void safePop() {
    // If there is only one route on the stack, navigate to the initial
    // page instead of popping.
    if (canPop()) {
      pop();
    } else {
      go('/');
    }
  }
}

extension _GoRouterStateExtensions on GoRouterState {
  Map<String, dynamic> get extraMap =>
      extra != null ? extra as Map<String, dynamic> : {};
  Map<String, dynamic> get allParams => <String, dynamic>{}
    ..addAll(pathParameters)
    ..addAll(uri.queryParameters)
    ..addAll(extraMap);
  TransitionInfo get transitionInfo => extraMap.containsKey(kTransitionInfoKey)
      ? extraMap[kTransitionInfoKey] as TransitionInfo
      : TransitionInfo.appDefault();
}

class FFParameters {
  FFParameters(this.state, [this.asyncParams = const {}]);

  final GoRouterState state;
  final Map<String, Future<dynamic> Function(String)> asyncParams;

  Map<String, dynamic> futureParamValues = {};

  // Parameters are empty if the params map is empty or if the only parameter
  // present is the special extra parameter reserved for the transition info.
  bool get isEmpty =>
      state.allParams.isEmpty ||
      (state.extraMap.length == 1 &&
          state.extraMap.containsKey(kTransitionInfoKey));
  bool isAsyncParam(MapEntry<String, dynamic> param) =>
      asyncParams.containsKey(param.key) && param.value is String;
  bool get hasFutures => state.allParams.entries.any(isAsyncParam);
  Future<bool> completeFutures() => Future.wait(
        state.allParams.entries.where(isAsyncParam).map(
          (param) async {
            final doc = await asyncParams[param.key]!(param.value)
                .onError((_, __) => null);
            if (doc != null) {
              futureParamValues[param.key] = doc;
              return true;
            }
            return false;
          },
        ),
      ).onError((_, __) => [false]).then((v) => v.every((e) => e));

  dynamic getParam<T>(
    String paramName,
    ParamType type, [
    bool isList = false,
  ]) {
    if (futureParamValues.containsKey(paramName)) {
      return futureParamValues[paramName];
    }
    if (!state.allParams.containsKey(paramName)) {
      return null;
    }
    final param = state.allParams[paramName];
    // Got parameter from `extras`, so just directly return it.
    if (param is! String) {
      return param;
    }
    // Return serialized value.
    return deserializeParam<T>(
      param,
      type,
      isList,
    );
  }
}

class FFRoute {
  const FFRoute({
    required this.name,
    required this.path,
    required this.builder,
    this.requireAuth = false,
    this.asyncParams = const {},
    this.routes = const [],
  });

  final String name;
  final String path;
  final bool requireAuth;
  final Map<String, Future<dynamic> Function(String)> asyncParams;
  final Widget Function(BuildContext, FFParameters) builder;
  final List<GoRoute> routes;

  GoRoute toRoute(AppStateNotifier appStateNotifier) => GoRoute(
        name: name,
        path: path,
        pageBuilder: (context, state) {
          fixStatusBarOniOS16AndBelow(context);
          final ffParams = FFParameters(state, asyncParams);
          final page = ffParams.hasFutures
              ? FutureBuilder(
                  future: ffParams.completeFutures(),
                  builder: (context, _) => builder(context, ffParams),
                )
              : builder(context, ffParams);
          final child = page;

          final transitionInfo = state.transitionInfo;
          return transitionInfo.hasTransition
              ? CustomTransitionPage(
                  key: state.pageKey,
                  child: child,
                  transitionDuration: transitionInfo.duration,
                  transitionsBuilder:
                      (context, animation, secondaryAnimation, child) =>
                          PageTransition(
                    type: transitionInfo.transitionType,
                    duration: transitionInfo.duration,
                    reverseDuration: transitionInfo.duration,
                    alignment: transitionInfo.alignment,
                    child: child,
                  ).buildTransitions(
                    context,
                    animation,
                    secondaryAnimation,
                    child,
                  ),
                )
              : MaterialPage(key: state.pageKey, child: child);
        },
        routes: routes,
      );
}

class TransitionInfo {
  const TransitionInfo({
    required this.hasTransition,
    this.transitionType = PageTransitionType.fade,
    this.duration = const Duration(milliseconds: 300),
    this.alignment,
  });

  final bool hasTransition;
  final PageTransitionType transitionType;
  final Duration duration;
  final Alignment? alignment;

  static TransitionInfo appDefault() => TransitionInfo(hasTransition: false);
}

class RootPageContext {
  const RootPageContext(this.isRootPage, [this.errorRoute]);
  final bool isRootPage;
  final String? errorRoute;

  static bool isInactiveRootPage(BuildContext context) {
    final rootPageContext = context.read<RootPageContext?>();
    final isRootPage = rootPageContext?.isRootPage ?? false;
    final location = GoRouter.of(context).routeInformationProvider.value.uri.toString();
    return isRootPage &&
        location != '/' &&
        location != rootPageContext?.errorRoute;
  }

  static Widget wrap(Widget child, {String? errorRoute}) => Provider.value(
        value: RootPageContext(true, errorRoute),
        child: child,
      );
}
