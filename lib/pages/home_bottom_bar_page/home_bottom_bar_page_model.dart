import 'package:soho_souk/main.dart';

import '../../Classified_App/classified_app_model.dart';
import '/home_tabs/add_product_tab/add_product_tab_widget.dart';
import '/home_tabs/chat_tab/chat_tab_widget.dart';
import '/home_tabs/favourite_tab/favourite_tab_widget.dart';
import '/home_tabs/home_tab/home_tab_widget.dart';
import '/home_tabs/profile_tab/profile_tab_widget.dart';
import 'home_bottom_bar_page_widget.dart' show HomeBottomBarPageWidget;
import 'package:flutter/material.dart';

class HomeBottomBarPageModel extends ClassifiedAppModel<HomeBottomBarPageWidget> {
  ///  State fields for stateful widgets in this page.

  final unfocusNode = FocusNode();
  // State field(s) for PageView widget.
 

  int get pageViewCurrentIndex => appStore.pageViewController != null &&
          appStore.pageViewController!.hasClients &&
          appStore.pageViewController!.page != null
      ? appStore.pageViewController!.page!.round()
      : 0;
  // Model for HomeTab component.
  late HomeTabModel homeTabModel;
  // Model for ChatTab component.
  late ChatTabModel chatTabModel;
  // Model for AddProductTab component.
  late AddProductTabModel addProductTabModel;
  // Model for FavouriteTab component.
  late FavouriteTabModel favouriteTabModel;
  // Model for ProfileTab component.
  late ProfileTabModel profileTabModel;

  /// Initialization and disposal methods.

  @override
  void initState(BuildContext context) {
    homeTabModel = createModel(context, () => HomeTabModel());
    chatTabModel = createModel(context, () => ChatTabModel());
    addProductTabModel = createModel(context, () => AddProductTabModel());
    favouriteTabModel = createModel(context, () => FavouriteTabModel());
    profileTabModel = createModel(context, () => ProfileTabModel());
  }

  @override
  void dispose() {
    unfocusNode.dispose();
    homeTabModel.dispose();
    chatTabModel.dispose();
    addProductTabModel.dispose();
    favouriteTabModel.dispose();
    profileTabModel.dispose();
  }

  /// Action blocks are added here.

  /// Additional helper methods are added here.
}
