import 'package:soho_souk/main.dart';
import 'package:soho_souk/pages/barter_post_list/barter_post_list_widget.dart';
import 'package:soho_souk/pages/favourite_empty_component/favourite_empty_component_widget.dart';
import 'package:soho_souk/pages/notification_page/notification_page_widget.dart';

import '../../Classified_App/classified_app_theme.dart';
import '../../Classified_App/classified_app_util.dart';
import '/home_tabs/add_product_tab/add_product_tab_widget.dart';
import '/home_tabs/chat_tab/chat_tab_widget.dart';
import '/home_tabs/favourite_tab/favourite_tab_widget.dart';
import '/home_tabs/home_tab/home_tab_widget.dart';
import '/home_tabs/profile_tab/profile_tab_widget.dart';
import 'dart:async';
import 'package:flutter/material.dart';
//import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'home_bottom_bar_page_model.dart';
export 'home_bottom_bar_page_model.dart';

class HomeBottomBarPageWidget extends StatefulWidget {
  const HomeBottomBarPageWidget({super.key});

  @override
  State<HomeBottomBarPageWidget> createState() =>
      _HomeBottomBarPageWidgetState();
}

class _HomeBottomBarPageWidgetState extends State<HomeBottomBarPageWidget> {
  late HomeBottomBarPageModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();
  StreamSubscription<bool>? _keyboardVisibilitySubscription;
  bool _isKeyboardVisible = false;

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => HomeBottomBarPageModel());

    if (!isWeb) {
      // _keyboardVisibilitySubscription =
      //     KeyboardVisibilityController().onChange.listen((bool visible) {
      //   setState(() {
      //     _isKeyboardVisible = visible;
      //   });
      // });
    }
  }

  @override
  void dispose() {
    _model.dispose();

    if (!isWeb) {
      _keyboardVisibilitySubscription?.cancel();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: ClassifiedAppTheme.of(context).primaryBackground,
        body: SafeArea(
          top: true,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              Expanded(
                child: Container(
                  width: double.infinity,
                  child: PageView(
                    controller: appStore.pageViewController ??=
                        PageController(initialPage: 0),
                    onPageChanged: (_) async {
                      setState(() {
                        FFAppState().selectedPageIndex =
                            _model.pageViewCurrentIndex;
                      });
                    },
                    scrollDirection: Axis.horizontal,
                    physics: NeverScrollableScrollPhysics(),
                    children: [
                      wrapWithModel(
                        model: _model.homeTabModel,
                        updateCallback: () => setState(() {}),
                        child: HomeTabWidget(),
                      ),
                      wrapWithModel(
                        model: _model.chatTabModel,
                        updateCallback: () => setState(() {}),
                        child: BarterPostListWidget(),
                      //  child: ChatTabWidget(),
                      ),
                      wrapWithModel(
                        model: _model.addProductTabModel,
                        updateCallback: () => setState(() {}),
                        child: AddProductTabWidget(),
                      ),
                      wrapWithModel(
                        model: _model.favouriteTabModel,
                        updateCallback: () => setState(() {}),
                        child: FavouriteTabWidget(),
                       // child: FavouriteEmptyComponentWidget(),
                      ),
                      wrapWithModel(
                        model: _model.profileTabModel,
                        updateCallback: () => setState(() {}),
                        child: NotificationPageWidget(),
                      ),
                    ],
                  ),
                ),
              ),
              // if (!(isWeb
              //     ? MediaQuery.viewInsetsOf(context).bottom > 0
              //     : _isKeyboardVisible))
                Align(
                  alignment: AlignmentDirectional(0.0, 1.0),
                  child: Container(
                    width: double.infinity,
                    height: 95.0,
                    decoration: BoxDecoration(
                      color: ClassifiedAppTheme.of(context).tertiary,
                      boxShadow: [
                        BoxShadow(
                          blurRadius: 26.0,
                          color: Color(0x14959595),
                          offset: Offset(0.0, -4.0),
                          spreadRadius: 0.0,
                        )
                      ],
                    ),
                    alignment: AlignmentDirectional(0.0, 0.0),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Expanded(
                          child: InkWell(
                            splashColor: Colors.transparent,
                            focusColor: Colors.transparent,
                            hoverColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            onTap: () async {
                              setState(() {
                                FFAppState().selectedPageIndex = 0;
                              });
                              await appStore.pageViewController?.animateToPage(
                                FFAppState().selectedPageIndex,
                                duration: Duration(milliseconds: 500),
                                curve: Curves.ease,
                              );
                            },
                            child: Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                color: ClassifiedAppTheme.of(context)
                                    .secondaryBackground,
                                borderRadius: BorderRadius.only(
                                  bottomLeft: Radius.circular(0.0),
                                  bottomRight: Radius.circular(0.0),
                                  topLeft: Radius.circular(16.0),
                                  topRight: Radius.circular(0.0),
                                ),
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Stack(
                                    children: [
                                      if (FFAppState().selectedPageIndex == 0)
                                        Padding(
                                          padding:
                                              EdgeInsetsDirectional.fromSTEB(
                                                  0.0, 0.0, 0.0, 7.0),
                                          child: ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(0.0),
                                            child: Image.asset(
                                              'assets/images/homeSelected.png',
                                              width: 24.0,
                                              height: 24.0,
                                              fit: BoxFit.cover,
                                            ),
                                          ),
                                        ),
                                      if (FFAppState().selectedPageIndex != 0)
                                        Padding(
                                          padding:
                                              EdgeInsetsDirectional.fromSTEB(
                                                  0.0, 0.0, 0.0, 7.0),
                                          child: ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(0.0),
                                            child: Image.asset(
                                              'assets/images/homeUnselected.png',
                                              width: 24.0,
                                              height: 24.0,
                                              fit: BoxFit.cover,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                  Text(
                                    FFLocalizations.of(context).getText(
                                      '9b5g29ml' /* Home */,
                                    ),
                                    textAlign: TextAlign.center,
                                    style: ClassifiedAppTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'Satoshi',
                                          color:
                                              FFAppState().selectedPageIndex ==
                                                      0
                                                  ? ClassifiedAppTheme.of(context)
                                                      .primary
                                                  : ClassifiedAppTheme.of(context)
                                                      .primaryText,
                                          fontSize: 13.0,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          child: InkWell(
                            splashColor: Colors.transparent,
                            focusColor: Colors.transparent,
                            hoverColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            onTap: () async {
                              setState(() {
                                FFAppState().selectedPageIndex = 1;
                              });
                              await appStore.pageViewController?.animateToPage(
                                FFAppState().selectedPageIndex,
                                duration: Duration(milliseconds: 500),
                                curve: Curves.ease,
                              );
                            },
                            child: Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                color: ClassifiedAppTheme.of(context)
                                    .secondaryBackground,
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Stack(
                                    children: [
                                      if (FFAppState().selectedPageIndex == 1)
                                        Padding(
                                          padding:
                                              EdgeInsetsDirectional.fromSTEB(
                                                  0.0, 0.0, 0.0, 7.0),
                                          child: ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(0.0),
                                            child: Image.asset(
                                              'assets/images/barter-select.png',
                                              width: 24.0,
                                              height: 24.0,
                                              fit: BoxFit.cover,
                                            ),
                                          ),
                                        ),
                                      if (FFAppState().selectedPageIndex != 1)
                                        Padding(
                                          padding:
                                              EdgeInsetsDirectional.fromSTEB(
                                                  0.0, 0.0, 0.0, 7.0),
                                          child: ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(0.0),
                                            child: Image.asset(
                                              'assets/images/barter.png',
                                              width: 24.0,
                                              height: 24.0,
                                              fit: BoxFit.cover,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                  Text(
                                    FFLocalizations.of(context).getText(
                                      '3pvq1j7p' /* Chats */,
                                    ),
                                    textAlign: TextAlign.center,
                                    style: ClassifiedAppTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'Satoshi',
                                          color:
                                              FFAppState().selectedPageIndex ==
                                                      1
                                                  ? ClassifiedAppTheme.of(context)
                                                      .primary
                                                  : ClassifiedAppTheme.of(context)
                                                      .primaryText,
                                          fontSize: 13.0,
                                          fontWeight: FontWeight.normal,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          child: Align(
                            alignment: AlignmentDirectional(0.0, 0.0),
                            child: InkWell(
                              splashColor: Colors.transparent,
                              focusColor: Colors.transparent,
                              hoverColor: Colors.transparent,
                              highlightColor: Colors.transparent,
                              onTap: () async {
                                setState(() {
                                  FFAppState().selectedPageIndex = 2;
                                });
                                await appStore.pageViewController?.animateToPage(
                                  FFAppState().selectedPageIndex,
                                  duration: Duration(milliseconds: 500),
                                  curve: Curves.ease,
                                );
                              },
                              child: Container(
                                width: double.infinity,
                                height: double.infinity,
                                decoration: BoxDecoration(
                                  color: ClassifiedAppTheme.of(context)
                                      .primaryBackground,
                                ),
                                alignment: AlignmentDirectional(0.0, 0.0),
                                child: Column(
                                  children: [
                                    SizedBox(
                                      height: 5.0,
                                    ),
                                    Container(
                                      width: 46.0,
                                      height: 46.0,
                                      decoration: BoxDecoration(
                                        color: ClassifiedAppTheme.of(context).primary,
                                        shape: BoxShape.circle,
                                      ),
                                      alignment: AlignmentDirectional(0.0, 0.0),
                                      child: SvgPicture.asset(
                                        'assets/images/add-white.svg',
                                        width: 26.0,
                                        height: 26.0,
                                        fit: BoxFit.contain,
                                      ),
                                      
                                    ),
                                    SizedBox(
                                      height: 4.0,
                                    ),
                                    Text(
                                    FFLocalizations.of(context).getText(
                                      '5tg6jwhl' /* Add Post */,
                                    ),
                                    textAlign: TextAlign.center,
                                    style: ClassifiedAppTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'Satoshi',
                                          color:
                                              FFAppState().selectedPageIndex ==
                                                      1
                                                  ? ClassifiedAppTheme.of(context)
                                                      .primary
                                                  : ClassifiedAppTheme.of(context)
                                                      .primaryText,
                                          fontSize: 13.0,
                                          fontWeight: FontWeight.normal,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          child: InkWell(
                            splashColor: Colors.transparent,
                            focusColor: Colors.transparent,
                            hoverColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            onTap: () async {
                              setState(() {
                                FFAppState().selectedPageIndex = 3;
                              });
                              await appStore.pageViewController?.animateToPage(
                                FFAppState().selectedPageIndex,
                                duration: Duration(milliseconds: 500),
                                curve: Curves.ease,
                              );
                            },
                            child: Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                color: ClassifiedAppTheme.of(context)
                                    .secondaryBackground,
                                borderRadius: BorderRadius.only(
                                  bottomLeft: Radius.circular(0.0),
                                  bottomRight: Radius.circular(0.0),
                                  topLeft: Radius.circular(0.0),
                                  topRight: Radius.circular(0.0),
                                ),
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Stack(
                                    children: [
                                      if (FFAppState().selectedPageIndex == 3)
                                        Padding(
                                          padding:
                                              EdgeInsetsDirectional.fromSTEB(
                                                  0.0, 0.0, 0.0, 7.0),
                                          child: ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(0.0),
                                            child: Image.asset(
                                              'assets/images/favSelected.png',
                                              width: 24.0,
                                              height: 24.0,
                                              fit: BoxFit.cover,
                                            ),
                                          ),
                                        ),
                                      if (FFAppState().selectedPageIndex != 3)
                                        Padding(
                                          padding:
                                              EdgeInsetsDirectional.fromSTEB(
                                                  0.0, 0.0, 0.0, 7.0),
                                          child: ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(0.0),
                                            child: Image.asset(
                                              'assets/images/favUnselected.png',
                                              width: 24.0,
                                              height: 24.0,
                                              fit: BoxFit.cover,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                  Text(
                                    FFLocalizations.of(context).getText(
                                      '357uslu2' /* Favorites */,
                                    ),
                                    textAlign: TextAlign.center,
                                    style: ClassifiedAppTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'Satoshi',
                                          color:
                                              FFAppState().selectedPageIndex ==
                                                      3
                                                  ? ClassifiedAppTheme.of(context)
                                                      .primary
                                                  : ClassifiedAppTheme.of(context)
                                                      .primaryText,
                                          fontSize: 14.0,
                                          fontWeight: FontWeight.normal,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          child: InkWell(
                            splashColor: Colors.transparent,
                            focusColor: Colors.transparent,
                            hoverColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            onTap: () async {
                              setState(() {
                                FFAppState().selectedPageIndex = 4;
                              });
                              await appStore.pageViewController?.animateToPage(
                                FFAppState().selectedPageIndex,
                                duration: Duration(milliseconds: 500),
                                curve: Curves.ease,
                              );
                            },
                            child: Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                color: ClassifiedAppTheme.of(context)
                                    .secondaryBackground,
                                borderRadius: BorderRadius.only(
                                  bottomLeft: Radius.circular(0.0),
                                  bottomRight: Radius.circular(0.0),
                                  topLeft: Radius.circular(0.0),
                                  topRight: Radius.circular(16.0),
                                ),
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Stack(
                                    children: [
                                      if (FFAppState().selectedPageIndex == 4)
                                        Padding(
                                          padding:
                                              EdgeInsetsDirectional.fromSTEB(
                                                  0.0, 0.0, 0.0, 7.0),
                                          child: ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(0.0),
                                            child: SvgPicture.asset(
                                      'assets/images/notification_primary.svg',
                                      width: 24.0,
                                      height: 24.0,
                                      fit: BoxFit.cover,
                                      color: ClassifiedAppTheme.of(context).primary,
                                    ),
                                          ),
                                        ),
                                      if (FFAppState().selectedPageIndex != 4)
                                        Padding(
                                          padding:
                                              EdgeInsetsDirectional.fromSTEB(
                                                  0.0, 0.0, 0.0, 7.0),
                                          child: ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(0.0),
                                            child: SvgPicture.asset(
                                      'assets/images/notification.svg',
                                      width: 24.0,
                                      height: 24.0,
                                      fit: BoxFit.cover,
                                    ),
                                           
                                          ),
                                        ),
                                    ],
                                  ),
                                  Text(
                                    FFLocalizations.of(context).getText(
                                          'chhnt6y5' /* Alert */,
                                        ),
                                    textAlign: TextAlign.center,
                                    style: ClassifiedAppTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'Satoshi',
                                          color:
                                              FFAppState().selectedPageIndex ==
                                                      4
                                                  ? ClassifiedAppTheme.of(context)
                                                      .primary
                                                  : ClassifiedAppTheme.of(context)
                                                      .primaryText,
                                          fontSize: 14.0,
                                          fontWeight: FontWeight.normal,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
