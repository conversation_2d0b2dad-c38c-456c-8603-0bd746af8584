import '../../Classified_App/classified_app_model.dart';
import '/pages/app_button/app_button_widget.dart';
import 'favourite_empty_component_widget.dart'
    show FavouriteEmptyComponentWidget;
import 'package:flutter/material.dart';

class FavouriteEmptyComponentModel
    extends ClassifiedAppModel<FavouriteEmptyComponentWidget> {
  ///  State fields for stateful widgets in this component.

  // Model for AppButton component.
  late AppButtonModel appButtonModel;

  /// Initialization and disposal methods.

  @override
  void initState(BuildContext context) {
    appButtonModel = createModel(context, () => AppButtonModel());
  }

  @override
  void dispose() {
    appButtonModel.dispose();
  }

  /// Action blocks are added here.

  /// Additional helper methods are added here.
}
