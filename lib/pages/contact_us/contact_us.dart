import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:soho_souk/ApiUtils.dart';
import 'package:soho_souk/Classified_App/classified_app_theme.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/services.dart';
import 'package:soho_souk/widget/loader.dart';

class ContactUsPage extends StatefulWidget {
  @override
  _ContactUsPageState createState() => _ContactUsPageState();
}

class _ContactUsPageState extends State<ContactUsPage> {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController mobileController = TextEditingController();
  final TextEditingController messageController = TextEditingController();

  @override
  void dispose() {
    nameController.dispose();
    emailController.dispose();
    mobileController.dispose();
    messageController.dispose();
    super.dispose();
  }
 

  Future<void> _submitForm() async {
    final String name = nameController.text;
    final String email = emailController.text;
    final String mobile = mobileController.text;
    final String message = messageController.text;

    if (name.isEmpty || email.isEmpty || mobile.isEmpty || message.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
        'Please fill all fields',
        textAlign: TextAlign.center,
        style: TextStyle(color: Colors.white),
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }
      showLoadingDialog(context);
    final response = await http.post(
    Uri.parse("${ApiUtils.BASE_URL}save-contact-enquiry"),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
        'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi",
      },
      body: jsonEncode(<String, String>{
        'name': name,
        'email': email,
        'mobile': mobile,
        'message': message,
      }),
    );

    if (response.statusCode == 200) {
       Navigator.of(context, rootNavigator: true).pop(false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Form submitted successfully', textAlign: TextAlign.center,
        style: TextStyle(color: Colors.white),),    backgroundColor: Colors.green,),
      );
      nameController.clear();
      emailController.clear();
      mobileController.clear();
      messageController.clear();
    } else {
       Navigator.of(context, rootNavigator: true).pop(false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to submit form', textAlign: TextAlign.center,
        style: TextStyle(color: Colors.white),),    backgroundColor: Colors.red,),
      );
    }
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
      title: Text('Contact Us'),
      backgroundColor: ClassifiedAppTheme.of(context).primaryColor,
      foregroundColor: Colors.white,
      centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Container(
          height: MediaQuery.of(context).size.height,
        color: Colors.white,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
          children: <Widget>[
            // CircleAvatar(
            // radius: 50,
            // backgroundColor: Colors.transparent,
            // backgroundImage: AssetImage('assets/images/contact-us.png'), // Add your contact image here
            // ),
            SizedBox(height: 20),
            TextField(
            controller: nameController,
            style: TextStyle(color: Colors.black),
            decoration: InputDecoration(
              enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: ClassifiedAppTheme.of(context).primaryColor),
              borderRadius: BorderRadius.all(Radius.circular(20)),
              ),
              focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(color: ClassifiedAppTheme.of(context).primaryColor),
              borderRadius: BorderRadius.all(Radius.circular(20)),
              ),
              labelText: 'Name',
              labelStyle: TextStyle(color: ClassifiedAppTheme.of(context).primaryColor),
              border: OutlineInputBorder(),
            ),
            ),
            SizedBox(height: 20),
            TextField(
            controller: emailController,
            style: TextStyle(color: Colors.black),
              decoration: InputDecoration(
              enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: ClassifiedAppTheme.of(context).primaryColor),
              borderRadius: BorderRadius.all(Radius.circular(20)),
              ),
              focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(color: ClassifiedAppTheme.of(context).primaryColor),
              borderRadius: BorderRadius.all(Radius.circular(20)),
              ),
              labelText: 'Email',
              labelStyle: TextStyle(color: ClassifiedAppTheme.of(context).primaryColor),
              border: OutlineInputBorder(),
            ),
            ),
            SizedBox(height: 20),
            TextField(
            controller: mobileController,
            keyboardType: TextInputType.number,
            inputFormatters: <TextInputFormatter>[
              FilteringTextInputFormatter.digitsOnly
            ],
            style: TextStyle(color: Colors.black),
             decoration: InputDecoration(
              enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: ClassifiedAppTheme.of(context).primaryColor),
              borderRadius: BorderRadius.all(Radius.circular(20)),
              ),
              focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(color: ClassifiedAppTheme.of(context).primaryColor),
              borderRadius: BorderRadius.all(Radius.circular(20)),
              ),
              labelText: 'Mobile Number',
              labelStyle: TextStyle(color: ClassifiedAppTheme.of(context).primaryColor),
              border: OutlineInputBorder(),
            ),
            ),
            SizedBox(height: 20),
            TextField(
            controller: messageController,
            style: TextStyle(color: Colors.black),
              decoration: InputDecoration(
              enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: ClassifiedAppTheme.of(context).primaryColor),
              borderRadius: BorderRadius.all(Radius.circular(20)),
              ),
              focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(color: ClassifiedAppTheme.of(context).primaryColor),
              borderRadius: BorderRadius.all(Radius.circular(20)),
              ),
              labelText: 'Message',
              labelStyle: TextStyle(color: ClassifiedAppTheme.of(context).primaryColor),
              border: OutlineInputBorder(),
            ),
            maxLines: 5,
            ),
            SizedBox(height: 20),
            ElevatedButton(
            onPressed: () {
              _submitForm();
              // Handle submit action
            },
              child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 32.0),
              child: Text(
                'Submit',
                style: TextStyle(fontSize: 18),
              ),
              ),
              style: ElevatedButton.styleFrom(
              backgroundColor: ClassifiedAppTheme.of(context).primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              ),
            ),
          ],
          ),
        ),
        ),
      ),
    );
     
  }
}