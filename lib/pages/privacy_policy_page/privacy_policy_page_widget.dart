import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:soho_souk/main.dart';

import '../../Classified_App/classified_app_animations.dart';
import '../../Classified_App/classified_app_model.dart';
import '../../Classified_App/classified_app_theme.dart';
import '../../Classified_App/internationalization.dart';
import '/pages/app_bar/app_bar_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'privacy_policy_page_model.dart';
export 'privacy_policy_page_model.dart';

class PrivacyPolicyPageWidget extends StatefulWidget {
  const PrivacyPolicyPageWidget({super.key});

  @override
  State<PrivacyPolicyPageWidget> createState() =>
      _PrivacyPolicyPageWidgetState();
}

class _PrivacyPolicyPageWidgetState extends State<PrivacyPolicyPageWidget>
    with TickerProviderStateMixin {
  late PrivacyPolicyPageModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  final animationsMap = {
    'columnOnPageLoadAnimation': AnimationInfo(
      trigger: AnimationTrigger.onPageLoad,
      effects: [
        FadeEffect(
          curve: Curves.easeInOut,
          delay: 50.ms,
          duration: 300.ms,
          begin: 0.15,
          end: 1.0,
        ),
      ],
    ),
  };

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => PrivacyPolicyPageModel());
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }
    String preprocessHtml(String html) {
  final regex = RegExp(r'style="[^"]*"'); // Matches the style attribute
  return html.replaceAll(regex, '');
}

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: ClassifiedAppTheme.of(context).primaryBackground,
        body: SafeArea(
          top: true,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              wrapWithModel(
                model: _model.appBarModel,
                updateCallback: () => setState(() {}),
                child: AppBarWidget(
                  title: 'Privacy policy',
                ),
              ),
              Expanded(
                child: Padding(
                  padding:
                      EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 16.0, 0.0),
                  child: SingleChildScrollView(
                    child: HtmlWidget(
  preprocessHtml("${appStore.siteDetails[0].privacy_policy}"),
  textStyle: ClassifiedAppTheme.of(context)
      .bodyMedium
      .override(
        fontFamily: 'Satoshi',
        color: ClassifiedAppTheme.of(context).secondaryText,
        fontSize: 14.0, // Set desired font size
        fontWeight: FontWeight.w500,
        useGoogleFonts: false,
      ),
),
                  ).animateOnPageLoad(
                      animationsMap['columnOnPageLoadAnimation']!),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
