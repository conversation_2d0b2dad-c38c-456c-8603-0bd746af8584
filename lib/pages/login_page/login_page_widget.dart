import 'dart:io';

import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nb_utils/nb_utils.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:soho_souk/ApiUtils.dart';
import 'package:soho_souk/main.dart';
import 'package:soho_souk/widget/loader.dart';
import '../../Classified_App/classified_app_theme.dart';
import '../../Classified_App/classified_app_util.dart';
import '/pages/app_button/app_button_widget.dart';
import '/custom_code/actions/index.dart' as actions;
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'login_page_model.dart';
export 'login_page_model.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
class LoginPageWidget extends StatefulWidget {
  const LoginPageWidget({super.key});

  @override
  State<LoginPageWidget> createState() => _LoginPageWidgetState();
}

class _LoginPageWidgetState extends State<LoginPageWidget> {
  late LoginPageModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => LoginPageModel());

    _model.textController1 ??= TextEditingController();
    _model.textFieldFocusNode1 ??= FocusNode();

    _model.textController2 ??= TextEditingController();
    _model.textFieldFocusNode2 ??= FocusNode();
  }

  DateTime? lastPressed;


 Future<bool> _onWillPop() async {
    final now = DateTime.now();
    final maxDuration = Duration(seconds: 2);

    if (lastPressed == null || now.difference(lastPressed!) > maxDuration) {
      lastPressed = now;

      if (mounted) {
        ScaffoldMessenger.of(scaffoldKey.currentContext!).showSnackBar(
          SnackBar(
          backgroundColor: ClassifiedAppTheme.of(context).primary,
          content: Text('Press again to exit',style: TextStyle(color: Colors.white),),
          duration: maxDuration,
        ),
        );
      }
      return false; // Prevent exit
    }
    return true; // Allow exit
  }

  void _exitApp() async {
    if (await _onWillPop()) {
      if (Platform.isAndroid) {
        SystemNavigator.pop(); // Close app on Android
      } else if (Platform.isIOS) {
        exit(0); // Force exit on iOS (not recommended by Apple)
      }
    }
  }


  Future<UserCredential> signInWithGoogle() async {
    // Trigger the authentication flow
    final GoogleSignInAccount? googleUser = await GoogleSignIn().signIn();

    // Obtain the auth details from the request
    final GoogleSignInAuthentication? googleAuth =
        await googleUser?.authentication;

    // Create a new credential
    final credential = GoogleAuthProvider.credential(
      accessToken: googleAuth?.accessToken,
      idToken: googleAuth?.idToken,
    );

    // Once signed in, return the UserCredential

    return await FirebaseAuth.instance.signInWithCredential(credential);
    // print(user.user.);
    //return user;
  }
  void loginWithGoogle() async {
    final UserCredential user = await signInWithGoogle();
     showLoadingDialog(context);
    print(user.user);
    if (user.user != null) {
      
      http
          .post(
        Uri.parse("${ApiUtils.BASE_URL}google-signup"),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
          'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
        },
        body: jsonEncode(<String, dynamic>{
          'email': user.user!.email,
          'name': user.user!.displayName,
          //'profile_image': user.user!.photoURL,
          'firebase_key': appStore.firebase_key.toString(),
        }),
      )
          .then((response) async{
        //Map mapValue = json.decode(response.body);
       
          Navigator.of(context, rootNavigator: true).pop(false);
      Map mapValue = json.decode(response.body);
          if(response.statusCode ==200){
           // print(mapValue);
           await  appStore.loginStore(mapValue['name'], mapValue['user_id'],
            mapValue['message'], mapValue['email'], mapValue['profile_image'],mapValue['mobile'],context);
             context.goNamed(
                'HomeBottomBarPage',
                extra: <String, dynamic>{
                  kTransitionInfoKey: TransitionInfo(
                    hasTransition: true,
                    transitionType: PageTransitionType.rightToLeft,
                    duration: Duration(milliseconds: 300),
                  ),
                },
              );
          }else{
             toasty(context, "${mapValue['message']}",
                                    bgColor: Colors.red, textColor: Colors.black);
          }
      });
    }
  }
  void signupApple(String name,String email,String userIdentifier) async {
   
     showLoadingDialog(context);

      http
          .post(
        Uri.parse("${ApiUtils.BASE_URL}apple-signup"),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
          'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
        },
        body: jsonEncode(<String, dynamic>{
          'email': email,
          'name': name,
          'firebase_key': appStore.firebase_key.toString(),
          'userIdentifier':userIdentifier.toString()
        }),
      )
          .then((response) async{
        //Map mapValue = json.decode(response.body);
       
          Navigator.of(context, rootNavigator: true).pop(false);
      Map mapValue = json.decode(response.body);
          if(response.statusCode ==200){
           // print(mapValue);
           await  appStore.loginStore(mapValue['name'], mapValue['user_id'],
            mapValue['message'], mapValue['email'], mapValue['profile_image'],mapValue['mobile'],context);
             context.goNamed(
                'HomeBottomBarPage',
                extra: <String, dynamic>{
                  kTransitionInfoKey: TransitionInfo(
                    hasTransition: true,
                    transitionType: PageTransitionType.rightToLeft,
                    duration: Duration(milliseconds: 300),
                  ),
                },
              );
          }else{
             toasty(context, "${mapValue['message']}",
                                    bgColor: Colors.red, textColor: Colors.black);
          }
      });
    
  }
  void authApple(String userIdentifier) async {

     showLoadingDialog(context);

      http
          .post(
        Uri.parse("${ApiUtils.BASE_URL}apple-auth"),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
          'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
        },
        body: jsonEncode(<String, dynamic>{
          'firebase_key': appStore.firebase_key.toString(),
          'userIdentifier':userIdentifier.toString()
        }),
      )
          .then((response) async{
        //Map mapValue = json.decode(response.body);
       
          Navigator.of(context, rootNavigator: true).pop(false);
      Map mapValue = json.decode(response.body);
          if(response.statusCode ==200){
           // print(mapValue);
           await  appStore.loginStore(mapValue['name'], mapValue['user_id'],
            mapValue['message'], mapValue['email'], mapValue['profile_image'],mapValue['mobile'],context);
             context.goNamed(
                'HomeBottomBarPage',
                extra: <String, dynamic>{
                  kTransitionInfoKey: TransitionInfo(
                    hasTransition: true,
                    transitionType: PageTransitionType.rightToLeft,
                    duration: Duration(milliseconds: 300),
                  ),
                },
              );
          }else{
             toasty(context, "${mapValue['message']}",
                                    bgColor: Colors.red, textColor: Colors.black);
          }
      });
    
  }

  void _loginNow() {
 
    http
        .post(
      Uri.parse("${ApiUtils.BASE_URL}login"),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
        'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi",
      },
      body: jsonEncode(<String, dynamic>{
        'email':  _model.textController1.text,
        'password':  _model.textController2.text,
         'firebase_key': appStore.firebase_key.toString(),
      }),
    )
        .then((response) async{
           Navigator.of(context, rootNavigator: true).pop(false);
      Map mapValue = json.decode(response.body);
          if(response.statusCode ==200){
           // print(mapValue);
           await  appStore.loginStore(mapValue['name'], mapValue['user_id'],
            mapValue['message'], mapValue['email'], mapValue['profile_image'],mapValue['mobile'],context);
             context.goNamed(
                'HomeBottomBarPage',
                extra: <String, dynamic>{
                  kTransitionInfoKey: TransitionInfo(
                    hasTransition: true,
                    transitionType: PageTransitionType.rightToLeft,
                    duration: Duration(milliseconds: 300),
                  ),
                },
              );
          }else{
             toasty(context, "${mapValue['message']}",
                                    bgColor: Colors.red, textColor: Colors.black);
          }
      
        });
  }



  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false, // Prevents default back behavior
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          _exitApp(); // Handle back press
        }
      },
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: ClassifiedAppTheme.of(context).primaryBackground,
        body: SafeArea(
          top: true,
          child: Padding(
            padding: EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  
                   Stack(
      children: [
            // Center the logo
            Center(
      child: Padding(
        padding: const EdgeInsets.only(top: 16.0),
        child: SvgPicture.asset(
          "assets/images/logo_svg.svg",
          width: 160.0, // Adjust the width of the logo
          fit: BoxFit.contain,
        ),
      ),
            ),
            
            // Position the back icon at the top-left
            Positioned(
      top: 16.0, // Adjust the distance from the top
      left: 0.0, // Adjust the distance from the left
      child: InkWell(
        splashColor: Colors.transparent,
        focusColor: Colors.transparent,
        hoverColor: Colors.transparent,
        highlightColor: Colors.transparent,
        onTap: () async {
          setState(() {
            FFAppState().selectedPageIndex = 0;
          });
          context.goNamed(
            'HomeBottomBarPage',
            extra: <String, dynamic>{
              kTransitionInfoKey: TransitionInfo(
                hasTransition: true,
                transitionType: PageTransitionType.rightToLeft,
                duration: Duration(milliseconds: 300),
              ),
            },
          );
        },
        child: Container(
          width: 36.0, // Adjust the size of the container
          height: 36.0,
          decoration: BoxDecoration(
            color: ClassifiedAppTheme.of(context).primary,
            shape: BoxShape.circle,
          ),
          alignment: Alignment.center,
          child: SvgPicture.asset(
            'assets/images/arrow-left.svg',
            width: 16.0, // Adjust the width of the back icon
            height: 16.0,
            color: Colors.white,
            fit: BoxFit.contain,
          ),
        ),
      ),
            ),
      ],
            ),
            
            
                 
                  Form(
                    key: _model.formKey,
                    autovalidateMode: AutovalidateMode.disabled,
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                      
                      
                        Padding(
                          padding:
                              EdgeInsetsDirectional.fromSTEB(0.0, 2.0, 0.0, 0.0),
                          child: Text(
                            FFLocalizations.of(context).getText(
                              'esq7glag' /* Welcome back to the our shop p... */,
                            ),
                            style:
                                ClassifiedAppTheme.of(context).bodyMedium.override(
                                      fontFamily: 'Satoshi',
                                      color: Colors.black,
                                      fontSize: 17.0,
                                      fontWeight: FontWeight.w500,
                                      useGoogleFonts: false,
                                    ),
                          ),
                        ),
                        Padding(
                          padding:
                              EdgeInsetsDirectional.fromSTEB(0.0, 24.0, 0.0, 0.0),
                          child: Text(
                           FFLocalizations.of(context).getText(
                              'ksu45hz4' 
                            ),
                            style:
                                ClassifiedAppTheme.of(context).bodyMedium.override(
                                      fontFamily: 'Satoshi',
                                      fontSize: 17.0,
                                      fontWeight: FontWeight.w500,
                                      useGoogleFonts: false,
                                    ),
                          ),
                        ),
                        Padding(
                          padding:
                              EdgeInsetsDirectional.fromSTEB(0.0, 4.0, 0.0, 0.0),
                          child: TextFormField(
                            controller: _model.textController1,
                            focusNode: _model.textFieldFocusNode1,
                            textInputAction: TextInputAction.next,
                            obscureText: false,
                            decoration: InputDecoration(
                              isDense: true,
                              labelStyle:
                                  ClassifiedAppTheme.of(context).labelMedium,
                              hintText: FFLocalizations.of(context).getText(
                              'cjhh7fvh' 
                            ),
                              hintStyle: ClassifiedAppTheme.of(context)
                                  .labelMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    color: ClassifiedAppTheme.of(context)
                                        .secondaryText,
                                    fontSize: 17.0,
                                    fontWeight: FontWeight.w500,
                                    useGoogleFonts: false,
                                  ),
                              errorStyle: ClassifiedAppTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    color: Color(0xFFCD3232),
                                    useGoogleFonts: false,
                                  ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).info,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).primary,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).error,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              focusedErrorBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).error,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                            ),
                            style:
                                ClassifiedAppTheme.of(context).bodyMedium.override(
                                      fontFamily: 'Satoshi',
                                      fontSize: 17.0,
                                      fontWeight: FontWeight.w500,
                                      useGoogleFonts: false,
                                    ),
                            keyboardType: TextInputType.emailAddress,
                            cursorColor: Color(0xFF6753D6),
                            validator: _model.textController1Validator
                                .asValidator(context),
                          ),
                        ),
                        Padding(
                          padding:
                              EdgeInsetsDirectional.fromSTEB(0.0, 20.0, 0.0, 0.0),
                          child: Text(
                            FFLocalizations.of(context).getText(
                              'prj5vzjx' /* Password */,
                            ),
                            style:
                                ClassifiedAppTheme.of(context).bodyMedium.override(
                                      fontFamily: 'Satoshi',
                                      fontSize: 17.0,
                                      fontWeight: FontWeight.w500,
                                      useGoogleFonts: false,
                                    ),
                          ),
                        ),
                        Padding(
                          padding:
                              EdgeInsetsDirectional.fromSTEB(0.0, 4.0, 0.0, 0.0),
                          child: TextFormField(
                            controller: _model.textController2,
                            focusNode: _model.textFieldFocusNode2,
                            textInputAction: TextInputAction.done,
                            obscureText: !_model.passwordVisibility,
                            decoration: InputDecoration(
                              isDense: true,
                              labelStyle:
                                  ClassifiedAppTheme.of(context).labelMedium,
                              hintText: FFLocalizations.of(context).getText(
                                '3ijjbid3' /* Enter password */,
                              ),
                              hintStyle: ClassifiedAppTheme.of(context)
                                  .labelMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    color: ClassifiedAppTheme.of(context)
                                        .secondaryText,
                                    fontSize: 17.0,
                                    fontWeight: FontWeight.w500,
                                    useGoogleFonts: false,
                                  ),
                              errorStyle: ClassifiedAppTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    color: Color(0xFFCD3232),
                                    useGoogleFonts: false,
                                  ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).info,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).primary,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).error,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              focusedErrorBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).error,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              suffixIcon: InkWell(
                                onTap: () => setState(
                                  () => _model.passwordVisibility =
                                      !_model.passwordVisibility,
                                ),
                                focusNode: FocusNode(skipTraversal: true),
                                child: Icon(
                                  _model.passwordVisibility
                                      ? Icons.visibility_outlined
                                      : Icons.visibility_off_outlined,
                                  color: ClassifiedAppTheme.of(context).primaryText,
                                  size: 24.0,
                                ),
                              ),
                            ),
                            style:
                                ClassifiedAppTheme.of(context).bodyMedium.override(
                                      fontFamily: 'Satoshi',
                                      fontSize: 17.0,
                                      fontWeight: FontWeight.w500,
                                      useGoogleFonts: false,
                                    ),
                            keyboardType: TextInputType.visiblePassword,
                            cursorColor: ClassifiedAppTheme.of(context).primary,
                            validator: _model.textController2Validator
                                .asValidator(context),
                          ),
                        ),
                        Padding(
                          padding:
                              EdgeInsetsDirectional.fromSTEB(0.0, 20.0, 0.0, 0.0),
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              Theme(
                                data: ThemeData(
                                  checkboxTheme: CheckboxThemeData(
                                    visualDensity: VisualDensity.compact,
                                    materialTapTargetSize:
                                        MaterialTapTargetSize.shrinkWrap,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4.0),
                                    ),
                                  ),
                                  unselectedWidgetColor: Color(0xFFDCDCDC),
                                ),
                                child: Checkbox(
                                  value: _model.checkboxValue ??= false,
                                  onChanged: (newValue) async {
                                    setState(
                                        () => _model.checkboxValue = newValue!);
                                  },
                                  activeColor:
                                      ClassifiedAppTheme.of(context).primary,
                                  checkColor: ClassifiedAppTheme.of(context).info,
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  FFLocalizations.of(context).getText(
                                    'x0u8u78h' /* Remember me */,
                                  ),
                                  style: ClassifiedAppTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        color: ClassifiedAppTheme.of(context)
                                            .primaryText,
                                        fontSize: 17.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              Align(
                                alignment: AlignmentDirectional(1.0, 0.0),
                                child: InkWell(
                                  splashColor: Colors.transparent,
                                  focusColor: Colors.transparent,
                                  hoverColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                  onTap: () async {
                                    context.pushNamed(
                                      'ForgotPasswordPage',
                                      extra: <String, dynamic>{
                                        kTransitionInfoKey: TransitionInfo(
                                          hasTransition: true,
                                          transitionType:
                                              PageTransitionType.rightToLeft,
                                          duration: Duration(milliseconds: 300),
                                        ),
                                      },
                                    );
                                  },
                                  child: Text(
                                    FFLocalizations.of(context).getText(
                                      '88ke3mkp' /* Forgot password? */,
                                    ),
                                    style: ClassifiedAppTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'Satoshi',
                                          color: Color(0xFF030401),
                                          fontSize: 17.0,
                                          fontWeight: FontWeight.w500,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Padding(
                          padding:
                              EdgeInsetsDirectional.fromSTEB(0.0, 30.0, 0.0, 0.0),
                          child: wrapWithModel(
                            model: _model.appButtonModel,
                            updateCallback: () => setState(() {}),
                            child: AppButtonWidget(
                              text: FFLocalizations.of(context).getText(
                              'gwf8bwln'
                            ),
                              action: () async {
                                if (_model.formKey.currentState == null ||
                                    !_model.formKey.currentState!.validate()) {
                                  return;
                                }
              //                    toasty(context, "Server Issue, try later!",
              // bgColor: Colors.red, textColor: Colors.black);
              
                                // await actions.setLogin(
                                //   true,
                                // );
              
                                // context.goNamed(
                                //   'HomeBottomBarPage',
                                //   extra: <String, dynamic>{
                                //     kTransitionInfoKey: TransitionInfo(
                                //       hasTransition: true,
                                //       transitionType:
                                //           PageTransitionType.rightToLeft,
                                //       duration: Duration(milliseconds: 300),
                                //     ),
                                //   },
                                // );
              
                                setState(() {
                                  FFAppState().selectedPageIndex = 0;
                                });
                                 showLoadingDialog(context);
                                _loginNow();
                              },
                            ),
                          ),
                        ),
                      ]
                          .addToStart(SizedBox(height: 24.0))
                          .addToEnd(SizedBox(height: 24.0)),
                    ),
                  ),
                   Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.end,
                     children: [
                       Padding(
                         padding: const EdgeInsets.only(bottom: 12.0),
                         child: InkWell(
                                         splashColor: Colors.transparent,
                                         focusColor: Colors.transparent,
                                         hoverColor: Colors.transparent,
                                         highlightColor: Colors.transparent,
                                         onTap: () async {
                                           setState(() {
                                           FFAppState().selectedPageIndex = 0;
                                           });
                                           context.goNamed(
                                           'HomeBottomBarPage',
                                           extra: <String, dynamic>{
                          kTransitionInfoKey: TransitionInfo(
                          hasTransition: true,
                          transitionType: PageTransitionType.rightToLeft,
                          duration: Duration(milliseconds: 300),
                          ),
                                           },
                                           );
                                         },
                                         child: Row(
                                           children: [
                                             Text(
                              FFLocalizations.of(context).getText(
                                  'io0vhglm',
                                ),
                                style: ClassifiedAppTheme.of(context).bodyMedium.override(
                                  fontFamily: 'Satoshi',
                                  color: Colors.black,
                                  fontSize: 14.0,
                                  fontWeight: FontWeight.bold,
                                  useGoogleFonts: false,
                                  ),
                              ),
                                SizedBox(width: 8.0,),
                                           Container(
                          width: 20.0,
                          height: 20.0,
                          decoration: BoxDecoration(
                          color: ClassifiedAppTheme.of(context).primary,
                          shape: BoxShape.circle,
                          ),
                          alignment: Alignment.center, // Center the icon within the container
                          child: SvgPicture.asset(
                          'assets/images/arrow-right.svg',
                          width: 14.0,
                          height: 14.0,
                          color: Colors.white,
                          fit: BoxFit.contain,
                          ),
                                           ),
                                         
                         
                                           ],
                                         ),
                                         ),
                       ),
                     ],
                   ),
                  Align(
                    alignment: AlignmentDirectional(0.0, 1.0),
                    child: Padding(
                      padding:
                          EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 8.0),
                      child: RichText(
                        textScaler: MediaQuery.of(context).textScaler,
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: FFLocalizations.of(context).getText(
                                'tgbrocpa' /* Don’t have an account?  */,
                              ),
                              style: ClassifiedAppTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    color: ClassifiedAppTheme.of(context)
                                        .primaryText,
                                    fontSize: 17.0,
                                    fontWeight: FontWeight.w500,
                                    useGoogleFonts: false,
                                  ),
                            ),
                            TextSpan(
                              text: FFLocalizations.of(context).getText(
                                'it3rr9gi' /* Sign up */,
                              ),
                              style: TextStyle(
                                fontFamily: 'Satoshi',
                                color: ClassifiedAppTheme.of(context).primary,
                                fontWeight: FontWeight.w500,
                                fontSize: 17.0,
                              ),
                              mouseCursor: SystemMouseCursors.click,
                              recognizer: TapGestureRecognizer()
                                ..onTap = () async {
                                  context.pushNamed(
                                    'SignupPage',
                                    extra: <String, dynamic>{
                                      kTransitionInfoKey: TransitionInfo(
                                        hasTransition: true,
                                        transitionType:
                                            PageTransitionType.rightToLeft,
                                        duration: Duration(milliseconds: 300),
                                      ),
                                    },
                                  );
                                },
                            )
                          ],
                          style: ClassifiedAppTheme.of(context).bodyMedium,
                        ),
                      ),
                    ),
                  ),
          Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
      child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
      // Google Login Button
      if(Theme.of(context).platform == TargetPlatform.android)
      Expanded(
        child: ElevatedButton.icon(
          onPressed: () {
            loginWithGoogle();
            // Handle Google login action
          },
          icon: SvgPicture.asset(
            'assets/images/google_logo.svg', // Your Google logo SVG
            height: 24.0,
            width: 24.0,
          ),
          label: Text(
            'Google',
            style: TextStyle(
              fontSize: 16.0,
              fontWeight: FontWeight.w600,
            ),
          ),
          style: ElevatedButton.styleFrom(
            padding: EdgeInsets.symmetric(vertical: 12.0),
            backgroundColor: Colors.white,
            foregroundColor: Colors.black,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.0),
              side: BorderSide(color: Colors.grey.shade300),
            ),
          ),
        ),
      ),
      //SizedBox(width: 16.0), // Space between buttons
      // Facebook Login Button
      if (Theme.of(context).platform == TargetPlatform.iOS)
      Expanded(
        child: ElevatedButton.icon(
          onPressed: () async {
            try {
              final AuthorizationCredentialAppleID credential =
            await SignInWithApple.getAppleIDCredential(
          scopes: [
            AppleIDAuthorizationScopes.email,
            AppleIDAuthorizationScopes.fullName,
          ],
              );
            
              if (credential.userIdentifier != null) {
          print("Apple Sign-In Success: ${credential.userIdentifier}");
          print("Apple Sign-In Email: ${credential.email}");
          print("Apple Sign-In Full Name: ${credential.givenName}");
          print("Apple Sign-In Full Name: ${credential}");
            
          // Handle successful login
          if (credential.email != null) {
            signupApple(credential.givenName.toString(), credential.email.toString(), credential.userIdentifier.toString());
          } else {
            authApple(credential.userIdentifier.toString());
          }
              } else {
          // Handle login failure
          toasty(context, "Apple Sign-In failed", bgColor: Colors.red, textColor: Colors.black);
              }
            } catch (e) {
              // Handle exception
              toasty(context, "An error occurred: $e", bgColor: Colors.red, textColor: Colors.black);
            }
          },
      
          
          icon: Icon(
            Icons.apple,
            size: 24.0,
            color: Colors.white,
          ),
          label: Text(
            'Apple',
            style: TextStyle(
              fontSize: 16.0,
              fontWeight: FontWeight.w600,
            ),
          ),
          style: ElevatedButton.styleFrom(
            padding: EdgeInsets.symmetric(vertical: 12.0),
            backgroundColor: Colors.black, // Facebook blue color
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.0),
            ),
          ),
        ),
      ),
            ],
      ),
            ),
            
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
