import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:soho_souk/Classified_App/classified_app_util.dart';
import 'package:soho_souk/main.dart';
import 'package:soho_souk/models/Message.dart';

import '../../Classified_App/classified_app_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'chat_details_page_model.dart';
export 'chat_details_page_model.dart';

class ChatDetailsPageWidget extends StatefulWidget {
  const ChatDetailsPageWidget({super.key,required this.vendor_id,required this.vendor_name});
  final int vendor_id;
  final String vendor_name;
  @override
  State<ChatDetailsPageWidget> createState() => _ChatDetailsPageWidgetState();
}

class _ChatDetailsPageWidgetState extends State<ChatDetailsPageWidget> {
  late ChatDetailsPageModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => ChatDetailsPageModel());

    _model.textController ??= TextEditingController();
    _model.textFieldFocusNode ??= FocusNode();
    print('vendor_id: ${ appStore.user_id.toString()+
      widget.vendor_id.toString()}');
  }
  bool showSend = false;

  void onSendMessage() async {
    if (_model.textController.text.isEmpty) {
      return;
    }
    String message = _model.textController.text;
    _model.textController!.clear();
    final Timestamp timestamp = Timestamp.now();
    List<String> ids = [
      appStore.user_id.toString(),
      widget.vendor_id.toString()
    ];
    ids.sort();
    String? chatRoomId = ids.join('_');
    showSend = false;
    Message newMessage = Message(
        message: message,
        reciver_id: widget.vendor_id.toString(),
        fromMe: false,
        sender_id: appStore.user_id.toString(),
        timestamp: timestamp);

    await FirebaseFirestore.instance
        .collection("chat")
        .doc(chatRoomId)
        .collection("messages")
        .add(newMessage.toMap());
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  Widget getView() {
    return StreamBuilder(
        stream: getMessage(),
        builder: (context, snapshot) {
          if (snapshot.hasError) {
            return Center(
              child: Text('Error: ${snapshot.error}'),
            );
          }

          if (!snapshot.hasData) {
            return Center(
              child: CircularProgressIndicator(),
            );
          }

          return ListView.builder(
            itemCount: snapshot.data!.docs.length,
            padding: EdgeInsets.symmetric(vertical: 10),
            // controller: scrollController,
            // reverse: true,
            itemBuilder: (context, index) {
              return Container(
                padding: EdgeInsets.symmetric(horizontal: 14, vertical: 10),
                child: Align(
                  alignment: (appStore.user_id.toString() ==
                          snapshot.data!.docs[index]['sender_id'])
                      ? Alignment.topRight
                      : Alignment.topLeft,
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      color: (appStore.user_id.toString() ==
                              snapshot.data!.docs[index]['sender_id'])
                          ? Colors.blue[200]
                          : Colors.grey[200],
                    ),
                    padding: EdgeInsets.all(16),
                    child: Text(
                      snapshot.data!.docs[index]['message'],
                      style: TextStyle(fontSize: 15),
                    ),
                  ),
                ),
              );
              // Message item = Message(
              //   message: snapshot.data!.docs[index]['message'],
              //   reciver_id: snapshot.data!.docs[index]['reciver_id'],
              //   fromMe: appStore.user_id.toString() ==
              //           snapshot.data!.docs[index]['sender_id']
              //       ? true
              //       : false,
              //   sender_id: snapshot.data!.docs[index]['sender_id'],
              //   timestamp: snapshot.data!.docs[index]['timestamp'],
              // );
              // return buildListItemView(index, item);
            },
          );
        });
  }

  Stream<QuerySnapshot> getMessage() {
    List<String> ids = [
      appStore.user_id.toString(),
      widget.vendor_id.toString()
    ];
    ids.sort();
    String? chatRoomId = ids.join('_');
    return FirebaseFirestore.instance
        .collection("chat")
        .doc(chatRoomId)
        .collection("messages")
        .orderBy('timestamp', descending: false)
        .snapshots();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: ClassifiedAppTheme.of(context).primaryBackground,
        body: SafeArea(
          top: true,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 16.0, 16.0),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Container(
                      width: 40.0,
                      height: 40.0,
                      decoration: BoxDecoration(
                        color: ClassifiedAppTheme.of(context).tertiary,
                        shape: BoxShape.circle,
                      ),
                      alignment: AlignmentDirectional(0.0, 0.0),
                      child: InkWell(
                        splashColor: Colors.transparent,
                        focusColor: Colors.transparent,
                        hoverColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        onTap: () async {
                          context.safePop();
                        },
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8.0),
                          child: SvgPicture.asset(
                            'assets/images/arrow-left.svg',
                            width: 24.0,
                            height: 24.0,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                    // Padding(
                    //   padding:
                    //       EdgeInsetsDirectional.fromSTEB(12.0, 0.0, 16.0, 0.0),
                    //   child: Image.asset(
                    //     'assets/images/topratedOne.png',
                    //     width: 50.0,
                    //     height: 50.0,
                    //     fit: BoxFit.cover,
                    //   ),
                    // ),
                    SizedBox(
                      width: 15.0,
                    ),
                    Expanded(
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${widget.vendor_name}',
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 18.0,
                                  fontWeight: FontWeight.w600,
                                  useGoogleFonts: false,
                                ),
                          ),
                          Text(
                            FFLocalizations.of(context).getText(
                              'ijjbh1wd' /* Online */,
                            ),
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  color: Color(0xFF04B155),
                                  fontSize: 13.0,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ],
                      ),
                    ),
                    // ClipRRect(
                    //   borderRadius: BorderRadius.circular(8.0),
                    //   child: SvgPicture.asset(
                    //     'assets/images/video.svg',
                    //     width: 24.0,
                    //     height: 24.0,
                    //     fit: BoxFit.cover,
                    //   ),
                    // ),
                    // Padding(
                    //   padding:
                    //       EdgeInsetsDirectional.fromSTEB(24.0, 0.0, 0.0, 0.0),
                    //   child: SvgPicture.asset(
                    //     'assets/images/call-rounded.svg',
                    //     width: 24.0,
                    //     height: 24.0,
                    //     fit: BoxFit.cover,
                    //   ),
                    // ),
                  ],
                ),
              ),
              Divider(
                height: 0.0,
                thickness: 1.0,
                color: ClassifiedAppTheme.of(context).info,
              ),
               Expanded(
              child: getView(),
            ),
              // Expanded(
              //   child: Padding(
              //     padding:
              //         EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 16.0, 0.0),
              //     child: ListView(
              //       padding: EdgeInsets.zero,
              //       shrinkWrap: true,
              //       scrollDirection: Axis.vertical,
              //       children: [
                    
              //         // Text(
              //         //   FFLocalizations.of(context).getText(
              //         //     'cxak82tg' /* TODAY */,
              //         //   ),
              //         //   textAlign: TextAlign.center,
              //         //   style: ClassifiedAppTheme.of(context).bodyMedium.override(
              //         //         fontFamily: 'Satoshi',
              //         //         fontSize: 15.0,
              //         //         fontWeight: FontWeight.w500,
              //         //         useGoogleFonts: false,
              //         //       ),
              //         // ),
              //         Align(
              //           alignment: AlignmentDirectional(1.0, 0.0),
              //           child: Padding(
              //             padding: EdgeInsetsDirectional.fromSTEB(
              //                 0.0, 24.0, 0.0, 0.0),
              //             child: Container(
              //               width: 119.0,
              //               height: 49.0,
              //               decoration: BoxDecoration(
              //                 color: ClassifiedAppTheme.of(context).primary,
              //                 borderRadius: BorderRadius.only(
              //                   bottomLeft: Radius.circular(12.0),
              //                   bottomRight: Radius.circular(12.0),
              //                   topLeft: Radius.circular(12.0),
              //                   topRight: Radius.circular(0.0),
              //                 ),
              //               ),
              //               alignment: AlignmentDirectional(0.0, 0.0),
              //               child: Text(
              //                 FFLocalizations.of(context).getText(
              //                   'nn6tbxeq' /* Hi, Esther 👋🏻 */,
              //                 ),
              //                 style: ClassifiedAppTheme.of(context)
              //                     .bodyMedium
              //                     .override(
              //                       fontFamily: 'Satoshi',
              //                       color: Colors.white,
              //                       fontSize: 17.0,
              //                       useGoogleFonts: false,
              //                     ),
              //               ),
              //             ),
              //           ),
              //         ),
              //         Align(
              //           alignment: AlignmentDirectional(1.0, 0.0),
              //           child: Padding(
              //             padding: EdgeInsetsDirectional.fromSTEB(
              //                 0.0, 16.0, 0.0, 0.0),
              //             child: Container(
              //               width: 276.0,
              //               height: 96.0,
              //               decoration: BoxDecoration(
              //                 color: ClassifiedAppTheme.of(context).primary,
              //                 borderRadius: BorderRadius.only(
              //                   bottomLeft: Radius.circular(12.0),
              //                   bottomRight: Radius.circular(12.0),
              //                   topLeft: Radius.circular(12.0),
              //                   topRight: Radius.circular(0.0),
              //                 ),
              //               ),
              //               alignment: AlignmentDirectional(0.0, 0.0),
              //               child: Text(
              //                 FFLocalizations.of(context).getText(
              //                   '30mgogg7' /* I'm looking for information ab... */,
              //                 ),
              //                 style: ClassifiedAppTheme.of(context)
              //                     .bodyMedium
              //                     .override(
              //                       fontFamily: 'Satoshi',
              //                       color: Colors.white,
              //                       fontSize: 17.0,
              //                       useGoogleFonts: false,
              //                     ),
              //               ),
              //             ),
              //           ),
              //         ),
              //         Align(
              //           alignment: AlignmentDirectional(1.0, 0.0),
              //           child: Padding(
              //             padding: EdgeInsetsDirectional.fromSTEB(
              //                 0.0, 8.0, 0.0, 0.0),
              //             child: Text(
              //               FFLocalizations.of(context).getText(
              //                 '4pfjxfzo' /* 12.00  */,
              //               ),
              //               textAlign: TextAlign.end,
              //               style: ClassifiedAppTheme.of(context)
              //                   .bodyMedium
              //                   .override(
              //                     fontFamily: 'Satoshi',
              //                     color: ClassifiedAppTheme.of(context)
              //                         .secondaryText,
              //                     fontSize: 15.0,
              //                     fontWeight: FontWeight.w500,
              //                     useGoogleFonts: false,
              //                   ),
              //             ),
              //           ),
              //         ),
              //         Align(
              //           alignment: AlignmentDirectional(-1.0, 0.0),
              //           child: Padding(
              //             padding: EdgeInsetsDirectional.fromSTEB(
              //                 0.0, 24.0, 0.0, 0.0),
              //             child: Container(
              //               width: 272.0,
              //               height: 64.0,
              //               decoration: BoxDecoration(
              //                 color: Colors.white,
              //                 boxShadow: [
              //                   BoxShadow(
              //                     blurRadius: 4.0,
              //                     color: Color(0x17000000),
              //                     offset: Offset(0.0, 2.0),
              //                   )
              //                 ],
              //                 borderRadius: BorderRadius.only(
              //                   bottomLeft: Radius.circular(12.0),
              //                   bottomRight: Radius.circular(12.0),
              //                   topLeft: Radius.circular(0.0),
              //                   topRight: Radius.circular(12.0),
              //                 ),
              //               ),
              //               alignment: AlignmentDirectional(0.0, 0.0),
              //               child: Text(
              //                 FFLocalizations.of(context).getText(
              //                   'vk0455zu' /* Hi. ronald! of course, the doo... */,
              //                 ),
              //                 style: ClassifiedAppTheme.of(context)
              //                     .bodyMedium
              //                     .override(
              //                       fontFamily: 'Satoshi',
              //                       color: ClassifiedAppTheme.of(context)
              //                           .primaryText,
              //                       fontSize: 17.0,
              //                       useGoogleFonts: false,
              //                     ),
              //               ),
              //             ),
              //           ),
              //         ),
              //         Align(
              //           alignment: AlignmentDirectional(-1.0, 0.0),
              //           child: Padding(
              //             padding: EdgeInsetsDirectional.fromSTEB(
              //                 0.0, 8.0, 0.0, 0.0),
              //             child: Text(
              //               FFLocalizations.of(context).getText(
              //                 'h2h9ll5w' /* 12.00  */,
              //               ),
              //               textAlign: TextAlign.end,
              //               style: ClassifiedAppTheme.of(context)
              //                   .bodyMedium
              //                   .override(
              //                     fontFamily: 'Satoshi',
              //                     color: ClassifiedAppTheme.of(context)
              //                         .secondaryText,
              //                     fontSize: 15.0,
              //                     fontWeight: FontWeight.w500,
              //                     useGoogleFonts: false,
              //                   ),
              //             ),
              //           ),
              //         ),
              //       ],
              //     ),
              //   ),
              // ),
              Container(
                width: double.infinity,
                height: 92.0,
                decoration: BoxDecoration(
                  color: ClassifiedAppTheme.of(context).secondaryBackground,
                  boxShadow: [
                    BoxShadow(
                      blurRadius: 4.0,
                      color: Color(0x33000000),
                      offset: Offset(2.0, 0.0),
                    )
                  ],
                ),
                child: Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      // Padding(
                      //   padding:
                      //       EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 8.0, 0.0),
                      //   child: SvgPicture.asset(
                      //     'assets/images/add-circle.svg',
                      //     width: 30.0,
                      //     height: 30.0,
                      //     fit: BoxFit.cover,
                      //   ),
                      // ),
                      SizedBox(
                        height: 26.0,
                        child: VerticalDivider(
                          width: 0.0,
                          thickness: 1.0,
                          color: ClassifiedAppTheme.of(context).info,
                        ),
                      ),
                      Expanded(
                        child: Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              4.0, 0.0, 0.0, 0.0),
                          child: TextFormField(
                            controller: _model.textController,
                            focusNode: _model.textFieldFocusNode,
                            textInputAction: TextInputAction.next,
                            obscureText: false,
                            decoration: InputDecoration(
                              labelStyle:
                                  ClassifiedAppTheme.of(context).labelMedium,
                              hintText: FFLocalizations.of(context).getText(
                                '3fy1c69v' /* Write a reply... */,
                              ),
                              hintStyle: ClassifiedAppTheme.of(context)
                                  .labelMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    fontSize: 18.0,
                                    useGoogleFonts: false,
                                  ),
                              errorStyle: ClassifiedAppTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    color: ClassifiedAppTheme.of(context).error,
                                    useGoogleFonts: false,
                                  ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: Colors.transparent,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).primary,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).error,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              focusedErrorBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).error,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              contentPadding: EdgeInsetsDirectional.fromSTEB(
                                  16.0, 0.0, 0.0, 0.0),
                            ),
                            style: ClassifiedAppTheme.of(context).bodyMedium,
                            keyboardType: TextInputType.emailAddress,
                            cursorColor: ClassifiedAppTheme.of(context).primary,
                            validator: _model.textControllerValidator
                                .asValidator(context),
                          ),
                        ),
                      ),
                      Padding(
                        padding:
                            EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 0.0, 0.0),
                        child: GestureDetector(
                          onTap: () => onSendMessage(),
                          child: Container(
                            width: 44.0,
                            height: 44.0,
                            decoration: BoxDecoration(
                              color: ClassifiedAppTheme.of(context).primary,
                              shape: BoxShape.circle,
                            ),
                            alignment: AlignmentDirectional(0.0, 0.0),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(8.0),
                              child: SvgPicture.asset(
                                'assets/images/send-2.svg',
                                width: 24.0,
                                height: 24.0,
                                fit: BoxFit.contain,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
