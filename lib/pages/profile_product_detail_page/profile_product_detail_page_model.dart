import '../../Classified_App/classified_app_model.dart';
import 'profile_product_detail_page_widget.dart'
    show ProfileProductDetailPageWidget;
import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';

class ProfileProductDetailPageModel
    extends ClassifiedAppModel<ProfileProductDetailPageWidget> {
  ///  State fields for stateful widgets in this page.

  final unfocusNode = FocusNode();
  // State field(s) for PageView widget.
  PageController? pageViewController;

  int get pageViewCurrentIndex => pageViewController != null &&
          pageViewController!.hasClients &&
          pageViewController!.page != null
      ? pageViewController!.page!.round()
      : 0;
  // State field(s) for Expandable widget.
  late ExpandableController expandableController;

  /// Initialization and disposal methods.

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {
    unfocusNode.dispose();
    expandableController.dispose();
  }

  /// Action blocks are added here.

  /// Additional helper methods are added here.
}
