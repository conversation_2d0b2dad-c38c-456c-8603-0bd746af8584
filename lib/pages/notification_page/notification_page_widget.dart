import 'dart:convert';

import 'package:soho_souk/ApiUtils.dart';
import 'package:soho_souk/Classified_App/classified_app_util.dart';
import 'package:soho_souk/main.dart';

import '../../Classified_App/classified_app_model.dart';
import '../../Classified_App/classified_app_theme.dart';
import '../../app_state.dart';
import '/pages/app_bar/app_bar_widget.dart';
import 'package:http/http.dart' as http;
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'notification_page_model.dart';
export 'notification_page_model.dart';

class NotificationPageWidget extends StatefulWidget {
  const NotificationPageWidget({super.key});

  @override
  State<NotificationPageWidget> createState() => _NotificationPageWidgetState();
}

class _NotificationPageWidgetState extends State<NotificationPageWidget> {
  late NotificationPageModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    if(appStore.logined)
    fetchNotifications();
    _model = createModel(context, () => NotificationPageModel());
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }
  List<PushNotification> list =[];
void fetchNotifications() async {


    try {

      http.get(Uri.parse("${ApiUtils.BASE_URL}get-push-notification/${appStore.user_id}"),
    headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
          }
   ).then((response) {
      if (response.statusCode == 200) {
        // Map mapValue = json.decode(response.body);
        setState(() {
        List<dynamic> data = json.decode(response.body);
        print("data ${data}");
        list = data.map((json) => PushNotification.fromJson(json)).toList();
          
       
        });
      }
    });
      
    } catch (e) {
      print('Error fetching notifications: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: ClassifiedAppTheme.of(context).primaryBackground,
        body: SafeArea(
          top: true,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              Padding(
      padding: EdgeInsetsDirectional.fromSTEB(16.0, 21.0, 16.0, 0.0),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          
          Padding(
            padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 5.0),
            child: AutoSizeText(
              valueOrDefault<String>(
                FFLocalizations.of(context).getText(
                                        'g881f0w5' /* Notification */,
                                      ),"Notifications",
               
              ),
              style: ClassifiedAppTheme.of(context).bodyMedium.override(
                    fontFamily: 'Satoshi',
                    color: ClassifiedAppTheme.of(context).primaryText,
                    fontSize: 20.0,
                    fontWeight: FontWeight.bold,
                    useGoogleFonts: false,
                  ),
            ),
          ),
         
        ],
      ),
    ),
              Expanded(
                child: Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(10.0, 0.0, 10.0, 0.0),
                  child: Builder(
                    builder: (context) {
                      // final notificationList =
                      //     FFAppState().notificationList.toList();
                        return ListView.separated(
                        padding: EdgeInsets.fromLTRB(
                          0,
                          16.0,
                          0,
                          20.0,
                        ),
                        shrinkWrap: true,
                        scrollDirection: Axis.vertical,
                        itemCount: list.length,
                        separatorBuilder: (_, __) => SizedBox(height: 16.0),
                        itemBuilder: (context, notificationListIndex) {
                          final notificationListItem =
                              list[notificationListIndex];
                          return Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(
                                6.0, 0.0, 6.0, 0.0),
                            child: Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                color: ClassifiedAppTheme.of(context)
                                    .secondaryBackground,
                                boxShadow: [
                                  BoxShadow(
                                    blurRadius: 4.0,
                                    color: Color(0x32000000),
                                    offset: Offset(0.0, 2.0),
                                  )
                                ],
                                borderRadius: BorderRadius.circular(20.0),
                              ),
                              child: Padding(
                                padding: EdgeInsets.all(16.0),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      width: 40.0,
                                      height: 40.0,
                                      decoration: BoxDecoration(
                                        color: ClassifiedAppTheme.of(context)
                                            .secondaryBackground,
                                        boxShadow: [
                                          BoxShadow(
                                            blurRadius: 4.0,
                                            color: Color(0x33000000),
                                            offset: Offset(0.0, 2.0),
                                          )
                                        ],
                                        shape: BoxShape.circle,
                                      ),
                                      alignment: AlignmentDirectional(0.0, 0.0),
                                      child: SvgPicture.asset(
                                        'assets/images/notification_primary.svg',
                                        width: 24.0,
                                        height: 24.0,
                                        fit: BoxFit.contain,
                                         color: ClassifiedAppTheme.of(context).primary,
                                      ),
                                    ),
                                    Expanded(
                                      child: Padding(
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            16.0, 0.0, 0.0, 0.0),
                                        child: Column(
                                          mainAxisSize: MainAxisSize.max,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            AutoSizeText(
                                              notificationListItem.title,
                                              textAlign: TextAlign.start,
                                              maxLines: 2,
                                              style:
                                                  ClassifiedAppTheme.of(context)
                                                      .bodyMedium
                                                      .override(
                                                        fontFamily: 'Satoshi',
                                                        fontSize: 20.0,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        useGoogleFonts: false,
                                                      ),
                                              minFontSize: 14.0,
                                            ),
                                            Padding(
                                              padding: EdgeInsetsDirectional
                                                  .fromSTEB(0.0, 8.0, 0.0, 0.0),
                                              child: Text(
                                                notificationListItem.description,
                                                style:
                                                    ClassifiedAppTheme.of(context)
                                                        .bodyMedium
                                                        .override(
                                                          fontFamily: 'Satoshi',
                                                          color: ClassifiedAppTheme
                                                                  .of(context)
                                                              .secondaryText,
                                                          fontSize: 17.0,
                                                          fontWeight:
                                                              FontWeight.w500,
                                                          useGoogleFonts: false,
                                                        ),
                                              ),
                                            ),
                                            
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
class PushNotification {

  final String title;
  final String description;

  PushNotification({

    required this.title,
    required this.description,
  });

  factory PushNotification.fromJson(Map<String, dynamic> json) {
    return PushNotification(

      title: json['title'],
      description: json['description'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
   
      'title': title,
      'description': description,
    };
  }
}