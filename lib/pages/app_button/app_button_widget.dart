import '../../Classified_App/classified_app_model.dart';
import '../../Classified_App/classified_app_theme.dart';
import 'package:flutter/material.dart';
import '../../Classified_App/classified_app_widgets.dart';
import 'app_button_model.dart';
export 'app_button_model.dart';

class AppButtonWidget extends StatefulWidget {
  const AppButtonWidget({
    super.key,
    this.text,
    this.action,
  });

  final String? text;
  final Future Function()? action;

  @override
  State<AppButtonWidget> createState() => _AppButtonWidgetState();
}

class _AppButtonWidgetState extends State<AppButtonWidget> {
  late AppButtonModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => AppButtonModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FFButtonWidget(
      onPressed: () async {
        await widget.action?.call();
      },
      text: widget.text!,
      options: FFButtonOptions(
        width: double.infinity,
        height: 56.0,
        padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
        iconPadding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 0.0, 0.0),
        color: ClassifiedAppTheme.of(context).primary,
        textStyle: ClassifiedAppTheme.of(context).titleSmall.override(
              fontFamily: 'Satoshi',
              color: Colors.white,
              fontSize: 18.0,
              fontWeight: FontWeight.bold,
              useGoogleFonts: false,
            ),
        elevation: 0.0,
        borderSide: BorderSide(
          color: Colors.transparent,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(12.0),
      ),
    );
  }
}
