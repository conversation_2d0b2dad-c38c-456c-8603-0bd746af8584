import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:soho_souk/ApiUtils.dart';
import 'package:soho_souk/Classified_App/classified_app_theme.dart';
import 'package:soho_souk/Classified_App/classified_app_util.dart';
import 'package:soho_souk/models/BlogModel.dart';
import 'package:html/parser.dart';

class BlogListPage extends StatefulWidget {
  @override
  _BlogListPageState createState() => _BlogListPageState();
}

class _BlogListPageState extends State<BlogListPage> {
   List<BlogModel> blogs = [];
  

  @override
  void initState() {
    super.initState();
     getBlog();
    // Add any initialization logic here
  }
 bool? loader = true;
  String stripHtml(String htmlString) {
  final document = parse(htmlString);
  return document.body?.text ?? '';
}


    getBlog() async{
    try {
      final response = await http.get(
          Uri.parse("${ApiUtils.BASE_URL}get-blog"),
          headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
          });
      if (response.statusCode == 200) {
        setState(() {
          loader=false;
       blogs = (json.decode(response.body) as List)
            .map((data) => new BlogModel.fromJson(data))
            .toList();
      
        });
      } else {
        print('Error Occurred');
      }
    } catch (e) {
      print('Error Occurred product' + e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: ClassifiedAppTheme.of(context).primaryColor,
        title: Text('Blog '),
        centerTitle: true,
      ),
      body: loader == true ? 
      Container(
        color: Colors.white,
        child: Center(
          child: SpinKitRipple(
                color: ClassifiedAppTheme.of(context).primary,
              ),
        ),
      ):Container(
        color: Colors.white,
        child: ListView.builder(
          padding: EdgeInsets.all(8.0),
          itemCount: blogs.length,
          itemBuilder: (context, index) {
            return GestureDetector(
                                onTap: () {
                                      context.pushNamed(
                                'BlogDetailPage',
                                queryParameters: {
                                  'blog': jsonEncode(blogs[index].toJson()), // Serialize PostModel
                                 
                                }.withoutNulls,
                                extra: <String, dynamic>{
                                  kTransitionInfoKey: TransitionInfo(
                                    hasTransition: true,
                                    transitionType:
                                        PageTransitionType.rightToLeft,
                                    duration: Duration(milliseconds: 300),
                                  ),
                                },
                              );
                                },
              child: Container(
                margin: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.5),
                      spreadRadius: 2,
                      blurRadius: 5,
                      offset: Offset(0, 3),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Image.network(ApiUtils.admin_files+blogs[index].image!),
                    SizedBox(height: 10),
                    Text(
                      blogs[index].title!,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: ClassifiedAppTheme.of(context).primaryColor,
                      ),
                    ),
               
                  
                   Container(
                child: Text(
                  stripHtml(blogs[index].description!), // Convert HTML to plain text
                  maxLines: 2, // Limit to 2 lines
                  overflow: TextOverflow.ellipsis, // Add ellipsis if overflowed
                  style: ClassifiedAppTheme.of(context).bodyMedium.override(
                        fontFamily: 'Satoshi',
                        color: ClassifiedAppTheme.of(context).secondaryText,
                        fontSize: 10.0,
                        fontWeight: FontWeight.w500,
                        useGoogleFonts: false,
                      ),
                ),
              ),
              
                      SizedBox(height: 10),
                      Text(
                      'Admin',
                      style: TextStyle(fontSize: 14, color: Colors.black54),
                      ),
                      SizedBox(height: 10),
                    Text(
                      blogs[index].date!,
                      style: TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}