import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:soho_souk/ApiUtils.dart';
import 'package:soho_souk/Classified_App/classified_app_theme.dart';
import 'package:soho_souk/models/BlogModel.dart';


class BlogDetailPage extends StatelessWidget {

  final BlogModel? blog;

  BlogDetailPage({
    this.blog,
  });
String preprocessHtml(String html) {
  final regex = RegExp(r'style="[^"]*"'); // Matches the style attribute
  return html.replaceAll(regex, '');
}
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          blog?.title ?? 'Default Title',
          style: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: Colors.white,
          ),
        ),
        backgroundColor: ClassifiedAppTheme.of(context).primaryColor,
      ),
      
      body:  SingleChildScrollView(
        child: Container(
                  // margin: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
                  padding: EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.5),
                        spreadRadius: 2,
                        blurRadius: 5,
                        offset: Offset(0, 3),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Image.network(ApiUtils.admin_files+blog!.image!),
                      SizedBox(height: 10),
                      Text(
                        blog!.title!,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: ClassifiedAppTheme.of(context).primaryColor,
                        ),
                      ),
                   SizedBox(height: 10),
                        Text(
                        'Admin',
                        style: TextStyle(fontSize: 14, color: Colors.black54),
                        ),
                        SizedBox(height: 10),
                      Text(
                        blog!.date!,
                        style: TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                    
                     Container(
                  child: HtmlWidget(
  preprocessHtml("${blog!.description}"),
  textStyle: ClassifiedAppTheme.of(context)
      .bodyMedium
      .override(
        fontFamily: 'Satoshi',
        color: ClassifiedAppTheme.of(context).secondaryText,
        fontSize: 14.0, // Set desired font size
        fontWeight: FontWeight.w500,
        useGoogleFonts: false,
      ),
),




                ),
                
                      
                    ],
                  ),
                ),
      ),
    );
  }
}