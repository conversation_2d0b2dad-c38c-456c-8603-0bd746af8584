import 'package:soho_souk/Classified_App/classified_app_util.dart';

import 'chats_page_widget.dart' show ChatsPageWidget;
import 'package:flutter/material.dart';

class ChatsPageModel extends ClassifiedAppModel<ChatsPageWidget> {
  ///  State fields for stateful widgets in this page.

  final unfocusNode = FocusNode();
  // State field(s) for TabBar widget.
  TabController? tabBarController;
  int get tabBarCurrentIndex =>
      tabBarController != null ? tabBarController!.index : 0;

  /// Initialization and disposal methods.

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {
    unfocusNode.dispose();
    tabBarController?.dispose();
  }

  /// Action blocks are added here.

  /// Additional helper methods are added here.
}
