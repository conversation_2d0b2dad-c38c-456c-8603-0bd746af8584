import '../../Classified_App/classified_app_model.dart';
import 'favourite_page_widget.dart' show FavouritePageWidget;
import 'package:flutter/material.dart';

class FavouritePageModel extends ClassifiedAppModel<FavouritePageWidget> {
  ///  State fields for stateful widgets in this page.

  final unfocusNode = FocusNode();

  /// Initialization and disposal methods.

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {
    unfocusNode.dispose();
  }

  /// Action blocks are added here.

  /// Additional helper methods are added here.
}
