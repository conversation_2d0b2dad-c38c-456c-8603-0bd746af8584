import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:nb_utils/nb_utils.dart';
import 'dart:convert';

import 'package:soho_souk/ApiUtils.dart';
import 'package:soho_souk/Classified_App/classified_app_theme.dart';
import 'package:soho_souk/main.dart';
import 'package:soho_souk/widget/loader.dart';

class ReportBottomSheet extends StatefulWidget {
  final int postId;

  const ReportBottomSheet({required this.postId});

  @override
  _ReportBottomSheetState createState() => _ReportBottomSheetState();
}

class _ReportBottomSheetState extends State<ReportBottomSheet> {
  List<dynamic> reportCategories = [];
  String? selectedCategory;
  TextEditingController commentController = TextEditingController();

  @override
  void initState() {
    super.initState();
    fetchReportCategories();
  }

  Future<void> fetchReportCategories() async {
    try {
      final response = await http.get(Uri.parse('${ApiUtils.BASE_URL}get-report-category'));
      if (response.statusCode == 200) {
        setState(() {
          reportCategories = json.decode(response.body);
        });
      } else {
        print("Failed to fetch categories: ${response.statusCode}");
      }
    } catch (e) {
      print("Error fetching categories: $e");
    }
  }

  Future<void> submitReport() async {
    if (!appStore.logined) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('You need to be logged in to report a post.')),
      );
       toasty(context, "You need to be logged in to report a post..",
                                    bgColor: Colors.red, textColor: Colors.black);
      return;
    }
    if (selectedCategory == null || commentController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Please select a Reason and add a comment.')),
      );
       toasty(context, "Please select a Reason and add a comment.",
                                    bgColor: Colors.red, textColor: Colors.black);
      return;
    }

    try {
       showLoadingDialog(context);
      final response = await http.post(
        Uri.parse('${ApiUtils.BASE_URL}report-post'),
          headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
          'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
        },
        body: jsonEncode(<String, dynamic>{
          'category_id': selectedCategory,
          'description': commentController.text,
          'user_id': appStore.user_id, // Replace with actual user ID
          'post_id': widget.postId.toString(),
        },
      ));
       
      if (response.statusCode == 200) {
        Navigator.pop(context);
        toasty(context, "Report submitted successfully.",
                                    bgColor: Colors.green, textColor: Colors.black);
      } else {
        print("Failed to submit report: ${response.statusCode}");
            toasty(context, "Failed to submit report. Please try again.",
                                    bgColor: Colors.red, textColor: Colors.black);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to submit report. Please try again.')),
        );
      }
    } catch (e) {
      print("Error submitting report: $e");
       toasty(context, "An error occurred. Please try again later.",
                                    bgColor: Colors.red, textColor: Colors.black);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('An error occurred. Please try again later.')),
      );
    }finally{
         Navigator.of(context, rootNavigator: true).pop(false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: MediaQuery.of(context).viewInsets,
      child: Container(
        padding: EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16.0)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Report Post',
                  style: TextStyle(
                    fontSize: 18.0,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.close),color: ClassifiedAppTheme.of(context).primary,
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            SizedBox(height: 16.0),
            DropdownButtonFormField(
              dropdownColor: Colors.white,
              items: reportCategories.map((category) {
                return DropdownMenuItem(
                  value: category['id'].toString(),
                  child: Text(
                    category['category'],
                    style: TextStyle(color: Colors.black),
                  ),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  selectedCategory = value as String?;
                });
              },
              decoration: InputDecoration(
                labelText: 'Select a Reason',
                labelStyle: TextStyle(color: Colors.black),
                filled: true,
                fillColor: Colors.grey[100],
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.0),
                  borderSide: BorderSide(color: Colors.grey),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.0),
                  borderSide: BorderSide(color: Colors.grey),
                ),
              ),
            ),
            SizedBox(height: 16.0),
            TextField(
              controller: commentController,
              style: TextStyle(color: Colors.black),
              decoration: InputDecoration(
                labelText: 'Comment',
                labelStyle: TextStyle(color: Colors.black),
                filled: true,
                fillColor: Colors.grey[100],
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.0),
                  borderSide: BorderSide(color: Colors.grey),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.0),
                  borderSide: BorderSide(color: Colors.grey),
                ),
              ),
              maxLines: 3,
            ),
            SizedBox(height: 16.0),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: ClassifiedAppTheme.of(context).primary,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  padding: EdgeInsets.symmetric(vertical: 16.0),
                ),
                onPressed: submitReport,
                child: Text(
                  'Submit',
                  style: TextStyle(fontSize: 16.0),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
