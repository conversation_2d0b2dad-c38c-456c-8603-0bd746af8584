import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:soho_souk/ApiUtils.dart';
class HeroPhotoViewWrapper extends StatefulWidget {
  final List<dynamic> imageUrls;
  final int initialIndex;
  final double minScale;
  final double maxScale;

  const HeroPhotoViewWrapper({
    required this.imageUrls,
    required this.initialIndex,
    this.minScale = 0.7,
    this.maxScale = 2.0,
  });

  @override
  _HeroPhotoViewWrapperState createState() => _HeroPhotoViewWrapperState();
}

class _HeroPhotoViewWrapperState extends State<HeroPhotoViewWrapper> {
  late PageController _pageController;
  int currentIndex = 0;

  @override
  void initState() {
    super.initState();
    currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        leading: IconButton(
          icon: Icon(Icons.close, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          '${currentIndex + 1}/${widget.imageUrls.length}',
          style: TextStyle(color: Colors.white),
        ),
      ),
      body: PhotoViewGallery.builder(
        scrollPhysics: const BouncingScrollPhysics(),
        builder: (BuildContext context, int index) {
          return PhotoViewGalleryPageOptions(
            imageProvider: NetworkImage("${ApiUtils.post_image}${widget.imageUrls[index]}"),
            minScale: PhotoViewComputedScale.contained * widget.minScale,
            maxScale: PhotoViewComputedScale.covered * widget.maxScale,
          );
        },
        itemCount: widget.imageUrls.length,
        loadingBuilder: (context, event) => Center(
          child: CircularProgressIndicator(),
        ),
        pageController: _pageController,
        onPageChanged: (index) {
          setState(() {
            currentIndex = index;
          });
        },
      ),
    );
  }
}