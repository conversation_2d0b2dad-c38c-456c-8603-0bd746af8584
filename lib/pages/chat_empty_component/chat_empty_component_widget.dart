import '../../Classified_App/classified_app_model.dart';
import '../../Classified_App/classified_app_theme.dart';
import '../../Classified_App/internationalization.dart';
import '/pages/app_button/app_button_widget.dart';
import 'package:flutter/material.dart';
import 'chat_empty_component_model.dart';
export 'chat_empty_component_model.dart';

class ChatEmptyComponentWidget extends StatefulWidget {
  const ChatEmptyComponentWidget({super.key});

  @override
  State<ChatEmptyComponentWidget> createState() =>
      _ChatEmptyComponentWidgetState();
}

class _ChatEmptyComponentWidgetState extends State<ChatEmptyComponentWidget> {
  late ChatEmptyComponentModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => ChatEmptyComponentModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Image.asset(
          'assets/images/emptyChat.png',
          width: 120.0,
          height: 120.0,
          fit: BoxFit.contain,
        ),
        Padding(
          padding: EdgeInsetsDirectional.fromSTEB(0.0, 28.0, 0.0, 0.0),
          child: Text(
            FFLocalizations.of(context).getText(
              'rxs0gfxl' /* No chats yet */,
            ),
            style: ClassifiedAppTheme.of(context).bodyMedium.override(
                  fontFamily: 'Satoshi',
                  fontSize: 24.0,
                  fontWeight: FontWeight.bold,
                  useGoogleFonts: false,
                ),
          ),
        ),
        Padding(
          padding: EdgeInsetsDirectional.fromSTEB(0.0, 16.0, 0.0, 0.0),
          child: Text(
            FFLocalizations.of(context).getText(
              '2tnygpk0' /* Your chats list is empty pleas... */,
            ),
            textAlign: TextAlign.center,
            style: ClassifiedAppTheme.of(context).bodyMedium.override(
                  fontFamily: 'Satoshi',
                  fontSize: 17.0,
                  fontWeight: FontWeight.w500,
                  useGoogleFonts: false,
                ),
          ),
        ),
        Padding(
          padding: EdgeInsetsDirectional.fromSTEB(54.0, 28.0, 54.0, 0.0),
          child: wrapWithModel(
            model: _model.appButtonModel,
            updateCallback: () => setState(() {}),
            child: AppButtonWidget(
              text: 'Go to home',
              action: () async {},
            ),
          ),
        ),
      ],
    );
  }
}
