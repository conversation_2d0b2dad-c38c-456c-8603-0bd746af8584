import '../../Classified_App/classified_app_model.dart';
import '/pages/app_button/app_button_widget.dart';
import 'chat_empty_component_widget.dart' show ChatEmptyComponentWidget;
import 'package:flutter/material.dart';

class ChatEmptyComponentModel
    extends ClassifiedAppModel<ChatEmptyComponentWidget> {
  ///  State fields for stateful widgets in this component.

  // Model for AppButton component.
  late AppButtonModel appButtonModel;

  /// Initialization and disposal methods.

  @override
  void initState(BuildContext context) {
    appButtonModel = createModel(context, () => AppButtonModel());
  }

  @override
  void dispose() {
    appButtonModel.dispose();
  }

  /// Action blocks are added here.

  /// Additional helper methods are added here.
}
