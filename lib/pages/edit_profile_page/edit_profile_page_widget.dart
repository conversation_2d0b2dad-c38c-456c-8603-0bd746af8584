import 'dart:convert';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:http/http.dart' as http;
import 'package:nb_utils/nb_utils.dart';
import 'package:soho_souk/ApiUtils.dart';
import 'package:soho_souk/Classified_App/classified_app_util.dart';
import 'package:soho_souk/main.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:dio/dio.dart';
import 'package:soho_souk/store/AppStore.dart';
import 'package:soho_souk/widget/loader.dart';
import '../../Classified_App/classified_app_theme.dart';
import '/pages/app_bar/app_bar_widget.dart';
import '/pages/app_button/app_button_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'edit_profile_page_model.dart';
export 'edit_profile_page_model.dart';

class EditProfilePageWidget extends StatefulWidget {
  const EditProfilePageWidget({super.key});

  @override
  State<EditProfilePageWidget> createState() => _EditProfilePageWidgetState();
}

class _EditProfilePageWidgetState extends State<EditProfilePageWidget> {
  late EditProfilePageModel _model;
 String? profile_image;
   XFile? uploadProfile;
  final scaffoldKey = GlobalKey<ScaffoldState>();
  final ImagePicker _picker = ImagePicker();
  productImage() async {
    _picker
        .pickImage(
      source: ImageSource.gallery,
    )
        .then((XFile? recordedVideo) {
      if (recordedVideo != null && recordedVideo.path != null) {
        setState(() {
          uploadProfile = recordedVideo;
        });

        _cropImage();
      }
    });
  }
Future _cropImage() async {
    if (uploadProfile != null) {
      CroppedFile? cropped = await ImageCropper()
          .cropImage(sourcePath: uploadProfile!.path, aspectRatio: CropAspectRatio(ratioX: 1, ratioY: 1), uiSettings: [
        AndroidUiSettings(
            toolbarTitle: 'Crop',
            cropGridColor: Colors.black,
            initAspectRatio: CropAspectRatioPreset.original,
            lockAspectRatio: false),
        IOSUiSettings(title: 'Crop')
      ]);

      if (cropped != null) {
        sendVideoDio(cropped.path, cropped.path.split('/').last);
      }
    }
  }

  sendVideoDio(String pathname, String fileName) async {
    try {
      FormData formData = FormData.fromMap({
        "profile_image": await MultipartFile.fromFile(pathname,
            filename: uploadProfile!.name),
        "customer_id": appStore.user_id,
      });
      Response response = await Dio()
          .post("${ApiUtils.BASE_URL}update-profile-image", data: formData);
      if (response.statusCode == 200) {
        toasty(context, "Successfully Profile Pic Update!",
            bgColor: Colors.green, textColor: Colors.black);
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('profile_image', response.data['profile_image']);
        setState(() {
          profile_image = response.data['profile_image'];
          appStore.profile_image = response.data['profile_image'];
        });
      } else {
        toasty(context, response.data['message'],
            bgColor: Colors.red, textColor: Colors.black);
      }

      // print("File upload response: $response");

      // print(response.data);
    } catch (e) {
      toasty(context, "$e", bgColor: Colors.red, textColor: Colors.black);
    }
  }

    void updateProfile(context) {
   
    bool emailValid = RegExp(
            r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+")
        .hasMatch(_model.textController3.text);
    if (_model.textController1.text == '') {
      toasty(context, 'Enter First Name',
          bgColor: Colors.red, textColor: Colors.white);
    } 
    else if (_model.textController2.text =='') {
      toasty(context, 'Enter Last Name',
          bgColor: Colors.red, textColor: Colors.white);
     
    } 
    else if (_model.textController4.text =='') {
      toasty(context, 'Enter Valid Mobile Number',
          bgColor: Colors.red, textColor: Colors.white);
     
    } 
    else if (emailValid == false) {
      toasty(context, 'Enter Vallid Email Address',
          bgColor: Colors.red, textColor: Colors.white);
     
    } else {
      try {
         showLoadingDialog(context);
      http
          .post(
        Uri.parse("${ApiUtils.BASE_URL}update-profile"),
        headers: <String, String>{
          'Content-Type': 'application/json; charset=UTF-8',
          'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
        },
        body: jsonEncode(<String, dynamic>{
          'customer_id': appStore.user_id,
          'first_name': _model.textController1.text,
          'last_name': _model.textController2.text,
          'email': _model.textController3.text,
          'mobile': _model.textController4.text,
        }),
      )
          .then((response) {
         Navigator.of(context, rootNavigator: true).pop(false);
        Map mapValue = json.decode(response.body);
        if (response.statusCode == 200) {
          String name =_model.textController1.text+" "+_model.textController2.text;
         appStore.updateProfile(name, _model.textController3.text,
              profile_image, _model.textController4.text,context);
             
          toasty(context, mapValue['message'],
              bgColor: Colors.green, textColor: Colors.black);
        
        } else {
          toasty(context, mapValue['message'],
              bgColor: Colors.red, textColor: Colors.black);
        }
      });
    }catch (e) {
        Navigator.of(context, rootNavigator: true).pop(false);
      toasty(context, "$e", bgColor: Colors.red, textColor: Colors.black);
    }
    }
  }

  void getProfile() {
   http.get(Uri.parse("${ApiUtils.BASE_URL}get-profile/${appStore.user_id}"),
    headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
          }
   ).then((response) {
      if (response.statusCode == 200) {
        Map mapValue = json.decode(response.body);
        setState(() {
          profile_image = mapValue['profile_image'] ?? null;
          _model.textController1?.text = mapValue['first_name'] ?? '';
          _model.textController2?.text = mapValue['last_name'] ?? '';
          _model.textController3?.text = mapValue['email'] ?? '';
          _model.textController4?.text = mapValue['mobile'] ?? '';
       
        });
      }
    });
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => EditProfilePageModel());

    _model.textController1 ??= TextEditingController();
    _model.textFieldFocusNode1 ??= FocusNode();

    _model.textController2 ??= TextEditingController();
    _model.textFieldFocusNode2 ??= FocusNode();

    _model.textController3 ??= TextEditingController();
    _model.textFieldFocusNode3 ??= FocusNode();
    _model.textController4 ??= TextEditingController();
    _model.textFieldFocusNode4 ??= FocusNode();
getProfile();
    // WidgetsBinding.instance.addPostFrameCallback((_) => setState(() {
    //       _model.textController1?.text = appStore.user_name!;
    //       _model.textController2?.text = appStore.user_email!;
    //       _model.textController3?.text = appStore.user_mobile!;
    //       _model.textController4?.text = appStore.user_mobile!;
    //     }));
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: ClassifiedAppTheme.of(context).primaryBackground,
        body: SafeArea(
          top: true,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              wrapWithModel(
                model: _model.appBarModel,
                updateCallback: () => setState(() {}),
                child: AppBarWidget(
                  title: 'Edit profile',
                ),
              ),
              Expanded(
                child: Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Align(
                          alignment: AlignmentDirectional(0.0, 0.0),
                          child: Stack(
                            alignment: AlignmentDirectional(1.0, 1.0),
                            children: [
                             profile_image ==null? Image.asset(
                                'assets/images/ProfileEmpty.png',
                                width: 100.0,
                                height: 100.0,
                                fit: BoxFit.contain,
                              ):ClipRRect(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              50.0),
                                                      child: CachedNetworkImage(
                                                     width: 100.0,
                                height: 100.0,
                                                        fit: BoxFit.cover,
                                                        imageUrl:
                                                            "${ApiUtils.profile_files}${profile_image}",
                                                        placeholder:
                                                            (context, url) =>
                                                                Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                  .all(35.0),
                                                          child: Center(
                                                              child:
                                                                  CircularProgressIndicator()),
                                                        ),
                                                        errorWidget: (context,
                                                                url, error) =>
                                                            Icon(Icons.error),
                                                      ),
                                                    ),
                              Container(
                                width: 34.0,
                                height: 34.0,
                                decoration: BoxDecoration(
                                  color: ClassifiedAppTheme.of(context).tertiary,
                                  shape: BoxShape.circle,
                                ),
                                alignment: AlignmentDirectional(0.0, 0.0),
                                child: GestureDetector(
                                  onTap: (){
                                  productImage();
                                  },
                                  child: SvgPicture.asset(
                                    'assets/images/camera.svg',
                                    width: 20.0,
                                    height: 20.0,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0, 24.0, 0.0, 0.0),
                          child: Text(
                           "First name",
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 17.0,
                                  fontWeight: FontWeight.w500,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0, 4.0, 0.0, 0.0),
                          child: TextFormField(
                            controller: _model.textController1,
                            focusNode: _model.textFieldFocusNode1,
                            textInputAction: TextInputAction.next,
                            obscureText: false,
                            decoration: InputDecoration(
                              labelStyle:
                                  ClassifiedAppTheme.of(context).labelMedium,
                              hintText: FFLocalizations.of(context).getText(
                                'b656na5c' /* Enter name */,
                              ),
                              hintStyle: ClassifiedAppTheme.of(context)
                                  .labelMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    color: ClassifiedAppTheme.of(context)
                                        .secondaryText,
                                    fontSize: 17.0,
                                    fontWeight: FontWeight.w500,
                                    useGoogleFonts: false,
                                  ),
                              errorStyle: ClassifiedAppTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    color: ClassifiedAppTheme.of(context).error,
                                    useGoogleFonts: false,
                                  ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).info,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).primary,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).error,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              focusedErrorBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).error,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                            ),
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 17.0,
                                  fontWeight: FontWeight.w500,
                                  useGoogleFonts: false,
                                ),
                            keyboardType: TextInputType.name,
                            cursorColor: ClassifiedAppTheme.of(context).primary,
                            validator: _model.textController1Validator
                                .asValidator(context),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0, 24.0, 0.0, 0.0),
                          child: Text(
                           "Last name",
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 17.0,
                                  fontWeight: FontWeight.w500,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0, 4.0, 0.0, 0.0),
                          child: TextFormField(
                            controller: _model.textController2,
                            focusNode: _model.textFieldFocusNode2,
                            textInputAction: TextInputAction.next,
                            obscureText: false,
                            decoration: InputDecoration(
                              labelStyle:
                                  ClassifiedAppTheme.of(context).labelMedium,
                              hintText: FFLocalizations.of(context).getText(
                                'b656na5c' /* Enter name */,
                              ),
                              hintStyle: ClassifiedAppTheme.of(context)
                                  .labelMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    color: ClassifiedAppTheme.of(context)
                                        .secondaryText,
                                    fontSize: 17.0,
                                    fontWeight: FontWeight.w500,
                                    useGoogleFonts: false,
                                  ),
                              errorStyle: ClassifiedAppTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    color: ClassifiedAppTheme.of(context).error,
                                    useGoogleFonts: false,
                                  ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).info,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).primary,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).error,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              focusedErrorBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).error,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                            ),
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 17.0,
                                  fontWeight: FontWeight.w500,
                                  useGoogleFonts: false,
                                ),
                            keyboardType: TextInputType.name,
                            cursorColor: ClassifiedAppTheme.of(context).primary,
                            validator: _model.textController2Validator
                                .asValidator(context),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0, 20.0, 0.0, 0.0),
                          child: Text(
                            FFLocalizations.of(context).getText(
                              '9baf7fi7' /* Email address */,
                            ),
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 17.0,
                                  fontWeight: FontWeight.w500,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0, 4.0, 0.0, 0.0),
                          child: TextFormField(
                            controller: _model.textController3,
                            focusNode: _model.textFieldFocusNode3,
                            textInputAction: TextInputAction.next,
                            obscureText: false,
                            decoration: InputDecoration(
                              labelStyle:
                                  ClassifiedAppTheme.of(context).labelMedium,
                              hintText: FFLocalizations.of(context).getText(
                                'jfofvluz' /* Enter email address */,
                              ),
                              hintStyle: ClassifiedAppTheme.of(context)
                                  .labelMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    color: ClassifiedAppTheme.of(context)
                                        .secondaryText,
                                    fontSize: 17.0,
                                    fontWeight: FontWeight.w500,
                                    useGoogleFonts: false,
                                  ),
                              errorStyle: ClassifiedAppTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    color: Color(0xFFCD3232),
                                    useGoogleFonts: false,
                                  ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).info,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).primary,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).error,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              focusedErrorBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).error,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                            ),
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 17.0,
                                  fontWeight: FontWeight.w500,
                                  useGoogleFonts: false,
                                ),
                            keyboardType: TextInputType.emailAddress,
                            cursorColor: ClassifiedAppTheme.of(context).primary,
                            validator: _model.textController3Validator
                                .asValidator(context),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0, 20.0, 0.0, 0.0),
                          child: Text(
                            FFLocalizations.of(context).getText(
                              '5sj3en36' /* Phone number */,
                            ),
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 17.0,
                                  fontWeight: FontWeight.w500,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              0.0, 4.0, 0.0, 0.0),
                          child: TextFormField(
                            controller: _model.textController4,
                            focusNode: _model.textFieldFocusNode4,
                            textCapitalization: TextCapitalization.none,
                            textInputAction: TextInputAction.done,
                            obscureText: false,
                            decoration: InputDecoration(
                              labelStyle:
                                  ClassifiedAppTheme.of(context).labelMedium,
                              hintText: FFLocalizations.of(context).getText(
                                'vybq65wa' /* Enter phone number */,
                              ),
                                prefixIcon: Padding(
                        padding: EdgeInsets.all(14.0),
                        child: Text("+971",style: ClassifiedAppTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    fontSize: 14.0,
                                    fontWeight: FontWeight.w500,
                                    useGoogleFonts: false,
                                  )), // icon is 48px widget.
                      ),
                              hintStyle: ClassifiedAppTheme.of(context)
                                  .labelMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    color: ClassifiedAppTheme.of(context)
                                        .secondaryText,
                                    fontSize: 17.0,
                                    fontWeight: FontWeight.w500,
                                    useGoogleFonts: false,
                                  ),
                              errorStyle: ClassifiedAppTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    color: Color(0xFFCD3232),
                                    useGoogleFonts: false,
                                  ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).info,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).primary,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).error,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              focusedErrorBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).error,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                            ),
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 17.0,
                                  fontWeight: FontWeight.w500,
                                  useGoogleFonts: false,
                                ),
                            keyboardType: TextInputType.phone,
                            cursorColor: ClassifiedAppTheme.of(context).primary,
                            validator: _model.textController4Validator
                                .asValidator(context),
                          ),
                        ),
                      ].addToStart(SizedBox(height: 30.0)),
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 24.0),
                child: InkWell(
                  splashColor: Colors.transparent,
                  focusColor: Colors.transparent,
                  hoverColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  onTap: () async {
                    updateProfile(context);
                    
                  },
                  child: wrapWithModel(
                    model: _model.appButtonModel,
                    updateCallback: () => setState(() {}),
                    child: AppButtonWidget(
                      text: 'Save',
                      action: () async {
                           updateProfile(context);
                       // context.safePop();
                      },
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
