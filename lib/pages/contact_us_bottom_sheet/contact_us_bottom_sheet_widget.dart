import 'package:nb_utils/nb_utils.dart';
import 'package:soho_souk/main.dart';

import '../../Classified_App/classified_app_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../Classified_App/classified_app_util.dart';
import 'contact_us_bottom_sheet_model.dart';
export 'contact_us_bottom_sheet_model.dart';

class ContactUsBottomSheetWidget extends StatefulWidget {
   ContactUsBottomSheetWidget({Key? key, required this.vendor_id, required this.vendor_name, required this.mobile, required this.show_mobile,required this.enable_sms}) : super(key: key);
    final int vendor_id,show_mobile,enable_sms;
  final String vendor_name;
  final String mobile;

  @override
  State<ContactUsBottomSheetWidget> createState() =>
      _ContactUsBottomSheetWidgetState();
}

class _ContactUsBottomSheetWidgetState
    extends State<ContactUsBottomSheetWidget> {
  late ContactUsBottomSheetModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => ContactUsBottomSheetModel());
    print(widget.mobile);
    print(widget.vendor_id);
    print(widget.vendor_name);
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  triggerToast(){
     toasty(context, "Invalid Access, Please Login!",
            bgColor: Colors.red, textColor: Colors.black);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 278.0,
      decoration: BoxDecoration(
        color: ClassifiedAppTheme.of(context).secondaryBackground,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(0.0),
          bottomRight: Radius.circular(0.0),
          topLeft: Radius.circular(32.0),
          topRight: Radius.circular(32.0),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        children: [
          Container(
            width: 70.0,
            height: 5.0,
            decoration: BoxDecoration(
              color: ClassifiedAppTheme.of(context).info,
              borderRadius: BorderRadius.circular(20.0),
            ),
          ),
          Padding(
            padding: EdgeInsetsDirectional.fromSTEB(16.0, 10.0, 16.0, 16.0),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  width: 44.0,
                  height: 44.0,
                  decoration: BoxDecoration(
                    color: ClassifiedAppTheme.of(context).secondaryBackground,
                    shape: BoxShape.circle,
                  ),
                ),
                Text(
                  "Contact seller",
                  style: ClassifiedAppTheme.of(context).bodyMedium.override(
                        fontFamily: 'Satoshi',
                        fontSize: 20.0,
                        fontWeight: FontWeight.bold,
                        useGoogleFonts: false,
                      ),
                ),
                InkWell(
                  splashColor: Colors.transparent,
                  focusColor: Colors.transparent,
                  hoverColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  onTap: () async {
                    context.safePop();
                  },
                  child: Container(
                    width: 44.0,
                    height: 44.0,
                    decoration: BoxDecoration(
                      color: ClassifiedAppTheme.of(context).tertiary,
                      shape: BoxShape.circle,
                    ),
                    alignment: AlignmentDirectional(0.0, 0.0),
                    child: SvgPicture.asset(
                      'assets/images/close.svg',
                      width: 20.0,
                      height: 20.0,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Divider(
            height: 0.0,
            thickness: 1.0,
            color: ClassifiedAppTheme.of(context).info,
          ),
          Padding(
            padding: EdgeInsetsDirectional.fromSTEB(0.0, 24.0, 0.0, 0.0),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if(widget.show_mobile == 0)
                Expanded(
                  child: InkWell(
                    splashColor: Colors.transparent,
                    focusColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    onTap: () async {
                      if(appStore.logined){

                      await launchUrl(Uri(
                        scheme: 'tel',
                        path: '+971${widget.mobile}',
                      ));
                      }else{
                        triggerToast();
                      }
                    },
                    child: Container(
                      height: 112.0,
                      decoration: BoxDecoration(
                        color: ClassifiedAppTheme.of(context).secondaryBackground,
                        boxShadow: [
                          BoxShadow(
                            blurRadius: 4.0,
                            color: Color(0x33000000),
                            offset: Offset(0.0, 2.0),
                          )
                        ],
                        borderRadius: BorderRadius.circular(12.0),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            width: 48.0,
                            height: 48.0,
                            decoration: BoxDecoration(
                              color: ClassifiedAppTheme.of(context).tertiary,
                              shape: BoxShape.circle,
                            ),
                            alignment: AlignmentDirectional(0.0, 0.0),
                            child: SvgPicture.asset(
                              'assets/images/call.svg',
                              width: 24.0,
                              height: 24.0,
                              fit: BoxFit.contain,
                            ),
                          ),
                          Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(
                                0.0, 8.0, 0.0, 0.0),
                            child: Text(
                              FFLocalizations.of(context).getText(
                                'naq4hhk3' /* Call */,
                              ),
                              style: ClassifiedAppTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    fontSize: 17.0,
                                    fontWeight: FontWeight.w500,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                if(widget.enable_sms == 0)
                Expanded(
                  child: InkWell(
                    splashColor: Colors.transparent,
                    focusColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    onTap: () async {
                       if(appStore.logined){
                      if (isiOS) {
                        await launchUrl(Uri.parse(
                            "sms:${'+971${widget.mobile}'}&body=${Uri.encodeComponent('SMS:')}"));
                      } else {
                        await launchUrl(Uri(
                          scheme: 'sms',
                          path: '+971${widget.mobile}',
                          queryParameters: <String, String>{
                            'body': 'SMS:',
                          },
                        ));
                      }
                       }else{
                        triggerToast();
                      }
                    },
                    child: Container(
                      height: 112.0,
                      decoration: BoxDecoration(
                        color: ClassifiedAppTheme.of(context).secondaryBackground,
                        boxShadow: [
                          BoxShadow(
                            blurRadius: 4.0,
                            color: Color(0x33000000),
                            offset: Offset(0.0, 2.0),
                          )
                        ],
                        borderRadius: BorderRadius.circular(12.0),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            width: 48.0,
                            height: 48.0,
                            decoration: BoxDecoration(
                              color: ClassifiedAppTheme.of(context).tertiary,
                              shape: BoxShape.circle,
                            ),
                            alignment: AlignmentDirectional(0.0, 0.0),
                            child: SvgPicture.asset(
                              'assets/images/message-text.svg',
                              width: 24.0,
                              height: 24.0,
                              fit: BoxFit.contain,
                            ),
                          ),
                          Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(
                                0.0, 8.0, 0.0, 0.0),
                            child: Text(
                              FFLocalizations.of(context).getText(
                                '5a7pufb4' /* Message */,
                              ),
                              style: ClassifiedAppTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    fontSize: 17.0,
                                    fontWeight: FontWeight.w500,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    height: 112.0,
                    decoration: BoxDecoration(
                      color: ClassifiedAppTheme.of(context).secondaryBackground,
                      boxShadow: [
                        BoxShadow(
                          blurRadius: 4.0,
                          color: Color(0x33000000),
                          offset: Offset(0.0, 2.0),
                        )
                      ],
                      borderRadius: BorderRadius.circular(12.0),
                    ),
                    child: InkWell(
                      splashColor: Colors.transparent,
                      focusColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      onTap: () async {
                       
                            if(appStore.logined){
                               if(appStore.user_id == widget.vendor_id){
                         toasty(context, "Invalid Access, You can't chat with yourself!",
            bgColor: Colors.red, textColor: Colors.black);

                        }else{

                                context.pushNamed(
                                      'ChatDetailsPage',
                                      queryParameters: {
                                        'vendor_id': serializeParam(
                                         widget.vendor_id,
                                          ParamType.int,
                                        ),
                                        'vendor_name': serializeParam(
                                          widget.vendor_name,
                                          ParamType.String,
                                        ),
                                      }.withoutNulls,
                                      extra: <String, dynamic>{
                                        kTransitionInfoKey: TransitionInfo(
                                          hasTransition: true,
                                          transitionType:
                                              PageTransitionType.rightToLeft,
                                          duration: Duration(milliseconds: 300),
                                        ),
                                      },
                                    );
                        }
                        // context.pushNamed(
                        //   'ChatDetailsPage',
                        //   extra: <String, dynamic>{
                        //     kTransitionInfoKey: TransitionInfo(
                        //       hasTransition: true,
                        //       transitionType: PageTransitionType.rightToLeft,
                        //       duration: Duration(milliseconds: 300),
                        //     ),
                        //   },
                        // );
                          }else{
                        triggerToast();
                      }
                      },
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            width: 48.0,
                            height: 48.0,
                            decoration: BoxDecoration(
                              color: ClassifiedAppTheme.of(context).tertiary,
                              shape: BoxShape.circle,
                            ),
                            alignment: AlignmentDirectional(0.0, 0.0),
                            child: SvgPicture.asset(
                              'assets/images/message.svg',
                              width: 24.0,
                              height: 24.0,
                              fit: BoxFit.contain,
                            ),
                          ),
                          Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(
                                0.0, 8.0, 0.0, 0.0),
                            child: Text(
                              FFLocalizations.of(context).getText(
                                't0deyxjm' /* Chats */,
                              ),
                              style: ClassifiedAppTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    fontSize: 17.0,
                                    fontWeight: FontWeight.w500,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ]
                  .divide(SizedBox(width: 16.0))
                  .addToStart(SizedBox(width: 16.0))
                  .addToEnd(SizedBox(width: 16.0)),
            ),
          ),
        ].addToStart(SizedBox(height: 16.0)),
      ),
    );
  }
}
