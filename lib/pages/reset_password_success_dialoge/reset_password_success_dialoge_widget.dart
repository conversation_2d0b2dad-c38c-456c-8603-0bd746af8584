import '../../Classified_App/classified_app_theme.dart';
import '../../Classified_App/classified_app_util.dart';
import '/pages/app_button/app_button_widget.dart';
import 'package:flutter/material.dart';
import 'reset_password_success_dialoge_model.dart';
export 'reset_password_success_dialoge_model.dart';

class ResetPasswordSuccessDialogeWidget extends StatefulWidget {
  const ResetPasswordSuccessDialogeWidget({super.key});

  @override
  State<ResetPasswordSuccessDialogeWidget> createState() =>
      _ResetPasswordSuccessDialogeWidgetState();
}

class _ResetPasswordSuccessDialogeWidgetState
    extends State<ResetPasswordSuccessDialogeWidget> {
  late ResetPasswordSuccessDialogeModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => ResetPasswordSuccessDialogeModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(20.0, 0.0, 20.0, 0.0),
      child: Container(
        width: 396.0,
        height: 392.0,
        decoration: BoxDecoration(
          color: ClassifiedAppTheme.of(context).secondaryBackground,
          borderRadius: BorderRadius.circular(12.0),
        ),
        child: Padding(
          padding: EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                'assets/images/passwordSuccess.png',
                width: 120.0,
                height: 120.0,
                fit: BoxFit.contain,
              ),
              Align(
                alignment: AlignmentDirectional(0.0, 0.0),
                child: Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(0.0, 28.0, 0.0, 0.0),
                  child: Text(
                    FFLocalizations.of(context).getText(
                      'n5f6vhwt' /* Password reset successfully */,
                    ),
                    style: ClassifiedAppTheme.of(context).bodyMedium.override(
                          fontFamily: 'Satoshi',
                          fontSize: 24.0,
                          fontWeight: FontWeight.bold,
                          useGoogleFonts: false,
                        ),
                  ),
                ),
              ),
              Align(
                alignment: AlignmentDirectional(0.0, 0.0),
                child: Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(0.0, 16.0, 0.0, 0.0),
                  child: Text(
                    FFLocalizations.of(context).getText(
                      'xl2cmmkh' /* You have successfully change y... */,
                    ),
                    textAlign: TextAlign.center,
                    style: ClassifiedAppTheme.of(context).bodyMedium.override(
                          fontFamily: 'Satoshi',
                          fontSize: 17.0,
                          fontWeight: FontWeight.w500,
                          useGoogleFonts: false,
                        ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(54.0, 28.0, 54.0, 0.0),
                child: InkWell(
                  splashColor: Colors.transparent,
                  focusColor: Colors.transparent,
                  hoverColor: Colors.transparent,
                  highlightColor: Colors.transparent,
                  onTap: () async {
                    context.goNamed(
                      'LoginPage',
                      extra: <String, dynamic>{
                        kTransitionInfoKey: TransitionInfo(
                          hasTransition: true,
                          transitionType: PageTransitionType.rightToLeft,
                          duration: Duration(milliseconds: 300),
                        ),
                      },
                    );
                  },
                  child: wrapWithModel(
                    model: _model.appButtonModel,
                    updateCallback: () => setState(() {}),
                    child: AppButtonWidget(
                      text: 'Go to login',
                      action: () async {
                        context.goNamed(
                          'LoginPage',
                          extra: <String, dynamic>{
                            kTransitionInfoKey: TransitionInfo(
                              hasTransition: true,
                              transitionType: PageTransitionType.rightToLeft,
                              duration: Duration(milliseconds: 300),
                            ),
                          },
                        );
                      },
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
