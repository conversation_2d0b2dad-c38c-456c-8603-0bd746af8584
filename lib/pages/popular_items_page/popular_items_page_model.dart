
import '../../Classified_App/classified_app_model.dart';
import '/pages/app_bar/app_bar_widget.dart';
import 'popular_items_page_widget.dart' show PopularItemsPageWidget;
import 'package:flutter/material.dart';

class PopularItemsPageModel extends ClassifiedAppModel<PopularItemsPageWidget> {
  ///  State fields for stateful widgets in this page.

  final unfocusNode = FocusNode();
  // Model for AppBar component.
  late AppBarModel appBarModel;

  /// Initialization and disposal methods.

  @override
  void initState(BuildContext context) {
    appBarModel = createModel(context, () => AppBarModel());
  }

  @override
  void dispose() {
    unfocusNode.dispose();
    appBarModel.dispose();
  }

  /// Action blocks are added here.

  /// Additional helper methods are added here.
}
