import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:soho_souk/main.dart';
import 'package:soho_souk/service/serviceApi.dart';

import '../../Classified_App/classified_app_animations.dart';
import '../../Classified_App/classified_app_theme.dart';
import '../../Classified_App/classified_app_util.dart';
import '/custom_code/actions/index.dart' as actions;
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'splash_page_model.dart';
export 'splash_page_model.dart';

class SplashPageWidget extends StatefulWidget {
  const SplashPageWidget({super.key});

  @override
  State<SplashPageWidget> createState() => _SplashPageWidgetState();
}

class _SplashPageWidgetState extends State<SplashPageWidget>
    with TickerProviderStateMixin {
  late SplashPageModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  final animationsMap = {
    'imageOnPageLoadAnimation': AnimationInfo(
      trigger: AnimationTrigger.onPageLoad,
      effects: [
        FadeEffect(
          curve: Curves.easeInOut,
          delay: 50.ms,
          duration: 100.ms,
          begin: 0.0,
          end: 1.0,
        ),
      ],
    ),
  };
getData() async{
   await appStore.storeUserData();
    //appStore.setSliderData(await ServiceApi().getSlider());
    appStore.setCategoryData(await ServiceApi().getCategory());
    appStore.setAllPost(await ServiceApi().getAllPost());
    appStore.setSiteData(await ServiceApi().getSiteDetails());
    // appStore.setLatestPost(await ServiceApi().getLatestPost('get-latest-post-list'));
    // appStore.setFeaturePost(await ServiceApi().getLatestPost('get-featured-post-list'));
    // appStore.setBarterPost(await ServiceApi().getLatestPost('get-barter-post-list'));
    // appStore.setElecPost(await ServiceApi().getCategoryPost(1));
    // appStore.setVehiclesPost(await ServiceApi().getCategoryPost(6));
    appStore.getFavPost(await ServiceApi().getFav());
    appStore.setCityData(await ServiceApi().getCity());
    appStore.setItemConditions(await ServiceApi().getCondition());
      final prefs = await SharedPreferences.getInstance();
      final app_language = await prefs.getString('app_language');

    if(app_language !=null)
    MyApp.of(context).setLocale(app_language);
    
  }
  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => SplashPageModel());

    // On page load action.
    SchedulerBinding.instance.addPostFrameCallback((_) async {
       await getData();
      await actions.splashAction(
        () async {
            context.goNamed(
                'HomeBottomBarPage',
                extra: <String, dynamic>{
                  kTransitionInfoKey: TransitionInfo(
                    hasTransition: true,
                    transitionType: PageTransitionType.rightToLeft,
                    duration: Duration(milliseconds: 300),
                  ),
                },
              );

          // _model.onboardingAction = await actions.getOnBoarding();
          // _model.loginAction = await actions.getLogin();
          // if (_model.onboardingAction == true) {
          //   if (appStore.logined) {
          //     context.goNamed(
          //       'HomeBottomBarPage',
          //       extra: <String, dynamic>{
          //         kTransitionInfoKey: TransitionInfo(
          //           hasTransition: true,
          //           transitionType: PageTransitionType.rightToLeft,
          //           duration: Duration(milliseconds: 300),
          //         ),
          //       },
          //     );
          //   } else {
          //     context.goNamed(
          //       'LoginPage',
          //       extra: <String, dynamic>{
          //         kTransitionInfoKey: TransitionInfo(
          //           hasTransition: true,
          //           transitionType: PageTransitionType.rightToLeft,
          //           duration: Duration(milliseconds: 300),
          //         ),
          //       },
          //     );
          //   }
          // } else {

          //   context.goNamed(
          //     'IntroPage',
          //     extra: <String, dynamic>{
          //       kTransitionInfoKey: TransitionInfo(
          //         hasTransition: true,
          //         transitionType: PageTransitionType.rightToLeft,
          //         duration: Duration(milliseconds: 300),
          //       ),
          //     },
          //   );
            
          // }
        },
      );
    });
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: ClassifiedAppTheme.of(context).primaryBackground,
        body: SafeArea(
          top: true,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Align(
                alignment: AlignmentDirectional(0.0, 0.0),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8.0),
                  child: SvgPicture.asset(
                    'assets/images/logo_svg.svg',
                    width: 126.0,
                    height: 156.0,
                    fit: BoxFit.contain,
                  ),
                ).animateOnPageLoad(animationsMap['imageOnPageLoadAnimation']!),
              ),
            ],
          ),
        ),
           floatingActionButton:  FloatingActionButton(
            elevation: 0.0,
            child: SpinKitRipple(
              color: ClassifiedAppTheme.of(context).primary,
            ),
            backgroundColor: Colors.white,
            onPressed: () {}),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat
      ),
      
    );
  }
}
