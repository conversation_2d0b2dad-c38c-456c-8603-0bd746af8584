import 'package:flutter_svg/svg.dart';
import 'package:soho_souk/ApiUtils.dart';
import 'package:soho_souk/main.dart';

import '../../Classified_App/classified_app_util.dart';
import '/backend/schema/structs/index.dart';
import '../../Classified_App/classified_app_theme.dart';
import '/pages/app_bar/app_bar_widget.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'categories_page_model.dart';
export 'categories_page_model.dart';

class CategoriesPageWidget extends StatefulWidget {
   CategoriesPageWidget({super.key, this.barter = false});
  bool barter;
  @override
  State<CategoriesPageWidget> createState() => _CategoriesPageWidgetState();
}

class _CategoriesPageWidgetState extends State<CategoriesPageWidget> {
  late CategoriesPageModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => CategoriesPageModel());
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: ClassifiedAppTheme.of(context).primaryBackground,
        body: SafeArea(
          top: true,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              wrapWithModel(
                model: _model.appBarModel,
                updateCallback: () => setState(() {}),
                child: AppBarWidget(
                  title: 'Categories',
                ),
              ),
              Expanded(
                child: Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                  child: Builder(
                    builder: (context) {
                      // final categoriesList =
                      //     FFAppState().categoriesDataList.toList();
                      return GridView.builder(
                        padding: EdgeInsets.fromLTRB(
                          0,
                          16.0,
                          0,
                          0,
                        ),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: 10.0,
                          mainAxisSpacing: 16.0,
                          childAspectRatio: 1.0,
                        ),
                        scrollDirection: Axis.vertical,
                        itemCount: appStore.parentCategoryList.length,
                        itemBuilder: (context, index) {
                          final categoriesListItem =
                              appStore.parentCategoryList[index];
                          return Padding(
          padding: const EdgeInsets.all(4.0),
          child: GestureDetector(
            onTap: () {
                 if([9,10].contains(categoriesListItem.id)){
                  
                  context.pushNamed(
                                'CategoryProductPage',
                                queryParameters: {
                                  'title': serializeParam(
                                    categoriesListItem.category_name.toString(),
                                    ParamType.String,
                                  ),
                                  'index': serializeParam(
                                     categoriesListItem.id,
                                    ParamType.int,
                                  ),
                                  'isMaincategory': serializeParam(
                                   true,
                                    ParamType.bool,
                                  ),
                                    'barter': serializeParam(
                                   widget.barter,
                                    ParamType.bool,
                                  ),
                                }.withoutNulls,
                                extra: <String, dynamic>{
                                  kTransitionInfoKey: TransitionInfo(
                                    hasTransition: true,
                                    transitionType:
                                        PageTransitionType.rightToLeft,
                                    duration: Duration(milliseconds: 300),
                                  ),
                                },
                              );
              }else{
              context.pushNamed(
                'SubCategoriesPage',
                queryParameters: {
                  'title': serializeParam(
                    categoriesListItem.category_name.toString(),
                    ParamType.String,
                  ),
                  'image': serializeParam(
                    categoriesListItem.image.toString(),
                    ParamType.String,
                  ),
                  'index': serializeParam(
                    categoriesListItem.id,
                    ParamType.int,
                  ),
                    'barter': serializeParam(
                    widget.barter,
                    ParamType.bool,
                  ),
                }.withoutNulls,
                extra: <String, dynamic>{
                  kTransitionInfoKey: TransitionInfo(
                    hasTransition: true,
                    transitionType: PageTransitionType.rightToLeft,
                    duration: Duration(milliseconds: 300),
                  ),
                },
              );
              }
            },
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 6.0,
                    spreadRadius: 2.0,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(12.0),
                    ),
                    child: categoriesListItem.image?.endsWith('.svg') ?? false
                        ? SvgPicture.network(
                            "${ApiUtils.admin_files}${categoriesListItem.image}",
                            fit: BoxFit.cover,
                            height: 60,
                            color: ClassifiedAppTheme.of(context).primary,
                            width: double.infinity,
                            placeholderBuilder: (BuildContext context) => Container(
                              height: 60,
                              color: Colors.grey.shade200,
                              child: Center(
                                child: CircularProgressIndicator(),
                              ),
                            ),
                          )
                        : CachedNetworkImage(
                            imageUrl: "${ApiUtils.admin_files}${categoriesListItem.image}",
                            fit: BoxFit.cover,
                            height: 60,
                            width: double.infinity,
                            errorWidget: (context, url, error) => Container(
                              height: 60,
                              color: Colors.grey.shade200,
                              child: Icon(Icons.broken_image,
                                  size: 40, color: Colors.grey),
                            ),
                          ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(
                      "${categoriesListItem.category_name}",
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: 12.0,
                        fontWeight: FontWeight.bold,
                        color: ClassifiedAppTheme.of(context).primary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
                        },
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
