import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:soho_souk/ApiUtils.dart';
import 'package:soho_souk/main.dart';
import 'package:soho_souk/models/PostModel.dart';

import '../../Classified_App/classified_app_theme.dart';
import '../../Classified_App/classified_app_util.dart';
import '/pages/app_bar/app_bar_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'search_page_model.dart';
export 'search_page_model.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
class SearchPageWidget extends StatefulWidget {
  const SearchPageWidget({super.key});

  @override
  State<SearchPageWidget> createState() => _SearchPageWidgetState();
}

class _SearchPageWidgetState extends State<SearchPageWidget> {
  late SearchPageModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => SearchPageModel());

    _model.textController ??= TextEditingController();
    _model.textFieldFocusNode ??= FocusNode();
  }
  List<PostModel> list =[];
  //  searchPost() async {
   
  //   if(_model.textController.text.length>=3){
  //     setState(() {
  //     list = appStore.allPost.where((post) {
  //       return post.post_name?.toLowerCase().contains(_model.textController.text.toLowerCase()) ?? false;
  //     }).toList();
  //   });
  //   }
  
  // }
  searchPost() async {
  if (_model.textController.text.length >= 3) {
    setState(() {
      list = appStore.allPost.where((post) {
        // Check if the post name matches
        bool matchesPostName = post.post_name
            ?.toLowerCase()
            .contains(_model.textController.text.toLowerCase()) ??
            false;

        // Check if category, sub-category, or child-category names match
        bool matchesCategory = false;
        if (post.category != null) {
          matchesCategory = appStore.categoryList.any((category) {
            return category.id == post.category &&
                category.category_name!
                    .toLowerCase()
                    .contains(_model.textController.text.toLowerCase());
          });
        }

        bool matchesSubCategory = false;
        if (post.sub_category != null) {
          matchesSubCategory = appStore.categoryList.any((category) {
            return category.id == post.sub_category &&
                category.category_name!
                    .toLowerCase()
                    .contains(_model.textController.text.toLowerCase());
          });
        }

        bool matchesChildCategory = false;
        if (post.childcategory != null) {
          matchesChildCategory = appStore.categoryList.any((category) {
            return category.id == post.childcategory &&
                category.category_name!
                    .toLowerCase()
                    .contains(_model.textController.text.toLowerCase());
          });
        }

        // Return true if any of the checks match
        return matchesPostName ||
            matchesCategory ||
            matchesSubCategory ||
            matchesChildCategory;
      }).toList();
    });
  }
}

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: scaffoldKey,
      backgroundColor: ClassifiedAppTheme.of(context).primaryBackground,
      body: SafeArea(
        top: true,
        child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            wrapWithModel(
              model: _model.appBarModel,
              updateCallback: () => setState(() {}),
              child: AppBarWidget(
                title: 'Search',
              ),
            ),
            Padding(
              padding: EdgeInsetsDirectional.fromSTEB(20.0, 0.0, 20.0, 0.0),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextFormField(
                    controller: _model.textController,
                    focusNode: _model.textFieldFocusNode,
                    onFieldSubmitted: (_) async {
                      searchPost();
                      // context.pushNamed(
                      //   'CategoryProductPage',
                      //   extra: <String, dynamic>{
                      //     kTransitionInfoKey: TransitionInfo(
                      //       hasTransition: true,
                      //       transitionType: PageTransitionType.rightToLeft,
                      //       duration: Duration(milliseconds: 300),
                      //     ),
                      //   },
                      // );
                    },
                    onChanged: (value) {
                      searchPost();
                    },
                    textInputAction: TextInputAction.done,
                    obscureText: false,
                    decoration: InputDecoration(
                      labelStyle: ClassifiedAppTheme.of(context).labelMedium,
                      hintText: FFLocalizations.of(context).getText(
                        'tdnkdqx2' /* Search here... */,
                      ),
                      hintStyle:
                          ClassifiedAppTheme.of(context).labelMedium.override(
                                fontFamily: 'Satoshi',
                                fontSize: 16.0,
                                useGoogleFonts: false,
                              ),
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: ClassifiedAppTheme.of(context).info,
                          width: 1.0,
                        ),
                        borderRadius: BorderRadius.circular(12.0),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: ClassifiedAppTheme.of(context).primary,
                          width: 1.0,
                        ),
                        borderRadius: BorderRadius.circular(12.0),
                      ),
                      errorBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: ClassifiedAppTheme.of(context).error,
                          width: 1.0,
                        ),
                        borderRadius: BorderRadius.circular(12.0),
                      ),
                      focusedErrorBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: ClassifiedAppTheme.of(context).error,
                          width: 1.0,
                        ),
                        borderRadius: BorderRadius.circular(12.0),
                      ),
                      prefixIcon: Icon(
                        FFIcons.ksearchnew,
                        color: ClassifiedAppTheme.of(context).secondaryText,
                        size: 24.0,
                      ),
                    ),
                    style: ClassifiedAppTheme.of(context).bodyMedium,
                    cursorColor: ClassifiedAppTheme.of(context).primary,
                    validator:
                        _model.textControllerValidator.asValidator(context),
                  ),
                  Padding(
                    padding:
                        EdgeInsetsDirectional.fromSTEB(0.0, 24.0, 0.0, 0.0),
                    child: Text(
                      "Search Result",
                      style: ClassifiedAppTheme.of(context).bodyMedium.override(
                            fontFamily: 'Satoshi',
                            fontSize: 20.0,
                            fontWeight: FontWeight.bold,
                            useGoogleFonts: false,
                          ),
                    ),
                  ),

                
                 
                ].addToStart(SizedBox(height: 16.0)),
              ),
            ),
             Expanded(
                child: Padding(
                  padding:
                      EdgeInsetsDirectional.fromSTEB(10.0, 0.0, 10.0, 0.0),
                  child: Builder(
                    builder: (context) {
                    
                      return GridView.builder(
                        padding: EdgeInsets.fromLTRB(
                          0,
                          16.0,
                          0,
                          70.0,
                        ),
                        gridDelegate:
                            SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: () {
                            if (MediaQuery.sizeOf(context).width <
                                kBreakpointSmall) {
                              return 2;
                            } else if (MediaQuery.sizeOf(context).width <
                                kBreakpointMedium) {
                              return 4;
                            } else if (MediaQuery.sizeOf(context).width <
                                kBreakpointLarge) {
                              return 6;
                            } else {
                              return 6;
                            }
                          }(),
                          crossAxisSpacing: 10.0,
                          mainAxisSpacing: 16.0,
                          childAspectRatio: 0.8,
                        ),
                        scrollDirection: Axis.vertical,
                        itemCount: list.length,
                        itemBuilder: (context, discountItemsListIndex) {
                          final recentPost =
                              list[discountItemsListIndex];
                          return GestureDetector(
                            onTap: (){
                                    context.pushNamed(
                                'ProductDetailPage',
                                queryParameters: {
                                  'post': jsonEncode(recentPost.toJson()), // Serialize PostModel
                                 
                                }.withoutNulls,
                                extra: <String, dynamic>{
                                  kTransitionInfoKey: TransitionInfo(
                                    hasTransition: true,
                                    transitionType:
                                        PageTransitionType.rightToLeft,
                                    duration: Duration(milliseconds: 300),
                                  ),
                                },
                              );
                            },
                            child: Padding(
                              padding: EdgeInsetsDirectional.fromSTEB(
                                  6.0, 0.0, 6.0, 0.0),
                              child: Container(
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  color: ClassifiedAppTheme.of(context)
                                      .secondaryBackground,
                                  boxShadow: [
                                    BoxShadow(
                                      blurRadius: 4.0,
                                      color: Color(0x27000000),
                                      offset: Offset(0.0, 4.0),
                                    )
                                  ],
                                  borderRadius: BorderRadius.circular(12.0),
                                ),
                                child: Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(
                                      8.0, 8.0, 8.0, 8.0),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Expanded(
                                        child: Stack(
                                          alignment: AlignmentDirectional(
                                              1.0, -1.0),
                                          children: [
                                            ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(
                                                      8.0),
                                              child: CachedNetworkImage(
                                                fadeInDuration: Duration(
                                                    milliseconds: 500),
                                                fadeOutDuration: Duration(
                                                    milliseconds: 500),
                                                imageUrl:
                                                    "${ApiUtils.post_image}${recentPost.image}",
                                                width: double.infinity,
                                                height: 115.0,
                                                fit: BoxFit.cover,
                                              ),
                                            ),
                                            Padding(
                                              padding: EdgeInsetsDirectional
                                                  .fromSTEB(
                                                      0.0, 8.0, 8.0, 0.0),
                                              child: Container(
                                                width: 24.0,
                                                height: 24.0,
                                                decoration: BoxDecoration(
                                                  color: ClassifiedAppTheme
                                                          .of(context)
                                                      .secondaryBackground,
                                                  shape: BoxShape.circle,
                                                ),
                                                alignment:
                                                    AlignmentDirectional(
                                                        0.0, 0.0),
                                                child: SvgPicture.asset(
                                                  'assets/images/heart.svg',
                                                  width: 13.0,
                                                  height: 13.0,
                                                  fit: BoxFit.contain,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Expanded(
                                        child: Column(
                                          mainAxisSize: MainAxisSize.max,
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceEvenly,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            AutoSizeText(
                                              recentPost.post_name.toString(),
                                              maxLines: 1,
                                              style: ClassifiedAppTheme.of(
                                                      context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'Satoshi',
                                                    fontSize: 15.0,
                                                    fontWeight:
                                                        FontWeight.bold,
                                                    useGoogleFonts: false,
                                                  ),
                                              minFontSize: 13.0,
                                            ),
                                            Text(
                                               "AED ${recentPost
                                                  .price}",
                                              maxLines: 1,
                                              style: ClassifiedAppTheme.of(
                                                      context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'Satoshi',
                                                    fontSize: 12.0,
                                                    fontWeight:
                                                        FontWeight.bold,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                            Row(
                                              mainAxisSize:
                                                  MainAxisSize.max,
                                              children: [
                                                recentPost.vendor_image ==''? ClipRRect(
                                    borderRadius: BorderRadius.circular(30.0),
                                      child: Image.asset(
                                        'assets/images/soho_icon.jpg',
                                        width: 32.0,
                                        height: 32.0,
                                      
                                        fit: BoxFit.contain,
                                      ),
                                    ):
                                     ClipRRect(
                                    borderRadius: BorderRadius.circular(30.0),
                                    child: CachedNetworkImage(
                                      
                                     width: 32.0,
                                        height: 32.0,
                                      fit: BoxFit.cover,
                                        imageUrl:
                                            "${ApiUtils.profile_files}${recentPost.vendor_image}",
                                        placeholder: (context, url) => Padding(
                                          padding: const EdgeInsets.all(35.0),
                                          child: Center(child: CircularProgressIndicator()),
                                        ),
                                        errorWidget: (context, url, error) =>
                                            Icon(Icons.error),
                                      ),
                                     
                                  ),
                                                Padding(
                                                  padding:
                                                      EdgeInsetsDirectional
                                                          .fromSTEB(
                                                              8.0,
                                                              0.0,
                                                              0.0,
                                                              0.0),
                                                  child: Column(
                                                    mainAxisSize:
                                                        MainAxisSize.max,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                                                             Container(
              width: MediaQuery.of(context).size.width * 0.26, // Adjust based on your layout
              child: Text(
                recentPost.vendor_name.toString(),
                overflow: TextOverflow.fade,
                maxLines: 1,
                softWrap: false,
                style: ClassifiedAppTheme.of(context).bodyMedium.override(
                  fontFamily: 'Satoshi',
                  fontSize: 12.0,
                  fontWeight: FontWeight.bold,
                  useGoogleFonts: false,
                ),
              ),
            ),
                                                      Padding(
                                                        padding:
                                                            EdgeInsetsDirectional
                                                                .fromSTEB(
                                                                    0.0,
                                                                    4.0,
                                                                    0.0,
                                                                    0.0),
                                                        child: Row(
                                                          mainAxisSize:
                                                              MainAxisSize
                                                                  .max,
                                                          children: [
                                                            SvgPicture
                                                                .asset(
                                                              'assets/images/location-home.svg',
                                                              width: 14.0,
                                                              height: 14.0,
                                                              fit: BoxFit
                                                                  .cover,
                                                            ),
                                                            Padding(
                                                              padding: EdgeInsetsDirectional
                                                                  .fromSTEB(
                                                                      4.0,
                                                                      0.0,
                                                                      0.0,
                                                                      0.0),
                                                              child: Text(
                                                                recentPost
                                                                    .city.toString(),
                                                                style: ClassifiedAppTheme.of(
                                                                        context)
                                                                    .bodyMedium
                                                                    .override(
                                                                      fontFamily:
                                                                          'Satoshi',
                                                                      fontSize:
                                                                          12.0,
                                                                      fontWeight:
                                                                          FontWeight.w500,
                                                                      useGoogleFonts:
                                                                          false,
                                                                    ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      );
                    },
                  ),
                ),
              )
          ],
        ),
      ),
    );
  }
}
