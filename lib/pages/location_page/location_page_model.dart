import '../../Classified_App/classified_app_model.dart';
import '/pages/app_button/app_button_widget.dart';
import 'location_page_widget.dart' show LocationPageWidget;
import 'package:flutter/material.dart';

class LocationPageModel extends ClassifiedAppModel<LocationPageWidget> {
  ///  State fields for stateful widgets in this page.

  final unfocusNode = FocusNode();
  // Model for AppButton component.
  late AppButtonModel appButtonModel;

  /// Initialization and disposal methods.

  @override
  void initState(BuildContext context) {
    appButtonModel = createModel(context, () => AppButtonModel());
  }

  @override
  void dispose() {
    unfocusNode.dispose();
    appButtonModel.dispose();
  }

  /// Action blocks are added here.

  /// Additional helper methods are added here.
}
