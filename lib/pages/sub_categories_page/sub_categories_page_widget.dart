import 'package:flutter_svg/svg.dart';
import 'package:soho_souk/ApiUtils.dart';
import 'package:soho_souk/main.dart';
import 'package:soho_souk/models/CategoryModel.dart';

import '../../Classified_App/classified_app_theme.dart';
import '../../Classified_App/classified_app_util.dart';
import '/pages/app_bar/app_bar_widget.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'sub_categories_page_model.dart';
export 'sub_categories_page_model.dart';

class SubCategoriesPageWidget extends StatefulWidget {
  const SubCategoriesPageWidget({
    super.key,
    required this.title,
    int? index,
    this.image,
    this.barter,
  }) : this.index = index ?? 0;
  final bool?  barter;
  final String? title,image;
  final int index;

  @override
  State<SubCategoriesPageWidget> createState() =>
      _SubCategoriesPageWidgetState();
}

class _SubCategoriesPageWidgetState extends State<SubCategoriesPageWidget> {
  late SubCategoriesPageModel _model;

  final scaffoldKey = GlobalKey<ScaffoldState>();
  List<CategoryModels> list =[];
  @override
  void initState() {
    super.initState();
    list.clear();
    list.add(CategoryModels.fromJson({'id': 0, 'category': "All", 'image': widget.image,'parent_id': widget.index}));
    list.addAll(appStore.getMainCategoryData(widget.index));
    //list = appStore.getMainCategoryData(widget.index);
    _model = createModel(context, () => SubCategoriesPageModel());
  }

  @override
  void dispose() {
    _model.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
   // List<CategoryModels> list = appStore.getMainCategoryData(widget.index);
   
    context.watch<FFAppState>();

    return GestureDetector(
      onTap: () => _model.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_model.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: ClassifiedAppTheme.of(context).primaryBackground,
        body: SafeArea(
          top: true,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              wrapWithModel(
                model: _model.appBarModel,
                updateCallback: () => setState(() {}),
                child: AppBarWidget(
                  title: widget.title,
                ),
              ),
              Expanded(
                child: Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                  child: Builder(
                    builder: (context) {
                     // final subList = FFAppState().subCatList.toList();
                      return GridView.builder(
                        padding: EdgeInsets.fromLTRB(
                          0,
                          16.0,
                          0,
                          0,
                        ),
                        gridDelegate:
                            SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: 16.0,
                          mainAxisSpacing: 16.0,
                          childAspectRatio: 1.0,
                        ),
                        scrollDirection: Axis.vertical,
                        itemCount: list.length,
                        itemBuilder: (context, subListIndex) {
                          final subListItem = list[subListIndex];
                          return InkWell(
                            splashColor: Colors.transparent,
                            focusColor: Colors.transparent,
                            hoverColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            onTap: () async {

                           final list=   appStore.categoryList.where((i) => i.parent_id == subListItem.id).toList();
                            if(list.length>0 && subListItem.id!=0){
                              print("subListItem.parent_id ${subListItem.parent_id}");
                            context.pushNamed(
                'SubCategoriesPage',
                queryParameters: {
                  'title': serializeParam(
                    subListItem.category_name.toString(),
                    ParamType.String,
                  ),
                  'image': serializeParam(
                    subListItem.image.toString(),
                    ParamType.String,
                  ),
                  'index': serializeParam(
                    subListItem.id,
                    ParamType.int,
                  ),
                    'barter': serializeParam(
                    widget.barter,
                    ParamType.bool,
                  ),
                }.withoutNulls,
                extra: <String, dynamic>{
                  kTransitionInfoKey: TransitionInfo(
                    hasTransition: true,
                    transitionType: PageTransitionType.rightToLeft,
                    duration: Duration(milliseconds: 300),
                  ),
                },
              );

                            }else{

                               context.pushNamed(
                                'CategoryProductPage',
                                queryParameters: {
                                  'title': serializeParam(
                                    subListItem.category_name.toString(),
                                    ParamType.String,
                                  ),
                                  'index': serializeParam(
                                     subListItem.id==0?subListItem.parent_id: subListItem.id,
                                    ParamType.int,
                                  ),
                                  'isMaincategory': serializeParam(
                                    subListItem.id==0?true:false,
                                    ParamType.bool,
                                  ),
                                    'barter': serializeParam(
                                    widget.barter,
                                    ParamType.bool,
                                  ),
                                }.withoutNulls,
                                extra: <String, dynamic>{
                                  kTransitionInfoKey: TransitionInfo(
                                    hasTransition: true,
                                    transitionType:
                                        PageTransitionType.rightToLeft,
                                    duration: Duration(milliseconds: 300),
                                  ),
                                },
                              );
                            }


                              // context.pushNamed(
                              //   'CategoryProductPage',
                              //   extra: <String, dynamic>{
                              //     kTransitionInfoKey: TransitionInfo(
                              //       hasTransition: true,
                              //       transitionType:
                              //           PageTransitionType.rightToLeft,
                              //       duration: Duration(milliseconds: 300),
                              //     ),
                              //   },
                              // );
                            },
                            child:Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 6.0,
                    spreadRadius: 2.0,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(12.0),
                    ),
                    child: subListItem.image?.endsWith('.svg') ?? false
                        ? SvgPicture.network(
                            "${ApiUtils.admin_files}${subListItem.image}",
                            fit: BoxFit.cover,
                            height: 60,
                            color: ClassifiedAppTheme.of(context).primary,
                            width: double.infinity,
                            placeholderBuilder: (BuildContext context) => Container(
                              height: 60,
                              color: Colors.grey.shade200,
                              child: Center(
                                child: CircularProgressIndicator(),
                              ),
                            ),
                          )
                        : CachedNetworkImage(
                            imageUrl: "${ApiUtils.admin_files}${subListItem.image}",
                            fit: BoxFit.cover,
                            height: 60,
                            width: double.infinity,
                            errorWidget: (context, url, error) => Container(
                              height: 60,
                              color: Colors.grey.shade200,
                              child: Icon(Icons.broken_image,
                                  size: 40, color: Colors.grey),
                            ),
                          ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(
                      "${subListItem.category_name}",
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: 12.0,
                        fontWeight: FontWeight.bold,
                        color: ClassifiedAppTheme.of(context).primary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
                          );
                        },
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
