import 'package:soho_souk/Classified_App/classified_app_model.dart';

import '/backend/schema/structs/index.dart';
import '/pages/app_bar/app_bar_widget.dart';
import 'sub_categories_page_widget.dart' show SubCategoriesPageWidget;
import 'package:flutter/material.dart';

class SubCategoriesPageModel extends ClassifiedAppModel<SubCategoriesPageWidget> {
  ///  Local state fields for this page.

  String title = 'Text';

  List<SubCategoryModelStruct> dataList = [];
  void addToDataList(SubCategoryModelStruct item) => dataList.add(item);
  void removeFromDataList(SubCategoryModelStruct item) => dataList.remove(item);
  void removeAtIndexFromDataList(int index) => dataList.removeAt(index);
  void insertAtIndexInDataList(int index, SubCategoryModelStruct item) =>
      dataList.insert(index, item);
  void updateDataListAtIndex(
          int index, Function(SubCategoryModelStruct) updateFn) =>
      dataList[index] = updateFn(dataList[index]);

  ///  State fields for stateful widgets in this page.

  final unfocusNode = FocusNode();
  // Model for AppBar component.
  late AppBarModel appBarModel;

  /// Initialization and disposal methods.

  @override
  void initState(BuildContext context) {
    appBarModel = createModel(context, () => AppBarModel());
  }

  @override
  void dispose() {
    unfocusNode.dispose();
    appBarModel.dispose();
  }

  /// Action blocks are added here.

  /// Additional helper methods are added here.
}
