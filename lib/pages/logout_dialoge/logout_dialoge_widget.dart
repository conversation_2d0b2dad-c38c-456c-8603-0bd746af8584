import 'package:nb_utils/nb_utils.dart';
import 'package:soho_souk/main.dart';

import '../../Classified_App/classified_app_theme.dart';
import 'package:flutter/material.dart';
import '../../Classified_App/classified_app_util.dart';
import 'logout_dialoge_model.dart';
export 'logout_dialoge_model.dart';

class LogoutDialogeWidget extends StatefulWidget {
  const LogoutDialogeWidget({super.key});

  @override
  State<LogoutDialogeWidget> createState() => _LogoutDialogeWidgetState();
}

class _LogoutDialogeWidgetState extends State<LogoutDialogeWidget> {
  late LogoutDialogeModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => LogoutDialogeModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  void logout() async {
    toasty(context, "Logout Successfully!",
        bgColor: Colors.yellow, textColor: Colors.black);
    await appStore.logout();

    context.goNamed(
      'LoginPage',
      extra: <String, dynamic>{
        kTransitionInfoKey: TransitionInfo(
          hasTransition: true,
          transitionType: PageTransitionType.rightToLeft,
          duration: Duration(milliseconds: 300),
        ),
      },
    );

    setState(() {
      FFAppState().selectedPageIndex = 0;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
      child: Container(
        width: double.infinity,
        height: 185.0,
        decoration: BoxDecoration(
          color: ClassifiedAppTheme.of(context).secondaryBackground,
          borderRadius: BorderRadius.circular(16.0),
        ),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                textAlign: TextAlign.center,
                FFLocalizations.of(context).getText(
                  '1ax0zw6a' /* Are you sure you want to log o... */,
                ),
                style: ClassifiedAppTheme.of(context).bodyMedium.override(
                      fontFamily: 'Satoshi',
                      fontSize: 20.0,
                      fontWeight: FontWeight.bold,
                      useGoogleFonts: false,
                    ),
              ),
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(16.0, 32.0, 16.0, 0.0),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Padding(
                        padding:
                            EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 8.0, 0.0),
                        child: InkWell(
                          splashColor: Colors.transparent,
                          focusColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          onTap: () async {
                            context.safePop();
                          },
                          child: Container(
                            height: 56.0,
                            decoration: BoxDecoration(
                              color: ClassifiedAppTheme.of(context)
                                  .primaryBackground,
                              borderRadius: BorderRadius.circular(12.0),
                              border: Border.all(
                                color: ClassifiedAppTheme.of(context).primary,
                              ),
                            ),
                            alignment: AlignmentDirectional(0.0, 0.0),
                            child: Text(
                              FFLocalizations.of(context).getText(
                                '4gqavk8g' /* Cancel */,
                              ),
                              style: ClassifiedAppTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    color:
                                        ClassifiedAppTheme.of(context).primary,
                                    fontSize: 18.0,
                                    fontWeight: FontWeight.bold,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding:
                            EdgeInsetsDirectional.fromSTEB(8.0, 0.0, 0.0, 0.0),
                        child: InkWell(
                          splashColor: Colors.transparent,
                          focusColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          onTap: () async {
                            logout();
                          },
                          child: Container(
                            height: 56.0,
                            decoration: BoxDecoration(
                              color: ClassifiedAppTheme.of(context).primary,
                              borderRadius: BorderRadius.circular(12.0),
                            ),
                            alignment: AlignmentDirectional(0.0, 0.0),
                            child: Text(
                              FFLocalizations.of(context).getText(
                                '8ctgsiej' /* Log out */,
                              ),
                              style: ClassifiedAppTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    color: ClassifiedAppTheme.of(context)
                                        .primaryBackground,
                                    fontSize: 18.0,
                                    fontWeight: FontWeight.bold,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
