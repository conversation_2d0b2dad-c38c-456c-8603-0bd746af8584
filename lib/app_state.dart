import 'dart:convert';
import 'package:flutter/material.dart';
import '/backend/schema/structs/index.dart';

class FFAppState extends ChangeNotifier {
  static FFAppState _instance = FFAppState._internal();

  factory FFAppState() {
    return _instance;
  }

  FFAppState._internal();

  static void reset() {
    _instance = FFAppState._internal();
  }

  Future initializePersistedState() async {}

  void update(VoidCallback callback) {
    callback();
    notifyListeners();
  }

  int _introIndex = 0;
  int get introIndex => _introIndex;
  set introIndex(int _value) {
    _introIndex = _value;
  }

  int _selectedPageIndex = 0;
  int get selectedPageIndex => _selectedPageIndex;
  set selectedPageIndex(int _value) {
    _selectedPageIndex = _value;
  }

  int _productIndex = 0;
  int get productIndex => _productIndex;
  set productIndex(int _value) {
    _productIndex = _value;
  }

  List<PopularItemsModelStruct> _popularDataList = [
    PopularItemsModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/m2tnem8o8hd0/popularOne.png\",\"title\":\"iPhone 15 pro max \",\"subTitle\":\"\$1500.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/wutbeaaiq3ac/popUserOne.png\",\"userName\":\"Esther howards\",\"city\":\"New york\"}')),
    PopularItemsModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/njdqwtodrz7h/recommendThree.png\",\"title\":\"Ather 450x 2023 model\",\"subTitle\":\"\$1200.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/0a0obamzxf9n/popUserTwo.png\",\"userName\":\"Ronald richards\",\"city\":\"Broome\"}')),
    PopularItemsModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/lrdeg6yd560t/popularThree.png\",\"title\":\"Ola s1 pro 2023 model\",\"subTitle\":\"\$1900.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/92ssk3vmun2b/recommendUserTwo.png\",\"userName\":\"Robert fox\",\"city\":\"New york\"}')),
    PopularItemsModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/j5tqafin5zfs/popularFour.png\",\"title\":\"Macbook 2010 intel i5\",\"subTitle\":\"\$500.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/8ixzs3rog2l4/recommendUserOne.png\",\"userName\":\"Ronald richards\",\"city\":\"Broome\"}')),
    PopularItemsModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/72p8ky4vxzb7/popularFive.png\",\"title\":\"7 BHK Villa\",\"subTitle\":\"\$ 1,90,800.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/3koisilybqi8/featuredUserTwo.png\",\"userName\":\"John howards\",\"city\":\"New jersey\"}')),
    PopularItemsModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/v8a9mbhwnizv/popularSix.png\",\"title\":\"iPhone xs 2020 model\",\"subTitle\":\"\$300.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/0a0obamzxf9n/popUserTwo.png\",\"userName\":\"Ronald richards\",\"city\":\"Los vegas\"}')),
    PopularItemsModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/vpn05h2el93z/popularSeven.png\",\"title\":\"iPhone 15 pro max \",\"subTitle\":\"\$65.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/wutbeaaiq3ac/popUserOne.png\",\"userName\":\"Esther howards\",\"city\":\"New york\"}')),
    PopularItemsModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/jyeq4va0jzm7/popularEight.png\",\"title\":\"Macbook 2015 intel i7 \",\"subTitle\":\"\$300.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/0a0obamzxf9n/popUserTwo.png\",\"userName\":\"Ronald richards\",\"city\":\"Broome\"}'))
  ];
  List<PopularItemsModelStruct> get popularDataList => _popularDataList;
  set popularDataList(List<PopularItemsModelStruct> _value) {
    _popularDataList = _value;
  }

  void addToPopularDataList(PopularItemsModelStruct _value) {
    _popularDataList.add(_value);
  }

  void removeFromPopularDataList(PopularItemsModelStruct _value) {
    _popularDataList.remove(_value);
  }

  void removeAtIndexFromPopularDataList(int _index) {
    _popularDataList.removeAt(_index);
  }

  void updatePopularDataListAtIndex(
    int _index,
    PopularItemsModelStruct Function(PopularItemsModelStruct) updateFn,
  ) {
    _popularDataList[_index] = updateFn(_popularDataList[_index]);
  }

  void insertAtIndexInPopularDataList(
      int _index, PopularItemsModelStruct _value) {
    _popularDataList.insert(_index, _value);
  }

  List<RecommendedItemModelStruct> _recommendedDataList = [
    RecommendedItemModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/m2tnem8o8hd0/popularOne.png\",\"title\":\"iPhone 15 pro max \",\"subTitle\":\"\$1500.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/wutbeaaiq3ac/popUserOne.png\",\"userName\":\"Esther howards\",\"city\":\"New york\"}')),
    RecommendedItemModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/7u2ijhkgtybe/thePug.png\",\"title\":\"Golden hamster\",\"subTitle\":\"\$300.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/0a0obamzxf9n/popUserTwo.png\",\"userName\":\"Ronald richards\",\"city\":\"Broome\"}')),
    RecommendedItemModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/njdqwtodrz7h/recommendThree.png\",\"title\":\"Ather 450x 2023 model\",\"subTitle\":\"\$1200.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/92ssk3vmun2b/recommendUserTwo.png\",\"userName\":\"Robert fox\",\"city\":\"New york\"}')),
    RecommendedItemModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/j5tqafin5zfs/popularFour.png\",\"title\":\"Macbook 2010 intel i5\",\"subTitle\":\"\$500.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/8ixzs3rog2l4/recommendUserOne.png\",\"userName\":\"Ronald richards\",\"city\":\"Broome\"}')),
    RecommendedItemModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/72p8ky4vxzb7/popularFive.png\",\"title\":\"7 BHK Villa\",\"subTitle\":\"\$ 1,90,800.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/3koisilybqi8/featuredUserTwo.png\",\"userName\":\"John howards\",\"city\":\"New jersey\"}')),
    RecommendedItemModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/v8a9mbhwnizv/popularSix.png\",\"title\":\"iPhone xs 2020 model\",\"subTitle\":\"\$300.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/0a0obamzxf9n/popUserTwo.png\",\"userName\":\"Ronald richards\",\"city\":\"Los vegas\"}')),
    RecommendedItemModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/vpn05h2el93z/popularSeven.png\",\"title\":\"iPhone 15 pro max \",\"subTitle\":\"\$65.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/wutbeaaiq3ac/popUserOne.png\",\"userName\":\"Esther howards\",\"city\":\"New york\"}')),
    RecommendedItemModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/jyeq4va0jzm7/popularEight.png\",\"title\":\"Macbook 2015 intel i7 \",\"subTitle\":\"\$300.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/0a0obamzxf9n/popUserTwo.png\",\"userName\":\"Ronald richards\",\"city\":\"Broome\"}'))
  ];
  List<RecommendedItemModelStruct> get recommendedDataList =>
      _recommendedDataList;
  set recommendedDataList(List<RecommendedItemModelStruct> _value) {
    _recommendedDataList = _value;
  }

  void addToRecommendedDataList(RecommendedItemModelStruct _value) {
    _recommendedDataList.add(_value);
  }

  void removeFromRecommendedDataList(RecommendedItemModelStruct _value) {
    _recommendedDataList.remove(_value);
  }

  void removeAtIndexFromRecommendedDataList(int _index) {
    _recommendedDataList.removeAt(_index);
  }

  void updateRecommendedDataListAtIndex(
    int _index,
    RecommendedItemModelStruct Function(RecommendedItemModelStruct) updateFn,
  ) {
    _recommendedDataList[_index] = updateFn(_recommendedDataList[_index]);
  }

  void insertAtIndexInRecommendedDataList(
      int _index, RecommendedItemModelStruct _value) {
    _recommendedDataList.insert(_index, _value);
  }

  List<CategoryDataModelStruct> _categoriesDataList = [
    CategoryDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/zpjo34376y0f/categoryOne.png\",\"title\":\"Laptop\",\"subCategoryList\":\"[\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/wsckps56q7x9/laptopOne.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Asus\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/xdhz2c02ehhh/laptopTwo.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"HP\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/pvgyrfpo52e7/laptopThree.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Acer\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/2t4200wcaj54/laptopFour.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Apple\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/xjps0s95av2d/laptopFive.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Dell\\\\\\\"}\\\"]\"}')),
    CategoryDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/2fo3zsn9lwac/categoryTwo.png\",\"title\":\"Phone \",\"subCategoryList\":\"[\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/elqz6t5zm3hy/phoneOne.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Xiaomi\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/k3pyz3qsa3p4/phoneTwo.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Oppo\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/7lvf4zm46mtq/phoneThree.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Vivo\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/2t4200wcaj54/laptopFour.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Apple\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/vu0p1u9fxuiw/phoneFive.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Samsung\\\\\\\"}\\\"]\"}')),
    CategoryDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/nwkz7xsjb4z7/categoryThree.png\",\"title\":\"Clothes\",\"subCategoryList\":\"[\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/jhk71aeexpbk/clothesOne.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Adidas\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/uvpsngeejh1s/clothesTwo.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Nike\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/mv6500xnkico/clothesThree.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Gucci\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/bw89ai28db1b/clothesFour.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Raymond\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/gdzrt78x0gq1/clothesFive.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Chanel\\\\\\\"}\\\"]\"}')),
    CategoryDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/jq5v3v9a6zz2/categoryFour.png\",\"title\":\"Shoes\",\"subCategoryList\":\"[\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/jhk71aeexpbk/clothesOne.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Adidas\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/uvpsngeejh1s/clothesTwo.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Nike\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/dgg97cuj611b/shoesOne.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Bata\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/19dnt6gltnsv/shoesTwo.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Puma\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/4yb86ayc37si/shoesThree.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Woodland\\\\\\\"}\\\"]\"}')),
    CategoryDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/aa92flbk45b7/categoryFive.png\",\"title\":\"Pets\",\"subCategoryList\":\"[\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/19fdybl9w56n/petsOne.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Dog\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/0vqmx9usgems/petsTwo.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Cat\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/ahznhlr2y8mc/petsThree.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Birds\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/1e39llumnmqs/petsFour.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Hamster\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/i8q6xv2pa106/petsFive.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Turtel\\\\\\\"}\\\"]\"}')),
    CategoryDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/jri1kr0th5c8/categorySix.png\",\"title\":\"Toys\",\"subCategoryList\":\"[\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/46ofrmuihl2v/toysOne.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Teddy \\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/8k80v02mxx1u/toysTwo.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Doll house\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/tsljuabi0lzd/toysThree.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Magic car\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/rdkfe65nq8dk/toysFour.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Tricycle\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/gj5kt14vnnpw/toysFive.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Drum\\\\\\\"}\\\"]\"}')),
    CategoryDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/ncppadkt0jyf/categorySeven.png\",\"title\":\"Cars\",\"subCategoryList\":\"[\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/32k4o94lmhg4/carsOne.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Tata\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/ilh8cyix1r4f/carsTwo.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Hyundai\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/qziq03bse9m1/carsThree.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Toyota\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/nh9m3fx9dyn0/carsFour.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Mahindra\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/i2a2x80c6bxa/carsFive.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Renault\\\\\\\"}\\\"]\"}')),
    CategoryDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/sy18pgt37w8c/categoryEight.png\",\"title\":\"Real estate\",\"subCategoryList\":\"[\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/16brkyjnk0bk/realEstateOne.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Villa\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/tzprbwt1s9l4/realEstateTwo.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Town house\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/rl24xu69o4oa/realEstateThree.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Condos\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/u4ab4eyfnkr6/realEstateFour.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Apartment\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/gj5kt14vnnpw/toysFive.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Row house\\\\\\\"}\\\"]\"}')),
    CategoryDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/vjgb8zu1np9c/categoryNine.png\",\"title\":\"Bikes\",\"subCategoryList\":\"[\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/vy73w73yswif/bikesOne.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Honda\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/9phd9pdawack/bikesTwo.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Ola\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/gj174tbixpwb/bikesThree.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Ather\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/h3d70aerks6u/bikesFour.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Bajaj\\\\\\\"}\\\",\\\"{\\\\\\\"image\\\\\\\":\\\\\\\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/s6ibjol9viq7/bikesFive.png\\\\\\\",\\\\\\\"title\\\\\\\":\\\\\\\"Suzuki\\\\\\\"}\\\"]\"}'))
  ];
  List<CategoryDataModelStruct> get categoriesDataList => _categoriesDataList;
  set categoriesDataList(List<CategoryDataModelStruct> _value) {
    _categoriesDataList = _value;
  }

  void addToCategoriesDataList(CategoryDataModelStruct _value) {
    _categoriesDataList.add(_value);
  }

  void removeFromCategoriesDataList(CategoryDataModelStruct _value) {
    _categoriesDataList.remove(_value);
  }

  void removeAtIndexFromCategoriesDataList(int _index) {
    _categoriesDataList.removeAt(_index);
  }

  void updateCategoriesDataListAtIndex(
    int _index,
    CategoryDataModelStruct Function(CategoryDataModelStruct) updateFn,
  ) {
    _categoriesDataList[_index] = updateFn(_categoriesDataList[_index]);
  }

  void insertAtIndexInCategoriesDataList(
      int _index, CategoryDataModelStruct _value) {
    _categoriesDataList.insert(_index, _value);
  }

  List<LatestVenderDataModelStruct> _latestVenderDataList = [
    LatestVenderDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/p7vl7gpzjrv8/latestVendorOne.png\",\"title\":\"Shop point\"}')),
    LatestVenderDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/08w9monj6edp/latestVendorTwo.png\",\"title\":\"Shopping\"}')),
    LatestVenderDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/kr1955e6z0zu/latestVendorThree.png\",\"title\":\"Beshop\"}')),
    LatestVenderDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/iujsac5t923s/latestVendorFour.png\",\"title\":\"Bangla mart\"}')),
    LatestVenderDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/v5yrnszqwwfd/latestVendorFive.png\",\"title\":\"Afsara mart\"}')),
    LatestVenderDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/p7vl7gpzjrv8/latestVendorOne.png\",\"title\":\"Shop point\"}')),
    LatestVenderDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/idj4c5zizf82/latestVendorSeven.png\",\"title\":\"Deal shop\"}')),
    LatestVenderDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/08w9monj6edp/latestVendorTwo.png\",\"title\":\"Shopping\"}'))
  ];
  List<LatestVenderDataModelStruct> get latestVenderDataList =>
      _latestVenderDataList;
  set latestVenderDataList(List<LatestVenderDataModelStruct> _value) {
    _latestVenderDataList = _value;
  }

  void addToLatestVenderDataList(LatestVenderDataModelStruct _value) {
    _latestVenderDataList.add(_value);
  }

  void removeFromLatestVenderDataList(LatestVenderDataModelStruct _value) {
    _latestVenderDataList.remove(_value);
  }

  void removeAtIndexFromLatestVenderDataList(int _index) {
    _latestVenderDataList.removeAt(_index);
  }

  void updateLatestVenderDataListAtIndex(
    int _index,
    LatestVenderDataModelStruct Function(LatestVenderDataModelStruct) updateFn,
  ) {
    _latestVenderDataList[_index] = updateFn(_latestVenderDataList[_index]);
  }

  void insertAtIndexInLatestVenderDataList(
      int _index, LatestVenderDataModelStruct _value) {
    _latestVenderDataList.insert(_index, _value);
  }

  List<TopRatedSellersDataModelStruct> _TopRatedSellersDataList = [
    TopRatedSellersDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/wutbeaaiq3ac/popUserOne.png\",\"name\":\"Ronald richards\",\"items\":\"11 items\",\"followers\":\"10 followers\"}')),
    TopRatedSellersDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/8ixzs3rog2l4/recommendUserOne.png\",\"name\":\"Wade warren\",\"items\":\"10 items\",\"followers\":\"12 followers\"}')),
    TopRatedSellersDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/hywj5l6vcczp/featuredUserOne.png\",\"name\":\"Jane cooper\",\"items\":\"15 items\",\"followers\":\"20 followers\"}')),
    TopRatedSellersDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/92ssk3vmun2b/recommendUserTwo.png\",\"name\":\"Cameron williamson\",\"items\":\"18 items\",\"followers\":\"8 followers\"}')),
    TopRatedSellersDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/3koisilybqi8/featuredUserTwo.png\",\"name\":\"Guy hawkins\",\"items\":\"41 items\",\"followers\":\"30 followers\"}')),
    TopRatedSellersDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/0a0obamzxf9n/popUserTwo.png\",\"name\":\"Ralph edwards\",\"items\":\"21 items\",\"followers\":\"32 followers\"}')),
    TopRatedSellersDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/5ezuyw1ytx8n/chatFive.png\",\"name\":\"Kristin watson\",\"items\":\"11 items\",\"followers\":\"25 followers\"}')),
    TopRatedSellersDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/5ws8y3uzgav3/topratedOne.png\",\"name\":\"Cody fisher\",\"items\":\"05 items\",\"followers\":\"18 followers\"}'))
  ];
  List<TopRatedSellersDataModelStruct> get TopRatedSellersDataList =>
      _TopRatedSellersDataList;
  set TopRatedSellersDataList(List<TopRatedSellersDataModelStruct> _value) {
    _TopRatedSellersDataList = _value;
  }

  void addToTopRatedSellersDataList(TopRatedSellersDataModelStruct _value) {
    _TopRatedSellersDataList.add(_value);
  }

  void removeFromTopRatedSellersDataList(
      TopRatedSellersDataModelStruct _value) {
    _TopRatedSellersDataList.remove(_value);
  }

  void removeAtIndexFromTopRatedSellersDataList(int _index) {
    _TopRatedSellersDataList.removeAt(_index);
  }

  void updateTopRatedSellersDataListAtIndex(
    int _index,
    TopRatedSellersDataModelStruct Function(TopRatedSellersDataModelStruct)
        updateFn,
  ) {
    _TopRatedSellersDataList[_index] =
        updateFn(_TopRatedSellersDataList[_index]);
  }

  void insertAtIndexInTopRatedSellersDataList(
      int _index, TopRatedSellersDataModelStruct _value) {
    _TopRatedSellersDataList.insert(_index, _value);
  }

  List<DiscountItemsModelStruct> _RecentuploadedItemsDataList = [
    DiscountItemsModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/mxm01z6zgzmj/discountOne.png\",\"title\":\"Ivary bridal sandals\",\"subTitle\":\"\$65.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/wutbeaaiq3ac/popUserOne.png\",\"city\":\"New york\",\"userName\":\"Esther howards\"}')),
    DiscountItemsModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/zytyxk2mu1u6/whiteCat.png\",\"title\":\"Golden hamster\",\"subTitle\":\"\$300.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/0a0obamzxf9n/popUserTwo.png\",\"city\":\"Broome\",\"userName\":\"Ronald richards\"}')),
    DiscountItemsModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/pzavlod77ajl/discountThree.png\",\"title\":\"Automatic gents watch \",\"subTitle\":\"\$100.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/92ssk3vmun2b/recommendUserTwo.png\",\"city\":\"New york\",\"userName\":\"Robert fox\"}')),
    DiscountItemsModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/6wl3tiabhtkm/discountFour.png\",\"title\":\"12BHK villa\",\"subTitle\":\"\$ 1,90,800.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/3koisilybqi8/featuredUserTwo.png\",\"city\":\"Broome\",\"userName\":\"Ronald richards\"}')),
    DiscountItemsModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/wfonsu0ajf3f/discountFive.png\",\"title\":\"Mercedes amg G 63 \",\"subTitle\":\"\$ 1,90,800.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/0a0obamzxf9n/popUserTwo.png\",\"city\":\"New jersey\",\"userName\":\"John howards\"}')),
    DiscountItemsModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/ql1my2ajejec/discountSix.png\",\"title\":\"Classic gents watch \",\"subTitle\":\"\$300.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/92ssk3vmun2b/recommendUserTwo.png\",\"city\":\"Los vegas\",\"userName\":\"Ronald richards\"}')),
    DiscountItemsModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/vpn05h2el93z/popularSeven.png\",\"title\":\"iPhone 15 pro max \",\"subTitle\":\"\$65.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/wutbeaaiq3ac/popUserOne.png\",\"city\":\"New york\",\"userName\":\"Esther howards\"}')),
    DiscountItemsModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/jyeq4va0jzm7/popularEight.png\",\"title\":\"Macbook 2015 intel i7 \",\"subTitle\":\"\$300.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/0a0obamzxf9n/popUserTwo.png\",\"city\":\"Broome\",\"userName\":\"Ronald richards\"}'))
  ];
  List<DiscountItemsModelStruct> get RecentuploadedItemsDataList =>
      _RecentuploadedItemsDataList;
  set RecentuploadedItemsDataList(List<DiscountItemsModelStruct> _value) {
    _RecentuploadedItemsDataList = _value;
  }

  void addToRecentuploadedItemsDataList(DiscountItemsModelStruct _value) {
    _RecentuploadedItemsDataList.add(_value);
  }

  void removeFromRecentuploadedItemsDataList(DiscountItemsModelStruct _value) {
    _RecentuploadedItemsDataList.remove(_value);
  }

  void removeAtIndexFromRecentuploadedItemsDataList(int _index) {
    _RecentuploadedItemsDataList.removeAt(_index);
  }

  void updateRecentuploadedItemsDataListAtIndex(
    int _index,
    DiscountItemsModelStruct Function(DiscountItemsModelStruct) updateFn,
  ) {
    _RecentuploadedItemsDataList[_index] =
        updateFn(_RecentuploadedItemsDataList[_index]);
  }

  void insertAtIndexInRecentuploadedItemsDataList(
      int _index, DiscountItemsModelStruct _value) {
    _RecentuploadedItemsDataList.insert(_index, _value);
  }

  List<BuyingChatModelStruct> _BuyingChatDataList = [
    BuyingChatModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/5ws8y3uzgav3/topratedOne.png\",\"name\":\"Esther howard\",\"message\":\"Hello, good morning\",\"time\":\"08:00\"}')),
    BuyingChatModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/8wj6i1qcpjio/discountUserOne.png\",\"name\":\"Jane cooper\",\"message\":\"How are you dude\",\"time\":\"09:00\"}')),
    BuyingChatModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/0a0obamzxf9n/popUserTwo.png\",\"name\":\"Guy hawkins\",\"message\":\"What about you\",\"time\":\"10:00\"}')),
    BuyingChatModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/zxnzhfmv5h4s/chatFour.png\",\"name\":\"Leslie alexander\",\"message\":\"I am fine\",\"time\":\"11:00\"}')),
    BuyingChatModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/5ezuyw1ytx8n/chatFive.png\",\"name\":\"Ralph edwards\",\"message\":\"Awesome\",\"time\":\"18:00\"}')),
    BuyingChatModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/gllq2ft15jrd/chatSix.png\",\"name\":\"Jenny wilson\",\"message\":\"What are you doing\",\"time\":\"22:00\"}')),
    BuyingChatModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/3koisilybqi8/featuredUserTwo.png\",\"name\":\"Jacob jones\",\"message\":\"Awesome\",\"time\":\"22:00\"}'))
  ];
  List<BuyingChatModelStruct> get BuyingChatDataList => _BuyingChatDataList;
  set BuyingChatDataList(List<BuyingChatModelStruct> _value) {
    _BuyingChatDataList = _value;
  }

  void addToBuyingChatDataList(BuyingChatModelStruct _value) {
    _BuyingChatDataList.add(_value);
  }

  void removeFromBuyingChatDataList(BuyingChatModelStruct _value) {
    _BuyingChatDataList.remove(_value);
  }

  void removeAtIndexFromBuyingChatDataList(int _index) {
    _BuyingChatDataList.removeAt(_index);
  }

  void updateBuyingChatDataListAtIndex(
    int _index,
    BuyingChatModelStruct Function(BuyingChatModelStruct) updateFn,
  ) {
    _BuyingChatDataList[_index] = updateFn(_BuyingChatDataList[_index]);
  }

  void insertAtIndexInBuyingChatDataList(
      int _index, BuyingChatModelStruct _value) {
    _BuyingChatDataList.insert(_index, _value);
  }

  List<SellingChatDataModelStruct> _SellingChatDataList = [
    SellingChatDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/5ws8y3uzgav3/topratedOne.png\",\"name\":\"Esther howard\",\"message\":\"Hello, good morning\",\"time\":\"08:00\"}')),
    SellingChatDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/8wj6i1qcpjio/discountUserOne.png\",\"name\":\"Jane cooper\",\"message\":\"How are you dude\",\"time\":\"09:00\"}')),
    SellingChatDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/0a0obamzxf9n/popUserTwo.png\",\"name\":\"Guy hawkins\",\"message\":\"What about you\",\"time\":\"10:00\"}')),
    SellingChatDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/zxnzhfmv5h4s/chatFour.png\",\"name\":\"Leslie alexander\",\"message\":\"I am fine\",\"time\":\"11:00\"}')),
    SellingChatDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/5ezuyw1ytx8n/chatFive.png\",\"name\":\"Ralph edwards\",\"message\":\"Awesome\",\"time\":\"18:00\"}')),
    SellingChatDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/gllq2ft15jrd/chatSix.png\",\"name\":\"Jenny wilson\",\"message\":\"What are you doing\",\"time\":\"22:00\"}')),
    SellingChatDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/3koisilybqi8/featuredUserTwo.png\",\"name\":\"Jacob jones\",\"message\":\"Awesome\",\"time\":\"22:00\"}'))
  ];
  List<SellingChatDataModelStruct> get SellingChatDataList =>
      _SellingChatDataList;
  set SellingChatDataList(List<SellingChatDataModelStruct> _value) {
    _SellingChatDataList = _value;
  }

  void addToSellingChatDataList(SellingChatDataModelStruct _value) {
    _SellingChatDataList.add(_value);
  }

  void removeFromSellingChatDataList(SellingChatDataModelStruct _value) {
    _SellingChatDataList.remove(_value);
  }

  void removeAtIndexFromSellingChatDataList(int _index) {
    _SellingChatDataList.removeAt(_index);
  }

  void updateSellingChatDataListAtIndex(
    int _index,
    SellingChatDataModelStruct Function(SellingChatDataModelStruct) updateFn,
  ) {
    _SellingChatDataList[_index] = updateFn(_SellingChatDataList[_index]);
  }

  void insertAtIndexInSellingChatDataList(
      int _index, SellingChatDataModelStruct _value) {
    _SellingChatDataList.insert(_index, _value);
  }

  List<FavouriteDataModelStruct> _FavouriteDataList = [
    FavouriteDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/m2tnem8o8hd0/popularOne.png\",\"title\":\"iPhone 15 pro max \",\"subTitle\":\"\$1500.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/wutbeaaiq3ac/popUserOne.png\",\"userName\":\"Esther howards\",\"city\":\"New york\"}')),
    FavouriteDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/zytyxk2mu1u6/whiteCat.png\",\"title\":\"Golden hamster\",\"subTitle\":\"\$300.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/0a0obamzxf9n/popUserTwo.png\",\"userName\":\"Ronald richards\",\"city\":\"Broome\"}')),
    FavouriteDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/lrdeg6yd560t/popularThree.png\",\"title\":\"Ola s1 pro 2023 model\",\"subTitle\":\"\$1900.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/92ssk3vmun2b/recommendUserTwo.png\",\"userName\":\"Robert fox\",\"city\":\"New york\"}')),
    FavouriteDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/j5tqafin5zfs/popularFour.png\",\"title\":\"Macbook 2010 intel i5\",\"subTitle\":\"\$500.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/8ixzs3rog2l4/recommendUserOne.png\",\"userName\":\"Ronald richards\",\"city\":\"Broome\"}')),
    FavouriteDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/72p8ky4vxzb7/popularFive.png\",\"title\":\"7 BHK Villa\",\"subTitle\":\"\$ 1,90,800.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/3koisilybqi8/featuredUserTwo.png\",\"userName\":\"John howards\",\"city\":\"New jersey\"}')),
    FavouriteDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/v8a9mbhwnizv/popularSix.png\",\"title\":\"iPhone xs 2020 model\",\"subTitle\":\"\$300.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/0a0obamzxf9n/popUserTwo.png\",\"userName\":\"Ronald richards\",\"city\":\"Los vegas\"}')),
    FavouriteDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/vpn05h2el93z/popularSeven.png\",\"title\":\"iPhone 15 pro max \",\"subTitle\":\"\$65.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/wutbeaaiq3ac/popUserOne.png\",\"userName\":\"Esther howards\",\"city\":\"New york\"}')),
    FavouriteDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/jyeq4va0jzm7/popularEight.png\",\"title\":\"Macbook 2015 intel i7 \",\"subTitle\":\"\$300.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/0a0obamzxf9n/popUserTwo.png\",\"userName\":\"Ronald richards\",\"city\":\"Broome\"}'))
  ];
  List<FavouriteDataModelStruct> get FavouriteDataList => _FavouriteDataList;
  set FavouriteDataList(List<FavouriteDataModelStruct> _value) {
    _FavouriteDataList = _value;
  }

  void addToFavouriteDataList(FavouriteDataModelStruct _value) {
    _FavouriteDataList.add(_value);
  }

  void removeFromFavouriteDataList(FavouriteDataModelStruct _value) {
    _FavouriteDataList.remove(_value);
  }

  void removeAtIndexFromFavouriteDataList(int _index) {
    _FavouriteDataList.removeAt(_index);
  }

  void updateFavouriteDataListAtIndex(
    int _index,
    FavouriteDataModelStruct Function(FavouriteDataModelStruct) updateFn,
  ) {
    _FavouriteDataList[_index] = updateFn(_FavouriteDataList[_index]);
  }

  void insertAtIndexInFavouriteDataList(
      int _index, FavouriteDataModelStruct _value) {
    _FavouriteDataList.insert(_index, _value);
  }

  List<SubCategoryModelStruct> _subCatList = [];
  List<SubCategoryModelStruct> get subCatList => _subCatList;
  set subCatList(List<SubCategoryModelStruct> _value) {
    _subCatList = _value;
  }

  void addToSubCatList(SubCategoryModelStruct _value) {
    _subCatList.add(_value);
  }

  void removeFromSubCatList(SubCategoryModelStruct _value) {
    _subCatList.remove(_value);
  }

  void removeAtIndexFromSubCatList(int _index) {
    _subCatList.removeAt(_index);
  }

  void updateSubCatListAtIndex(
    int _index,
    SubCategoryModelStruct Function(SubCategoryModelStruct) updateFn,
  ) {
    _subCatList[_index] = updateFn(_subCatList[_index]);
  }

  void insertAtIndexInSubCatList(int _index, SubCategoryModelStruct _value) {
    _subCatList.insert(_index, _value);
  }

  List<PopularItemsModelStruct> _CategoryProductDataList = [
    PopularItemsModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/wxs1bdp73krl/asusOne.png\",\"title\":\"ASUS ROG strix G15\",\"subTitle\":\"\$300,00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/wutbeaaiq3ac/popUserOne.png\",\"userName\":\"Esther howards\",\"city\":\"New york\"}')),
    PopularItemsModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/oowmju9tmttj/asusTwo.png\",\"title\":\"Asus vivobook 3\",\"subTitle\":\"\$180.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/0a0obamzxf9n/popUserTwo.png\",\"userName\":\"Ronald richards\",\"city\":\"Broome\"}')),
    PopularItemsModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/wrye5l71pbx2/asusThree.png\",\"title\":\"Asus Vivobook 16X\",\"subTitle\":\"\$200.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/92ssk3vmun2b/recommendUserTwo.png\",\"userName\":\"Robert fox\",\"city\":\"New york\"}')),
    PopularItemsModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/kjzc076zzbcf/asusFour.png\",\"title\":\"Asus Vivobook 15\",\"subTitle\":\"\$ 500.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/8ixzs3rog2l4/recommendUserOne.png\",\"userName\":\"Ronald richards\",\"city\":\"Broome\"}')),
    PopularItemsModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/a65qt60kdkuo/asusFive.png\",\"title\":\"Asus ROG G15\",\"subTitle\":\"\$ 150.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/3koisilybqi8/featuredUserTwo.png\",\"userName\":\"John howards\",\"city\":\"New jersey\"}')),
    PopularItemsModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/av8kb204aass/asusSix.png\",\"title\":\"Golden hamster\",\"subTitle\":\"\$280.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/0a0obamzxf9n/popUserTwo.png\",\"userName\":\"Ronald richards\",\"city\":\"Los vegas\"}')),
    PopularItemsModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/jyeq4va0jzm7/popularEight.png\",\"title\":\"iPhone 15 pro max \",\"subTitle\":\"\$65.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/wutbeaaiq3ac/popUserOne.png\",\"userName\":\"Esther howards\",\"city\":\"New york\"}')),
    PopularItemsModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/j5tqafin5zfs/popularFour.png\",\"title\":\"Macbook 2015 intel i7 \",\"subTitle\":\"\$300.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/0a0obamzxf9n/popUserTwo.png\",\"userName\":\"Ronald richards\",\"city\":\"Broome\"}'))
  ];
  List<PopularItemsModelStruct> get CategoryProductDataList =>
      _CategoryProductDataList;
  set CategoryProductDataList(List<PopularItemsModelStruct> _value) {
    _CategoryProductDataList = _value;
  }

  void addToCategoryProductDataList(PopularItemsModelStruct _value) {
    _CategoryProductDataList.add(_value);
  }

  void removeFromCategoryProductDataList(PopularItemsModelStruct _value) {
    _CategoryProductDataList.remove(_value);
  }

  void removeAtIndexFromCategoryProductDataList(int _index) {
    _CategoryProductDataList.removeAt(_index);
  }

  void updateCategoryProductDataListAtIndex(
    int _index,
    PopularItemsModelStruct Function(PopularItemsModelStruct) updateFn,
  ) {
    _CategoryProductDataList[_index] =
        updateFn(_CategoryProductDataList[_index]);
  }

  void insertAtIndexInCategoryProductDataList(
      int _index, PopularItemsModelStruct _value) {
    _CategoryProductDataList.insert(_index, _value);
  }

  List<FeaturedItemsDataModelStruct> _featuredDataList = [
    FeaturedItemsDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/xp4088q4ckei/featuredOne.png\",\"title\":\"Mercedes-Benz-S-class\",\"subTitle\":\"\$102.000,00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/wutbeaaiq3ac/popUserOne.png\",\"userName\":\"Esther howards\",\"city\":\"New york\"}')),
    FeaturedItemsDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/zytyxk2mu1u6/whiteCat.png\",\"title\":\"Automatic gents watch  \",\"subTitle\":\"\$80.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/0a0obamzxf9n/popUserTwo.png\",\"userName\":\"Ronald richards\",\"city\":\"Broome\"}')),
    FeaturedItemsDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/m2tnem8o8hd0/popularOne.png\",\"title\":\"iPhone 15 pro max \",\"subTitle\":\"\$1500.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/92ssk3vmun2b/recommendUserTwo.png\",\"userName\":\"Robert fox\",\"city\":\"New york\"}')),
    FeaturedItemsDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/j5tqafin5zfs/popularFour.png\",\"title\":\"Macbook 2010 intel i5\",\"subTitle\":\"\$ 500.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/8ixzs3rog2l4/recommendUserOne.png\",\"userName\":\"Ronald richards\",\"city\":\"Broome\"}')),
    FeaturedItemsDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/njdqwtodrz7h/recommendThree.png\",\"title\":\"Ather 450x 2023 model\",\"subTitle\":\"\$ 1200.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/3koisilybqi8/featuredUserTwo.png\",\"userName\":\"John howards\",\"city\":\"New jersey\"}')),
    FeaturedItemsDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/y6zjneyygkvt/popularTwo.png\",\"title\":\"Golden hamster\",\"subTitle\":\"\$80.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/0a0obamzxf9n/popUserTwo.png\",\"userName\":\"Ronald richards\",\"city\":\"Los vegas\"}')),
    FeaturedItemsDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/vpn05h2el93z/popularSeven.png\",\"title\":\"iPhone 15 pro max \",\"subTitle\":\"\$65.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/wutbeaaiq3ac/popUserOne.png\",\"userName\":\"Esther howards\",\"city\":\"New york\"}')),
    FeaturedItemsDataModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/jyeq4va0jzm7/popularEight.png\",\"title\":\"Macbook 2015 intel i7 \",\"subTitle\":\"\$300.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/0a0obamzxf9n/popUserTwo.png\",\"userName\":\"Ronald richards\",\"city\":\"Broome\"}'))
  ];
  List<FeaturedItemsDataModelStruct> get featuredDataList => _featuredDataList;
  set featuredDataList(List<FeaturedItemsDataModelStruct> _value) {
    _featuredDataList = _value;
  }

  void addToFeaturedDataList(FeaturedItemsDataModelStruct _value) {
    _featuredDataList.add(_value);
  }

  void removeFromFeaturedDataList(FeaturedItemsDataModelStruct _value) {
    _featuredDataList.remove(_value);
  }

  void removeAtIndexFromFeaturedDataList(int _index) {
    _featuredDataList.removeAt(_index);
  }

  void updateFeaturedDataListAtIndex(
    int _index,
    FeaturedItemsDataModelStruct Function(FeaturedItemsDataModelStruct)
        updateFn,
  ) {
    _featuredDataList[_index] = updateFn(_featuredDataList[_index]);
  }

  void insertAtIndexInFeaturedDataList(
      int _index, FeaturedItemsDataModelStruct _value) {
    _featuredDataList.insert(_index, _value);
  }

  List<NewDiscountItemModelStruct> _DiscountItemsDataList = [
    NewDiscountItemModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/xp4088q4ckei/featuredOne.png\",\"title\":\"Mercedes-Benz-S-class\",\"discountPrice\":\"\$102.0\",\"price\":\"\$100.0\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/wutbeaaiq3ac/popUserOne.png\",\"userName\":\"Esther howards\",\"city\":\"New york\"}')),
    NewDiscountItemModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/f5773eg7cn1l/featuredTwo.png\",\"title\":\"Automatic gents watch \",\"discountPrice\":\"\$80.00\",\"price\":\"\$50.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/0a0obamzxf9n/popUserTwo.png\",\"userName\":\"Ronald richards\",\"city\":\"Broome\"}')),
    NewDiscountItemModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/m2tnem8o8hd0/popularOne.png\",\"title\":\"iPhone 15 pro max \",\"discountPrice\":\"\$1500.00\",\"price\":\"\$1400.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/92ssk3vmun2b/recommendUserTwo.png\",\"userName\":\"Robert fox\",\"city\":\"New york\"}')),
    NewDiscountItemModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/j5tqafin5zfs/popularFour.png\",\"title\":\"Macbook 2010 intel i5\",\"discountPrice\":\"\$ 500.00\",\"price\":\"\$450.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/8ixzs3rog2l4/recommendUserOne.png\",\"userName\":\"Ronald richards\",\"city\":\"Broome\"}')),
    NewDiscountItemModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/njdqwtodrz7h/recommendThree.png\",\"title\":\"Ather 450x 2023 model\",\"discountPrice\":\"\$ 1200.00\",\"price\":\"\$1100.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/3koisilybqi8/featuredUserTwo.png\",\"userName\":\"John howards\",\"city\":\"New jersey\"}')),
    NewDiscountItemModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/zytyxk2mu1u6/whiteCat.png\",\"title\":\"Golden hamster\",\"discountPrice\":\"\$80.00\",\"price\":\"\$70.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/0a0obamzxf9n/popUserTwo.png\",\"userName\":\"Ronald richards\",\"city\":\"Los vegas\"}')),
    NewDiscountItemModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/vpn05h2el93z/popularSeven.png\",\"title\":\"iPhone 15 pro max \",\"discountPrice\":\"\$65.00\",\"price\":\"\$50.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/wutbeaaiq3ac/popUserOne.png\",\"userName\":\"Esther howards\",\"city\":\"New york\"}')),
    NewDiscountItemModelStruct.fromSerializableMap(jsonDecode(
        '{\"image\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/jyeq4va0jzm7/popularEight.png\",\"title\":\"Macbook 2015 intel i7 \",\"discountPrice\":\"\$300.00\",\"price\":\"\$50.00\",\"userImage\":\"https://storage.googleapis.com/flutterflow-io-6f20.appspot.com/projects/classified-app-1ccx48/assets/0a0obamzxf9n/popUserTwo.png\",\"userName\":\"Ronald richards\",\"city\":\"Broome\"}'))
  ];
  List<NewDiscountItemModelStruct> get DiscountItemsDataList =>
      _DiscountItemsDataList;
  set DiscountItemsDataList(List<NewDiscountItemModelStruct> _value) {
    _DiscountItemsDataList = _value;
  }

  void addToDiscountItemsDataList(NewDiscountItemModelStruct _value) {
    _DiscountItemsDataList.add(_value);
  }

  void removeFromDiscountItemsDataList(NewDiscountItemModelStruct _value) {
    _DiscountItemsDataList.remove(_value);
  }

  void removeAtIndexFromDiscountItemsDataList(int _index) {
    _DiscountItemsDataList.removeAt(_index);
  }

  void updateDiscountItemsDataListAtIndex(
    int _index,
    NewDiscountItemModelStruct Function(NewDiscountItemModelStruct) updateFn,
  ) {
    _DiscountItemsDataList[_index] = updateFn(_DiscountItemsDataList[_index]);
  }

  void insertAtIndexInDiscountItemsDataList(
      int _index, NewDiscountItemModelStruct _value) {
    _DiscountItemsDataList.insert(_index, _value);
  }

  String _selectedCategory = '';
  String get selectedCategory => _selectedCategory;
  set selectedCategory(String _value) {
    _selectedCategory = _value;
  }
  int?_selectedCategoryId =0;
  int get selectedCategoryId => _selectedCategoryId!;
  set selectedCategoryId(int _value) {
    _selectedCategoryId = _value;
    _selectedChildCategory='';
    _selectedChildCategoryId=null;
  }

  String _selectedChildCategory = '';
  String get selectedChildCategory => _selectedChildCategory;
  set selectedChildCategory(String _value) {
    _selectedChildCategory = _value;
  }
  int?_selectedChildCategoryId ;
  int get selectedChildCategoryId => _selectedChildCategoryId!;
  set selectedChildCategoryId(int _value) {
    _selectedChildCategoryId = _value;
  }
  bool haveSecondCat=false;
  bool haveChilsCat=false;
  String _selectedSecondChildCategory = '';
  String get selectedSecondChildCategory => _selectedSecondChildCategory;
  set selectedSecondChildCategory(String _value) {
    _selectedSecondChildCategory = _value;
  }
  int?_selectedSecondChildCategoryId;
  int get selectedSecondChildCategoryId => _selectedSecondChildCategoryId!;
  set selectedSecondChildCategoryId(int _value) {
    _selectedSecondChildCategoryId = _value;
  }
  List<NotificationModelStruct> _notificationList = [
    NotificationModelStruct.fromSerializableMap(jsonDecode(
        '{\"title\":\"Clearance sale\",\"message\":\"Get amazing deals on past-season favorites.\",\"time\":\"30 seconds ago\"}')),
    NotificationModelStruct.fromSerializableMap(jsonDecode(
        '{\"title\":\"Limited-time offer\",\"message\":\"Own a piece of history with our vintage collection.\",\"time\":\"5 min ago\"}')),
    NotificationModelStruct.fromSerializableMap(jsonDecode(
        '{\"title\":\"Final days\",\"message\":\"Everything must go in our massive clearance sale.\",\"time\":\"2 hours ago\"}')),
    NotificationModelStruct.fromSerializableMap(jsonDecode(
        '{\"title\":\"Quantities limited\",\"message\":\"Shop our pre-owned items before they\'re gone!\",\"time\":\"15 hours ago\"}')),
    NotificationModelStruct.fromSerializableMap(jsonDecode(
        '{\"title\":\"Sustainable shopping\",\"message\":\"Find high-quality pre-owned items at a fraction of the cost.\",\"time\":\"a few seconds ago\"}'))
  ];
  List<NotificationModelStruct> get notificationList => _notificationList;
  set notificationList(List<NotificationModelStruct> _value) {
    _notificationList = _value;
  }

  void addToNotificationList(NotificationModelStruct _value) {
    _notificationList.add(_value);
  }

  void removeFromNotificationList(NotificationModelStruct _value) {
    _notificationList.remove(_value);
  }

  void removeAtIndexFromNotificationList(int _index) {
    _notificationList.removeAt(_index);
  }

  void updateNotificationListAtIndex(
    int _index,
    NotificationModelStruct Function(NotificationModelStruct) updateFn,
  ) {
    _notificationList[_index] = updateFn(_notificationList[_index]);
  }

  void insertAtIndexInNotificationList(
      int _index, NotificationModelStruct _value) {
    _notificationList.insert(_index, _value);
  }
}
