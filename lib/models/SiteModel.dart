class SiteModel {
  String? terms_and_conditions,privacy_policy,about_us,staying_safe;

  SiteModel._(
      {
      this.terms_and_conditions,
      this.privacy_policy,
      this.about_us,
      this.staying_safe,
      });
  factory SiteModel.fromJson(Map<String, dynamic> json) {
    return SiteModel._(
      terms_and_conditions: json['terms_and_conditions']??'',
      privacy_policy: json['privacy_policy']??'',
      about_us: json['about_us']??'',
      staying_safe: json['staying_safe']??''
    );
  }
}