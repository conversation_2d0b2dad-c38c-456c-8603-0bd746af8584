class SliderModel {
  int? id, ad_id;
  String? title, button_text, image;

  SliderModel._(
      {
      this.id,
      this.image,
      this.ad_id,
      this.button_text,
      this.title});
  factory SliderModel.fromJson(Map<String, dynamic> json) {
    return SliderModel._(
      id: json['id'],
      image: json['image'],
      ad_id: json['ad_id'],
      button_text: json['button_text'],
      title: json['title'],
    );
  }
}