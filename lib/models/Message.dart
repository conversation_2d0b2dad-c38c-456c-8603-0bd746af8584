import 'package:cloud_firestore/cloud_firestore.dart';

class Message {
  final String? sender_id, reciver_id, message;
  final Timestamp? timestamp;

  late bool? fromMe;
  // bool showTime = true;

  Message(
      {this.message,
      this.reciver_id,
      this.fromMe,
      this.sender_id,
      this.timestamp});
  
  Map<String, dynamic> toMap() {
    return {
      "sender_id": sender_id,
      "reciver_id": reciver_id,
      "message": message,
      "timestamp": timestamp,
    };
  }


}
