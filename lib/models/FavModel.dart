class FavModel {
  int? id;
  String? post_name,image,price,city,vendor_name,vendor_image;

  FavModel._(
      {
      this.id,
      this.image,
      this.post_name,
      this.city,
      this.vendor_name,
      this.vendor_image,
      this.price
      });
  factory FavModel.fromJson(Map<String, dynamic> json) {
    return FavModel._(
      id: json['id'],
      image: json['image'],
      post_name: json['post_name'],
      price: json['price'],
      city: json['city'],
      vendor_name: json['vendor_name'],
      vendor_image: json['vendor_image'] ==null?'':json['vendor_image'],
    );
  }
}