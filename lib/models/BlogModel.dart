class BlogModel {
 int? id;
 String? title,image,description,date;

  BlogModel({
    this.id,
    this.image,
    this.title,
    this.description,
    this.date,
  });

  factory BlogModel.fromJson(Map<String, dynamic> json) {
    return BlogModel(
      id: json['id'],
      image: json['image'],
      title: json['title'],
      description: json['description'],
      date: json['date'],
   // Assuming the default value for fav is false
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'image': image,
      'description': description,
      'date': date,
   
    };
  }
}
