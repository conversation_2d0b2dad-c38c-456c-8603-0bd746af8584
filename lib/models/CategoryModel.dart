class CategoryModels {
  int? id, parent_id;
  String? category_name,image;

  CategoryModels._(
      {
      this.id,
      this.image,
      this.parent_id,
      this.category_name});
  factory CategoryModels.fromJson(Map<String, dynamic> json) {
    return CategoryModels._(
      id: json['id'],
      image: json['image'],
      parent_id: json['parent_id'],
      category_name: json['category'],
    );
  }
}