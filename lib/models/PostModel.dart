class PostModel {
  int? id, category, sub_category,childcategory, barter_enable, post_type, vendor_id, price,show_mobile,enable_sms ;
  String? post_name, image, city, area, item_conditions, vendor_name, vendor_image, description, mobile,created_at;
  bool fav;
  PostModel({
    this.id,
    this.image,
    this.category,
    this.childcategory,
    this.post_name,
    this.area,
    this.city,
    this.item_conditions,
    this.vendor_name,
    this.vendor_image,
    this.price,
    this.sub_category,
    this.barter_enable,
    this.post_type,
    required this.fav,
    this.vendor_id,
    this.description,
    this.mobile,
    this.show_mobile,
    this.enable_sms,
    this.created_at,

  });

  factory PostModel.fromJson(Map<String, dynamic> json) {
    return PostModel(
      id: json['id'],
      image: json['image'],
      category: json['category'] != null ? int.tryParse(json['category'].toString()) : null,
      sub_category: json['subcategory'] != null ? int.tryParse(json['subcategory'].toString()) : 0,
      barter_enable: json['barter_enable'],
      post_type: json['post_type'],
      post_name: json['post_name'],
      price: json['price'],
      city: json['city'],
      area: json['area'],
      item_conditions: json['item_conditions'],
      vendor_name: json['vendor_name'],
      vendor_id: json['vendor_id'],
      description: json['description'],
      mobile: json['mobile'],
      show_mobile: json['show_mobile'],
      enable_sms: json['enable_sms'],
      vendor_image: json['vendor_image']?.toString() ?? '',
      childcategory: json['childcategory'] != null ? int.tryParse(json['childcategory'].toString()) : 0,
      fav:json['fav'] ?? false, // Assuming the default value for fav is false
      created_at: json['created_at'] ,
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'post_name': post_name,
      'image': image,
      'category': category,
      'subcategory': sub_category,
      'childcategory': childcategory,
      'barter_enable': barter_enable,
      'post_type': post_type,
      'price': price,
      'city': city,
      'area': area,
      'item_conditions': item_conditions,
      'description': description,
      'mobile': mobile,
      'vendor_name': vendor_name,
      'vendor_image': vendor_image,
      'vendor_id': vendor_id,
      'fav': fav,
      'show_mobile': show_mobile,
      'enable_sms': enable_sms,
      'created_at': created_at,
    };
  }
}
