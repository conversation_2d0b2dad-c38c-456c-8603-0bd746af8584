// ignore_for_file: unnecessary_getters_setters

import '/backend/schema/util/schema_util.dart';

import 'index.dart';
import '../../../Classified_App/nav/serialization_util.dart';

class LatestVenderDataModelStruct extends BaseStruct {
  LatestVenderDataModelStruct({
    String? image,
    String? title,
  })  : _image = image,
        _title = title;

  // "image" field.
  String? _image;
  String get image => _image ?? '';
  set image(String? val) => _image = val;
  bool hasImage() => _image != null;

  // "title" field.
  String? _title;
  String get title => _title ?? '';
  set title(String? val) => _title = val;
  bool hasTitle() => _title != null;

  static LatestVenderDataModelStruct fromMap(Map<String, dynamic> data) =>
      LatestVenderDataModelStruct(
        image: data['image'] as String?,
        title: data['title'] as String?,
      );

  static LatestVenderDataModelStruct? maybeFromMap(dynamic data) => data is Map
      ? LatestVenderDataModelStruct.fromMap(data.cast<String, dynamic>())
      : null;

  Map<String, dynamic> toMap() => {
        'image': _image,
        'title': _title,
      }.withoutNulls;

  @override
  Map<String, dynamic> toSerializableMap() => {
        'image': serializeParam(
          _image,
          ParamType.String,
        ),
        'title': serializeParam(
          _title,
          ParamType.String,
        ),
      }.withoutNulls;

  static LatestVenderDataModelStruct fromSerializableMap(
          Map<String, dynamic> data) =>
      LatestVenderDataModelStruct(
        image: deserializeParam(
          data['image'],
          ParamType.String,
          false,
        ),
        title: deserializeParam(
          data['title'],
          ParamType.String,
          false,
        ),
      );

  @override
  String toString() => 'LatestVenderDataModelStruct(${toMap()})';

  @override
  bool operator ==(Object other) {
    return other is LatestVenderDataModelStruct &&
        image == other.image &&
        title == other.title;
  }

  @override
  int get hashCode => const ListEquality().hash([image, title]);
}

LatestVenderDataModelStruct createLatestVenderDataModelStruct({
  String? image,
  String? title,
}) =>
    LatestVenderDataModelStruct(
      image: image,
      title: title,
    );
