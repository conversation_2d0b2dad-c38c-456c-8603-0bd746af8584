// ignore_for_file: unnecessary_getters_setters


import '../../../Classified_App/nav/serialization_util.dart';
import 'index.dart';

class CategoryDataModelStruct extends BaseStruct {
  CategoryDataModelStruct({
    String? image,
    String? title,
    List<SubCategoryModelStruct>? subCategoryList,
  })  : _image = image,
        _title = title,
        _subCategoryList = subCategoryList;

  // "image" field.
  String? _image;
  String get image => _image ?? '';
  set image(String? val) => _image = val;
  bool hasImage() => _image != null;

  // "title" field.
  String? _title;
  String get title => _title ?? '';
  set title(String? val) => _title = val;
  bool hasTitle() => _title != null;

  // "subCategoryList" field.
  List<SubCategoryModelStruct>? _subCategoryList;
  List<SubCategoryModelStruct> get subCategoryList =>
      _subCategoryList ?? const [];
  set subCategoryList(List<SubCategoryModelStruct>? val) =>
      _subCategoryList = val;
  void updateSubCategoryList(Function(List<SubCategoryModelStruct>) updateFn) =>
      updateFn(_subCategoryList ??= []);
  bool hasSubCategoryList() => _subCategoryList != null;

  static CategoryDataModelStruct fromMap(Map<String, dynamic> data) =>
      CategoryDataModelStruct(
        image: data['image'] as String?,
        title: data['title'] as String?,
        subCategoryList: getStructList(
          data['subCategoryList'],
          SubCategoryModelStruct.fromMap,
        ),
      );

  static CategoryDataModelStruct? maybeFromMap(dynamic data) => data is Map
      ? CategoryDataModelStruct.fromMap(data.cast<String, dynamic>())
      : null;

  Map<String, dynamic> toMap() => {
        'image': _image,
        'title': _title,
        'subCategoryList': _subCategoryList?.map((e) => e.toMap()).toList(),
      }.withoutNulls;

  @override
  Map<String, dynamic> toSerializableMap() => {
        'image': serializeParam(
          _image,
          ParamType.String,
        ),
        'title': serializeParam(
          _title,
          ParamType.String,
        ),
        'subCategoryList': serializeParam(
          _subCategoryList,
          ParamType.DataStruct,
          true,
        ),
      }.withoutNulls;

  static CategoryDataModelStruct fromSerializableMap(
          Map<String, dynamic> data) =>
      CategoryDataModelStruct(
        image: deserializeParam(
          data['image'],
          ParamType.String,
          false,
        ),
        title: deserializeParam(
          data['title'],
          ParamType.String,
          false,
        ),
        subCategoryList: deserializeStructParam<SubCategoryModelStruct>(
          data['subCategoryList'],
          ParamType.DataStruct,
          true,
          structBuilder: SubCategoryModelStruct.fromSerializableMap,
        ),
      );

  @override
  String toString() => 'CategoryDataModelStruct(${toMap()})';

  @override
  bool operator ==(Object other) {
    const listEquality = ListEquality();
    return other is CategoryDataModelStruct &&
        image == other.image &&
        title == other.title &&
        listEquality.equals(subCategoryList, other.subCategoryList);
  }

  @override
  int get hashCode =>
      const ListEquality().hash([image, title, subCategoryList]);
}

CategoryDataModelStruct createCategoryDataModelStruct({
  String? image,
  String? title,
}) =>
    CategoryDataModelStruct(
      image: image,
      title: title,
    );
