// ignore_for_file: unnecessary_getters_setters

import '/backend/schema/util/schema_util.dart';

import 'index.dart';
import '../../../Classified_App/nav/serialization_util.dart';

class NewDiscountItemModelStruct extends BaseStruct {
  NewDiscountItemModelStruct({
    String? image,
    String? title,
    String? discountPrice,
    String? price,
    String? userImage,
    String? userName,
    String? city,
  })  : _image = image,
        _title = title,
        _discountPrice = discountPrice,
        _price = price,
        _userImage = userImage,
        _userName = userName,
        _city = city;

  // "image" field.
  String? _image;
  String get image => _image ?? '';
  set image(String? val) => _image = val;
  bool hasImage() => _image != null;

  // "title" field.
  String? _title;
  String get title => _title ?? '';
  set title(String? val) => _title = val;
  bool hasTitle() => _title != null;

  // "discountPrice" field.
  String? _discountPrice;
  String get discountPrice => _discountPrice ?? '';
  set discountPrice(String? val) => _discountPrice = val;
  bool hasDiscountPrice() => _discountPrice != null;

  // "price" field.
  String? _price;
  String get price => _price ?? '';
  set price(String? val) => _price = val;
  bool hasPrice() => _price != null;

  // "userImage" field.
  String? _userImage;
  String get userImage => _userImage ?? '';
  set userImage(String? val) => _userImage = val;
  bool hasUserImage() => _userImage != null;

  // "userName" field.
  String? _userName;
  String get userName => _userName ?? '';
  set userName(String? val) => _userName = val;
  bool hasUserName() => _userName != null;

  // "city" field.
  String? _city;
  String get city => _city ?? '';
  set city(String? val) => _city = val;
  bool hasCity() => _city != null;

  static NewDiscountItemModelStruct fromMap(Map<String, dynamic> data) =>
      NewDiscountItemModelStruct(
        image: data['image'] as String?,
        title: data['title'] as String?,
        discountPrice: data['discountPrice'] as String?,
        price: data['price'] as String?,
        userImage: data['userImage'] as String?,
        userName: data['userName'] as String?,
        city: data['city'] as String?,
      );

  static NewDiscountItemModelStruct? maybeFromMap(dynamic data) => data is Map
      ? NewDiscountItemModelStruct.fromMap(data.cast<String, dynamic>())
      : null;

  Map<String, dynamic> toMap() => {
        'image': _image,
        'title': _title,
        'discountPrice': _discountPrice,
        'price': _price,
        'userImage': _userImage,
        'userName': _userName,
        'city': _city,
      }.withoutNulls;

  @override
  Map<String, dynamic> toSerializableMap() => {
        'image': serializeParam(
          _image,
          ParamType.String,
        ),
        'title': serializeParam(
          _title,
          ParamType.String,
        ),
        'discountPrice': serializeParam(
          _discountPrice,
          ParamType.String,
        ),
        'price': serializeParam(
          _price,
          ParamType.String,
        ),
        'userImage': serializeParam(
          _userImage,
          ParamType.String,
        ),
        'userName': serializeParam(
          _userName,
          ParamType.String,
        ),
        'city': serializeParam(
          _city,
          ParamType.String,
        ),
      }.withoutNulls;

  static NewDiscountItemModelStruct fromSerializableMap(
          Map<String, dynamic> data) =>
      NewDiscountItemModelStruct(
        image: deserializeParam(
          data['image'],
          ParamType.String,
          false,
        ),
        title: deserializeParam(
          data['title'],
          ParamType.String,
          false,
        ),
        discountPrice: deserializeParam(
          data['discountPrice'],
          ParamType.String,
          false,
        ),
        price: deserializeParam(
          data['price'],
          ParamType.String,
          false,
        ),
        userImage: deserializeParam(
          data['userImage'],
          ParamType.String,
          false,
        ),
        userName: deserializeParam(
          data['userName'],
          ParamType.String,
          false,
        ),
        city: deserializeParam(
          data['city'],
          ParamType.String,
          false,
        ),
      );

  @override
  String toString() => 'NewDiscountItemModelStruct(${toMap()})';

  @override
  bool operator ==(Object other) {
    return other is NewDiscountItemModelStruct &&
        image == other.image &&
        title == other.title &&
        discountPrice == other.discountPrice &&
        price == other.price &&
        userImage == other.userImage &&
        userName == other.userName &&
        city == other.city;
  }

  @override
  int get hashCode => const ListEquality()
      .hash([image, title, discountPrice, price, userImage, userName, city]);
}

NewDiscountItemModelStruct createNewDiscountItemModelStruct({
  String? image,
  String? title,
  String? discountPrice,
  String? price,
  String? userImage,
  String? userName,
  String? city,
}) =>
    NewDiscountItemModelStruct(
      image: image,
      title: title,
      discountPrice: discountPrice,
      price: price,
      userImage: userImage,
      userName: userName,
      city: city,
    );
