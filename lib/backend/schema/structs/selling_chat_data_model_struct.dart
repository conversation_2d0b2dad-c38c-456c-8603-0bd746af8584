// ignore_for_file: unnecessary_getters_setters

import '/backend/schema/util/schema_util.dart';

import 'index.dart';
import '../../../Classified_App/nav/serialization_util.dart';

class SellingChatDataModelStruct extends BaseStruct {
  SellingChatDataModelStruct({
    String? image,
    String? name,
    String? message,
    String? time,
  })  : _image = image,
        _name = name,
        _message = message,
        _time = time;

  // "image" field.
  String? _image;
  String get image => _image ?? '';
  set image(String? val) => _image = val;
  bool hasImage() => _image != null;

  // "name" field.
  String? _name;
  String get name => _name ?? '';
  set name(String? val) => _name = val;
  bool hasName() => _name != null;

  // "message" field.
  String? _message;
  String get message => _message ?? '';
  set message(String? val) => _message = val;
  bool hasMessage() => _message != null;

  // "time" field.
  String? _time;
  String get time => _time ?? '';
  set time(String? val) => _time = val;
  bool hasTime() => _time != null;

  static SellingChatDataModelStruct fromMap(Map<String, dynamic> data) =>
      SellingChatDataModelStruct(
        image: data['image'] as String?,
        name: data['name'] as String?,
        message: data['message'] as String?,
        time: data['time'] as String?,
      );

  static SellingChatDataModelStruct? maybeFromMap(dynamic data) => data is Map
      ? SellingChatDataModelStruct.fromMap(data.cast<String, dynamic>())
      : null;

  Map<String, dynamic> toMap() => {
        'image': _image,
        'name': _name,
        'message': _message,
        'time': _time,
      }.withoutNulls;

  @override
  Map<String, dynamic> toSerializableMap() => {
        'image': serializeParam(
          _image,
          ParamType.String,
        ),
        'name': serializeParam(
          _name,
          ParamType.String,
        ),
        'message': serializeParam(
          _message,
          ParamType.String,
        ),
        'time': serializeParam(
          _time,
          ParamType.String,
        ),
      }.withoutNulls;

  static SellingChatDataModelStruct fromSerializableMap(
          Map<String, dynamic> data) =>
      SellingChatDataModelStruct(
        image: deserializeParam(
          data['image'],
          ParamType.String,
          false,
        ),
        name: deserializeParam(
          data['name'],
          ParamType.String,
          false,
        ),
        message: deserializeParam(
          data['message'],
          ParamType.String,
          false,
        ),
        time: deserializeParam(
          data['time'],
          ParamType.String,
          false,
        ),
      );

  @override
  String toString() => 'SellingChatDataModelStruct(${toMap()})';

  @override
  bool operator ==(Object other) {
    return other is SellingChatDataModelStruct &&
        image == other.image &&
        name == other.name &&
        message == other.message &&
        time == other.time;
  }

  @override
  int get hashCode => const ListEquality().hash([image, name, message, time]);
}

SellingChatDataModelStruct createSellingChatDataModelStruct({
  String? image,
  String? name,
  String? message,
  String? time,
}) =>
    SellingChatDataModelStruct(
      image: image,
      name: name,
      message: message,
      time: time,
    );
