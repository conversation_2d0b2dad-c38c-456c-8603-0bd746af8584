// ignore_for_file: unnecessary_getters_setters

import '/backend/schema/util/schema_util.dart';

import 'index.dart';
import '../../../Classified_App/nav/serialization_util.dart';

class TopRatedSellersDataModelStruct extends BaseStruct {
  TopRatedSellersDataModelStruct({
    String? image,
    String? name,
    String? items,
    String? followers,
  })  : _image = image,
        _name = name,
        _items = items,
        _followers = followers;

  // "image" field.
  String? _image;
  String get image => _image ?? '';
  set image(String? val) => _image = val;
  bool hasImage() => _image != null;

  // "name" field.
  String? _name;
  String get name => _name ?? '';
  set name(String? val) => _name = val;
  bool hasName() => _name != null;

  // "items" field.
  String? _items;
  String get items => _items ?? '';
  set items(String? val) => _items = val;
  bool hasItems() => _items != null;

  // "followers" field.
  String? _followers;
  String get followers => _followers ?? '';
  set followers(String? val) => _followers = val;
  bool hasFollowers() => _followers != null;

  static TopRatedSellersDataModelStruct fromMap(Map<String, dynamic> data) =>
      TopRatedSellersDataModelStruct(
        image: data['image'] as String?,
        name: data['name'] as String?,
        items: data['items'] as String?,
        followers: data['followers'] as String?,
      );

  static TopRatedSellersDataModelStruct? maybeFromMap(dynamic data) =>
      data is Map
          ? TopRatedSellersDataModelStruct.fromMap(data.cast<String, dynamic>())
          : null;

  Map<String, dynamic> toMap() => {
        'image': _image,
        'name': _name,
        'items': _items,
        'followers': _followers,
      }.withoutNulls;

  @override
  Map<String, dynamic> toSerializableMap() => {
        'image': serializeParam(
          _image,
          ParamType.String,
        ),
        'name': serializeParam(
          _name,
          ParamType.String,
        ),
        'items': serializeParam(
          _items,
          ParamType.String,
        ),
        'followers': serializeParam(
          _followers,
          ParamType.String,
        ),
      }.withoutNulls;

  static TopRatedSellersDataModelStruct fromSerializableMap(
          Map<String, dynamic> data) =>
      TopRatedSellersDataModelStruct(
        image: deserializeParam(
          data['image'],
          ParamType.String,
          false,
        ),
        name: deserializeParam(
          data['name'],
          ParamType.String,
          false,
        ),
        items: deserializeParam(
          data['items'],
          ParamType.String,
          false,
        ),
        followers: deserializeParam(
          data['followers'],
          ParamType.String,
          false,
        ),
      );

  @override
  String toString() => 'TopRatedSellersDataModelStruct(${toMap()})';

  @override
  bool operator ==(Object other) {
    return other is TopRatedSellersDataModelStruct &&
        image == other.image &&
        name == other.name &&
        items == other.items &&
        followers == other.followers;
  }

  @override
  int get hashCode =>
      const ListEquality().hash([image, name, items, followers]);
}

TopRatedSellersDataModelStruct createTopRatedSellersDataModelStruct({
  String? image,
  String? name,
  String? items,
  String? followers,
}) =>
    TopRatedSellersDataModelStruct(
      image: image,
      name: name,
      items: items,
      followers: followers,
    );
