// ignore_for_file: unnecessary_getters_setters

import '/backend/schema/util/schema_util.dart';

import 'index.dart';
import '../../../Classified_App/nav/serialization_util.dart';

class FeaturedItemsDataModelStruct extends BaseStruct {
  FeaturedItemsDataModelStruct({
    String? image,
    String? title,
    String? subTitle,
    String? userImage,
    String? userName,
    String? city,
  })  : _image = image,
        _title = title,
        _subTitle = subTitle,
        _userImage = userImage,
        _userName = userName,
        _city = city;

  // "image" field.
  String? _image;
  String get image => _image ?? '';
  set image(String? val) => _image = val;
  bool hasImage() => _image != null;

  // "title" field.
  String? _title;
  String get title => _title ?? '';
  set title(String? val) => _title = val;
  bool hasTitle() => _title != null;

  // "subTitle" field.
  String? _subTitle;
  String get subTitle => _subTitle ?? '';
  set subTitle(String? val) => _subTitle = val;
  bool hasSubTitle() => _subTitle != null;

  // "userImage" field.
  String? _userImage;
  String get userImage => _userImage ?? '';
  set userImage(String? val) => _userImage = val;
  bool hasUserImage() => _userImage != null;

  // "userName" field.
  String? _userName;
  String get userName => _userName ?? '';
  set userName(String? val) => _userName = val;
  bool hasUserName() => _userName != null;

  // "city" field.
  String? _city;
  String get city => _city ?? '';
  set city(String? val) => _city = val;
  bool hasCity() => _city != null;

  static FeaturedItemsDataModelStruct fromMap(Map<String, dynamic> data) =>
      FeaturedItemsDataModelStruct(
        image: data['image'] as String?,
        title: data['title'] as String?,
        subTitle: data['subTitle'] as String?,
        userImage: data['userImage'] as String?,
        userName: data['userName'] as String?,
        city: data['city'] as String?,
      );

  static FeaturedItemsDataModelStruct? maybeFromMap(dynamic data) => data is Map
      ? FeaturedItemsDataModelStruct.fromMap(data.cast<String, dynamic>())
      : null;

  Map<String, dynamic> toMap() => {
        'image': _image,
        'title': _title,
        'subTitle': _subTitle,
        'userImage': _userImage,
        'userName': _userName,
        'city': _city,
      }.withoutNulls;

  @override
  Map<String, dynamic> toSerializableMap() => {
        'image': serializeParam(
          _image,
          ParamType.String,
        ),
        'title': serializeParam(
          _title,
          ParamType.String,
        ),
        'subTitle': serializeParam(
          _subTitle,
          ParamType.String,
        ),
        'userImage': serializeParam(
          _userImage,
          ParamType.String,
        ),
        'userName': serializeParam(
          _userName,
          ParamType.String,
        ),
        'city': serializeParam(
          _city,
          ParamType.String,
        ),
      }.withoutNulls;

  static FeaturedItemsDataModelStruct fromSerializableMap(
          Map<String, dynamic> data) =>
      FeaturedItemsDataModelStruct(
        image: deserializeParam(
          data['image'],
          ParamType.String,
          false,
        ),
        title: deserializeParam(
          data['title'],
          ParamType.String,
          false,
        ),
        subTitle: deserializeParam(
          data['subTitle'],
          ParamType.String,
          false,
        ),
        userImage: deserializeParam(
          data['userImage'],
          ParamType.String,
          false,
        ),
        userName: deserializeParam(
          data['userName'],
          ParamType.String,
          false,
        ),
        city: deserializeParam(
          data['city'],
          ParamType.String,
          false,
        ),
      );

  @override
  String toString() => 'FeaturedItemsDataModelStruct(${toMap()})';

  @override
  bool operator ==(Object other) {
    return other is FeaturedItemsDataModelStruct &&
        image == other.image &&
        title == other.title &&
        subTitle == other.subTitle &&
        userImage == other.userImage &&
        userName == other.userName &&
        city == other.city;
  }

  @override
  int get hashCode => const ListEquality()
      .hash([image, title, subTitle, userImage, userName, city]);
}

FeaturedItemsDataModelStruct createFeaturedItemsDataModelStruct({
  String? image,
  String? title,
  String? subTitle,
  String? userImage,
  String? userName,
  String? city,
}) =>
    FeaturedItemsDataModelStruct(
      image: image,
      title: title,
      subTitle: subTitle,
      userImage: userImage,
      userName: userName,
      city: city,
    );
