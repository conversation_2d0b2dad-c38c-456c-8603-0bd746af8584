import 'dart:io';

import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:soho_souk/ApiUtils.dart';
import 'package:soho_souk/main.dart';
import 'package:soho_souk/models/PostModel.dart';
import 'package:soho_souk/pages/logout_dialoge/logout_dialoge_widget.dart';
import 'package:soho_souk/pages/profile_page/profile_page_model.dart';
import 'package:soho_souk/service/serviceApi.dart';
import 'package:soho_souk/widget/loader.dart';
import '../../Classified_App/classified_app_animations.dart';
import '../../Classified_App/classified_app_theme.dart';
import '../../Classified_App/classified_app_util.dart';
import '../../Classified_App/classified_app_widgets.dart';
import 'home_tab_model.dart';
export 'home_tab_model.dart';

class HomeTabWidget extends StatefulWidget {
  const HomeTabWidget({super.key});

  @override
  State<HomeTabWidget> createState() => _HomeTabWidgetState();
}

class _HomeTabWidgetState extends State<HomeTabWidget>
    with TickerProviderStateMixin {
  late HomeTabModel _model;
  late ProfilePageModel _models;
  final animationsMap = {
    'carouselOnPageLoadAnimation': AnimationInfo(
      trigger: AnimationTrigger.onPageLoad,
      effects: [
        FadeEffect(
          curve: Curves.easeInOut,
          delay: 50.ms,
          duration: 300.ms,
          begin: 0.15,
          end: 1.0,
        ),
      ],
    ),
    'gridViewOnPageLoadAnimation': AnimationInfo(
      trigger: AnimationTrigger.onPageLoad,
      effects: [
        FadeEffect(
          curve: Curves.easeInOut,
          delay: 20.ms,
          duration: 400.ms,
          begin: 0.15,
          end: 1.0,
        ),
      ],
    ),
    'stackOnPageLoadAnimation': AnimationInfo(
      trigger: AnimationTrigger.onPageLoad,
      effects: [
        FadeEffect(
          curve: Curves.easeInOut,
          delay: 20.ms,
          duration: 300.ms,
          begin: 0.15,
          end: 1.0,
        ),
      ],
    ),
  };

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => HomeTabModel());

    _model.textController ??= TextEditingController();
    _model.textFieldFocusNode ??= FocusNode();
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  // Function to simulate data refresh
  Future<void> _refreshData() async {
    showLoadingDialog(context);
    appStore.setAllPost(await ServiceApi().getAllPost());
    setState(() {});
    Navigator.of(context, rootNavigator: true).pop(false);
  }

  DateTime? lastPressed;

  Future<bool> _onWillPop() async {
    final now = DateTime.now();
    final maxDuration = Duration(seconds: 2);

    if (lastPressed == null || now.difference(lastPressed!) > maxDuration) {
      lastPressed = now;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          backgroundColor: ClassifiedAppTheme.of(context).primary,
          content: Text(
            'Press again to exit',
            style: TextStyle(color: Colors.white),
          ),
          duration: maxDuration,
        ),
      );
      return false; // Prevent app from closing
    }
    return true; // Allow exit
  }

  void _exitApp() async {
    if (await _onWillPop()) {
      if (Platform.isAndroid) {
        SystemNavigator.pop(); // Close app on Android
      } else if (Platform.isIOS) {
        exit(0); // Force exit on iOS (not recommended by Apple)
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false, // Prevents default back behavior
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          _exitApp(); // Handle back press
        }
      },
      child: RefreshIndicator(
        color: ClassifiedAppTheme.of(context).primary,
        backgroundColor: ClassifiedAppTheme.of(context).tertiary,
        onRefresh: _refreshData,
        child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            Container(
              decoration: BoxDecoration(
                color: ClassifiedAppTheme.of(context).secondaryBackground,
                boxShadow: [
                  BoxShadow(
                    blurRadius: 8.0,
                    color: Color(0x27000000),
                    offset: Offset(0.0, 5.0),
                  )
                ],
                // borderRadius: BorderRadius.circular(12.0),
              ),
              child: Padding(
                padding: EdgeInsetsDirectional.fromSTEB(16.0, 24.0, 16.0, 10.0),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                        height: 40,
                        child: Image.asset('assets/images/soho-logo.png')),
                    SizedBox(
                      width: 10,
                    ),
                    // Expanded(
                    //   child: Padding(
                    //     padding: EdgeInsetsDirectional.fromSTEB(4.0, 0.0, 4.0, 0.0),
                    //     child: Text(
                    //      "Hello Guest",
                    //       style: ClassifiedAppTheme.of(context).bodyMedium.override(
                    //             fontFamily: 'Satoshi',
                    //             fontSize: 18.0,
                    //             fontWeight: FontWeight.bold,
                    //             useGoogleFonts: false,
                    //           ),
                    //     ),
                    //   ),
                    // ),
                    // Opacity(
                    //   opacity: 0.0,
                    //   child: InkWell(
                    //     splashColor: Colors.transparent,
                    //     focusColor: Colors.transparent,
                    //     hoverColor: Colors.transparent,
                    //     highlightColor: Colors.transparent,
                    //     onTap: () async {
                    //       context.pushNamed(
                    //         'LocationPage',
                    //         extra: <String, dynamic>{
                    //           kTransitionInfoKey: TransitionInfo(
                    //             hasTransition: true,
                    //             transitionType: PageTransitionType.rightToLeft,
                    //             duration: Duration(milliseconds: 300),
                    //           ),
                    //         },
                    //       );
                    //     },
                    //     child: Container(
                    //       width: 36.0,
                    //       height: 36.0,
                    //       decoration: BoxDecoration(
                    //         color: ClassifiedAppTheme.of(context).secondaryBackground,
                    //         borderRadius: BorderRadius.circular(8.0),
                    //         border: Border.all(
                    //           color: ClassifiedAppTheme.of(context).info,
                    //         ),
                    //       ),
                    //       alignment: AlignmentDirectional(0.0, 0.0),
                    //       child: SvgPicture.asset(
                    //         'assets/images/location-home.svg',
                    //         width: 22.0,
                    //         height: 22.0,
                    //         fit: BoxFit.contain,
                    //       ),
                    //     ),
                    //   ),
                    // ),
                    Padding(
                      padding:
                          EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 0.0, 0.0),
                      child: InkWell(
                          splashColor: Colors.transparent,
                          focusColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          onTap: () async {
                            // context.pushNamed(
                            //   'NotificationPage',
                            //   extra: <String, dynamic>{
                            //     kTransitionInfoKey: TransitionInfo(
                            //       hasTransition: true,
                            //       transitionType: PageTransitionType.rightToLeft,
                            //       duration: Duration(milliseconds: 300),
                            //     ),
                            //   },
                            // );
                          },
                          child: PopupMenuTheme(
                            data: PopupMenuThemeData(
                              color: Colors
                                  .white, // Set the popup menu background color to white
                            ),
                            child: PopupMenuButton<String>(
                              onSelected: (value) {
                                // Handle the selected menu item
                                switch (value) {
                                  case 'profile':
                                    context.pushNamed(
                                      'ProfilePage',
                                      extra: <String, dynamic>{
                                        kTransitionInfoKey: TransitionInfo(
                                          hasTransition: true,
                                          transitionType:
                                              PageTransitionType.rightToLeft,
                                          duration: Duration(milliseconds: 300),
                                        ),
                                      },
                                    );
                                    break;
                                  case 'settings':
                                    context.pushNamed(
                                      'SettingsPage',
                                      extra: <String, dynamic>{
                                        kTransitionInfoKey: TransitionInfo(
                                          hasTransition: true,
                                          transitionType:
                                              PageTransitionType.rightToLeft,
                                          duration: Duration(milliseconds: 300),
                                        ),
                                      },
                                    );
                                    break;
                                  // case 'logout':
                                  //     showDialog(
                                  //                           context: context,
                                  //                           builder: (dialogContext) {
                                  //                             return Dialog(
                                  //                               elevation: 0,
                                  //                               insetPadding: EdgeInsets.zero,
                                  //                               backgroundColor: Colors.transparent,
                                  //                               alignment: AlignmentDirectional(0.0, 0.0)
                                  //                                   .resolve(Directionality.of(context)),
                                  //                               child: GestureDetector(
                                  //                                 onTap: () => _models
                                  //                                         .unfocusNode.canRequestFocus
                                  //                                     ? FocusScope.of(context)
                                  //                                         .requestFocus(
                                  //                                             _models.unfocusNode)
                                  //                                     : FocusScope.of(context).unfocus(),
                                  //                                 child: LogoutDialogeWidget(),
                                  //                               ),
                                  //                             );
                                  //                           },
                                  //                         ).then((value) => setState(() {}));
                                  //   break;
                                  // case 'login':
                                  //   context.pushNamed(
                                  //     'LoginPage',
                                  //     extra: <String, dynamic>{
                                  //       kTransitionInfoKey: TransitionInfo(
                                  //         hasTransition: true,
                                  //         transitionType: PageTransitionType.rightToLeft,
                                  //         duration: Duration(milliseconds: 300),
                                  //       ),
                                  //     },
                                  //   );
                                  //   break;
                                }
                              },
                              itemBuilder: (BuildContext context) {
                                return [
                                  PopupMenuItem(
                                    value: 'profile',
                                    child: Row(
                                      children: [
                                        Icon(Icons.person,
                                            color:
                                                ClassifiedAppTheme.of(context)
                                                    .primary), // Icon
                                        SizedBox(
                                            width: 8.0), // Add some spacing
                                        Text(
                                            FFLocalizations.of(context).getText(
                                              '6re5xuuo' /* Profile */,
                                            ),
                                            style: TextStyle(
                                                color: ClassifiedAppTheme.of(
                                                        context)
                                                    .primary)), // Text
                                      ],
                                    ),
                                  ),
                                  PopupMenuItem(
                                    value: 'settings',
                                    child: Row(
                                      children: [
                                        Icon(Icons.menu,
                                            color:
                                                ClassifiedAppTheme.of(context)
                                                    .primary), // Icon
                                        SizedBox(
                                            width: 8.0), // Add some spacing
                                        Text(
                                            FFLocalizations.of(context).getText(
                                              'oh4fn0bs' /* Menu */,
                                            ),
                                            style: TextStyle(
                                                color: ClassifiedAppTheme.of(
                                                        context)
                                                    .primary)), // Text
                                      ],
                                    ),
                                  ),
                                  //   appStore.logined ? PopupMenuItem(
                                  //   value: 'logout',
                                  //   child: Row(
                                  //     children: [
                                  //       Icon(Icons.logout, color:  ClassifiedAppTheme.of(context).primary), // Icon
                                  //       SizedBox(width: 8.0), // Add some spacing
                                  //       Text('Logout', style: TextStyle(color:  ClassifiedAppTheme.of(context).primary)), // Text
                                  //     ],
                                  //   ),
                                  // ): PopupMenuItem(
                                  //   value: 'login',
                                  //   child: Row(
                                  //     children: [
                                  //       Icon(Icons.login, color:  ClassifiedAppTheme.of(context).primary), // Icon
                                  //       SizedBox(width: 8.0), // Add some spacing
                                  //       Text('Login', style: TextStyle(color:  ClassifiedAppTheme.of(context).primary)), // Text
                                  //     ],
                                  //   ),
                                  // ),
                                ];
                              },
                              child: Container(
                                width: 36.0,
                                height: 36.0,
                                decoration: BoxDecoration(
                                  color:
                                      ClassifiedAppTheme.of(context).tertiary,
                                  borderRadius: BorderRadius.circular(8.0),
                                  border: Border.all(
                                    color:
                                        ClassifiedAppTheme.of(context).tertiary,
                                  ),
                                ),
                                alignment: AlignmentDirectional(0.0, 0.0),
                                child: Icon(
                                  Icons.more_vert,
                                  color: ClassifiedAppTheme.of(context).primary,
                                ),
                              ),
                            ),
                          )),
                    ),
                  ],
                ),
              ),
            ),
            // SizedBox(height: 15,),
            //     Container(
            //   height:  15,
            //   decoration: BoxDecoration(
            //       boxShadow: <BoxShadow>[
            //         BoxShadow(
            //             color: Colors.black54,
            //             blurRadius: 15.0,
            //             offset: Offset(0.0, 0.75)
            //         )
            //       ],
            //     color: Colors.white
            //   ),
            // ),
            Expanded(
              child: ListView(
                padding: EdgeInsets.fromLTRB(
                  0,
                  0,
                  0,
                  24.0,
                ),
                shrinkWrap: true,
                scrollDirection: Axis.vertical,
                children: [
                  // Padding(
                  //   padding: EdgeInsetsDirectional.fromSTEB(0.0, 10.0, 0.0, 0.0),
                  //   child: Container(
                  //     width: double.infinity,
                  //     height: 159.0,
                  //     child: CarouselSlider(
                  //       items: appStore.sliderList.map((data) {
                  //         return Stack(
                  //           children: [
                  //             ClipRRect(
                  //               borderRadius: BorderRadius.circular(8.0),
                  //               child: CachedNetworkImage(
                  //                 width: double.infinity,
                  //                 height: 159.0,
                  //                 fit: BoxFit.contain,
                  //                 imageUrl: "${ApiUtils.admin_files}${data.image}",
                  //                 placeholder: (context, url) => Padding(
                  //                   padding: const EdgeInsets.all(35.0),
                  //                   child:
                  //                       Center(child: CircularProgressIndicator()),
                  //                 ),
                  //                 errorWidget: (context, url, error) =>
                  //                     Icon(Icons.error),
                  //               ),
                  //             ),
                  //             Padding(
                  //               padding: EdgeInsetsDirectional.fromSTEB(
                  //                   32.0, 16.0, 0.0, 0.0),
                  //               child: Column(
                  //                 mainAxisSize: MainAxisSize.max,
                  //                 mainAxisAlignment: MainAxisAlignment.center,
                  //                 crossAxisAlignment: CrossAxisAlignment.start,
                  //                 children: [
                  //                   Text(
                  //                     "${data.title}",
                  //                     style: ClassifiedAppTheme.of(context)
                  //                         .bodyMedium
                  //                         .override(
                  //                           fontFamily: 'Satoshi',
                  //                           color: ClassifiedAppTheme.of(context)
                  //                               .primaryText,
                  //                           fontSize: 18.0,
                  //                           fontWeight: FontWeight.bold,
                  //                           useGoogleFonts: false,
                  //                         ),
                  //                   ),
                  //                   Padding(
                  //                     padding: EdgeInsetsDirectional.fromSTEB(
                  //                         0.0, 16.0, 0.0, 0.0),
                  //                     child: Container(
                  //                       width: 109.0,
                  //                       height: 36.0,
                  //                       decoration: BoxDecoration(
                  //                         color: ClassifiedAppTheme.of(context)
                  //                             .primary,
                  //                         borderRadius: BorderRadius.circular(6.0),
                  //                       ),
                  //                       alignment: AlignmentDirectional(0.0, 0.0),
                  //                       child: Text(
                  //                         "${data.button_text}",
                  //                         style: ClassifiedAppTheme.of(context)
                  //                             .bodyMedium
                  //                             .override(
                  //                               fontFamily: 'Urbanist',
                  //                               color:
                  //                                   ClassifiedAppTheme.of(context)
                  //                                       .secondary,
                  //                               fontSize: 15.0,
                  //                               fontWeight: FontWeight.normal,
                  //                             ),
                  //                       ),
                  //                     ),
                  //                   ),
                  //                 ],
                  //               ),
                  //             ),
                  //           ],
                  //         );
                  //       }).toList(),
                  //       carouselController: _model.carouselController ??=
                  //           CarouselSliderController(),
                  //       options: CarouselOptions(
                  //         initialPage: 1,
                  //         viewportFraction: 0.9,
                  //         disableCenter: true,
                  //         enlargeCenterPage: true,
                  //         enlargeFactor: 0.16,
                  //         enableInfiniteScroll: true,
                  //         scrollDirection: Axis.horizontal,
                  //         autoPlay: true,
                  //         autoPlayAnimationDuration: Duration(milliseconds: 800),
                  //         autoPlayInterval: Duration(milliseconds: (800 + 4000)),
                  //         autoPlayCurve: Curves.linear,
                  //         pauseAutoPlayInFiniteScroll: true,
                  //         onPageChanged: (index, _) async {
                  //           _model.carouselCurrentIndex = index;
                  //           setState(() {
                  //             FFAppState().introIndex = _model.carouselCurrentIndex;
                  //           });
                  //         },
                  //       ),
                  //     ),
                  //   ).animateOnPageLoad(
                  //       animationsMap['carouselOnPageLoadAnimation']!),
                  // ),
                  // Padding(
                  //   padding: EdgeInsetsDirectional.fromSTEB(0.0, 16.0, 0.0, 0.0),
                  //   child: InkWell(
                  //     splashColor: Colors.transparent,
                  //     focusColor: Colors.transparent,
                  //     hoverColor: Colors.transparent,
                  //     highlightColor: Colors.transparent,
                  //     onTap: () async {
                  //       setState(() {
                  //         FFAppState().introIndex = _model.carouselCurrentIndex;
                  //       });
                  //     },
                  //     child: Row(
                  //         mainAxisSize: MainAxisSize.max,
                  //         mainAxisAlignment: MainAxisAlignment.center,
                  //         children: appStore.sliderList.mapIndexed((index, data) {
                  //           return Padding(
                  //             padding: EdgeInsetsDirectional.fromSTEB(
                  //                 8.0, 0.0, 0.0, 0.0),
                  //             child: Container(
                  //               width: 8.0,
                  //               height: 8.0,
                  //               decoration: BoxDecoration(
                  //                 color: _model.carouselCurrentIndex == index
                  //                     ? ClassifiedAppTheme.of(context).primary
                  //                     : Color(0x674A75F6),
                  //                 shape: BoxShape.circle,
                  //               ),
                  //             ),
                  //           );
                  //         })),
                  //   ),
                  // ),
                  if (responsiveVisibility(
                    context: context,
                    phone: false,
                    tablet: false,
                    tabletLandscape: false,
                    desktop: false,
                  ))
                    Opacity(
                      opacity: 0.0,
                      child: Padding(
                        padding: EdgeInsetsDirectional.fromSTEB(
                            16.0, 16.0, 16.0, 0.0),
                        child: Container(
                          height: 54.0,
                          decoration: BoxDecoration(),
                          child: TextFormField(
                            controller: _model.textController,
                            focusNode: _model.textFieldFocusNode,
                            textInputAction: TextInputAction.done,
                            obscureText: false,
                            decoration: InputDecoration(
                              labelStyle:
                                  ClassifiedAppTheme.of(context).labelMedium,
                              hintText: FFLocalizations.of(context).getText(
                                'rcrr8c67' /* Search here... */,
                              ),
                              hintStyle: ClassifiedAppTheme.of(context)
                                  .labelMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    fontSize: 17.0,
                                    fontWeight: FontWeight.w500,
                                    useGoogleFonts: false,
                                  ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).info,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).primary,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              errorBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).error,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              focusedErrorBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: ClassifiedAppTheme.of(context).error,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              prefixIcon: Icon(
                                FFIcons.ksearchnew,
                                color:
                                    ClassifiedAppTheme.of(context).primaryText,
                                size: 24.0,
                              ),
                            ),
                            style: ClassifiedAppTheme.of(context).bodyMedium,
                            cursorColor: ClassifiedAppTheme.of(context).primary,
                            validator: _model.textControllerValidator
                                .asValidator(context),
                          ),
                        ),
                      ),
                    ),
                  Padding(
                    padding:
                        EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 16.0, 0.0),
                    child: InkWell(
                      splashColor: Colors.transparent,
                      focusColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                      onTap: () async {
                        context.pushNamed(
                          'SearchPage',
                          extra: <String, dynamic>{
                            kTransitionInfoKey: TransitionInfo(
                              hasTransition: true,
                              transitionType: PageTransitionType.rightToLeft,
                              duration: Duration(milliseconds: 300),
                            ),
                          },
                        );
                      },
                      child: Container(
                        width: double.infinity,
                        height: 50.0,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12.0),
                          border: Border.all(
                            color: ClassifiedAppTheme.of(context).info,
                          ),
                        ),
                        child: Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              16.0, 0.0, 16.0, 0.0),
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              Icon(
                                FFIcons.ksearchnew,
                                color: Color(0xFF131313),
                                size: 24.0,
                              ),
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    12.0, 0.0, 0.0, 0.0),
                                child: Text(
                                  FFLocalizations.of(context).getText(
                                    '1fmasdwk' /* Search here... */,
                                  ),
                                  style: ClassifiedAppTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        color: ClassifiedAppTheme.of(context)
                                            .secondaryText,
                                        fontSize: 17.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  Padding(
                    padding:
                        EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 16.0, 0.0),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Expanded(
                          child: Text(
                            FFLocalizations.of(context).getText(
                              'q8j3d826' /* Categories */,
                            ),
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 20.0,
                                  fontWeight: FontWeight.bold,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                        InkWell(
                          splashColor: Colors.transparent,
                          focusColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          onTap: () async {
                            context.pushNamed(
                              'CategoriesPage',
                              queryParameters: {
                                'barter': serializeParam(
                                  false,
                                  ParamType.bool,
                                ),
                              }.withoutNulls,
                              extra: <String, dynamic>{
                                kTransitionInfoKey: TransitionInfo(
                                  hasTransition: true,
                                  transitionType:
                                      PageTransitionType.rightToLeft,
                                  duration: Duration(milliseconds: 300),
                                ),
                              },
                            );
                          },
                          child: Text(
                            FFLocalizations.of(context).getText(
                              'izwddy6n' /* View all */,
                            ),
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  color: ClassifiedAppTheme.of(context)
                                      .primaryText,
                                  fontSize: 15.0,
                                  fontWeight: FontWeight.w500,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding:
                        EdgeInsetsDirectional.fromSTEB(0.0, 16.0, 0.0, 16.0),
                    child: Container(
                      width: double.infinity,
                      height: 190.0,
                      child: GridView.builder(
                        scrollDirection: Axis.horizontal,
                        padding: EdgeInsets.symmetric(horizontal: 16.0),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          crossAxisSpacing: 1.0,
                          mainAxisSpacing: 16.0,
                          childAspectRatio: 0.75,
                        ),
                        itemCount: appStore.parentCategoryList.length,
                        itemBuilder: (context, index) {
                          final data = appStore.parentCategoryList[index];
                          return Padding(
                            padding:
                                const EdgeInsets.only(top: 4.0, bottom: 8.0),
                            child: GestureDetector(
                              onTap: () {
                                if ([9, 10].contains(data.id)) {
                                  context.pushNamed(
                                    'CategoryProductPage',
                                    queryParameters: {
                                      'title': serializeParam(
                                        data.category_name.toString(),
                                        ParamType.String,
                                      ),
                                      'index': serializeParam(
                                        data.id,
                                        ParamType.int,
                                      ),
                                      'isMaincategory': serializeParam(
                                        true,
                                        ParamType.bool,
                                      ),
                                      'barter': serializeParam(
                                        false,
                                        ParamType.bool,
                                      ),
                                    }.withoutNulls,
                                    extra: <String, dynamic>{
                                      kTransitionInfoKey: TransitionInfo(
                                        hasTransition: true,
                                        transitionType:
                                            PageTransitionType.rightToLeft,
                                        duration: Duration(milliseconds: 300),
                                      ),
                                    },
                                  );
                                } else {
                                  context.pushNamed(
                                    'SubCategoriesPage',
                                    queryParameters: {
                                      'title': serializeParam(
                                        data.category_name.toString(),
                                        ParamType.String,
                                      ),
                                      'image': serializeParam(
                                        data.image.toString(),
                                        ParamType.String,
                                      ),
                                      'index': serializeParam(
                                        data.id,
                                        ParamType.int,
                                      ),
                                      'barter': serializeParam(
                                        false,
                                        ParamType.bool,
                                      ),
                                    }.withoutNulls,
                                    extra: <String, dynamic>{
                                      kTransitionInfoKey: TransitionInfo(
                                        hasTransition: true,
                                        transitionType:
                                            PageTransitionType.rightToLeft,
                                        duration: Duration(milliseconds: 300),
                                      ),
                                    },
                                  );
                                }
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(12.0),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.1),
                                      blurRadius: 6.0,
                                      spreadRadius: 2.0,
                                      offset: Offset(0, 4),
                                    ),
                                  ],
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    ClipRRect(
                                      borderRadius: BorderRadius.vertical(
                                        top: Radius.circular(12.0),
                                      ),
                                      child: data.image?.endsWith('.svg') ??
                                              false
                                          ? SvgPicture.network(
                                              "${ApiUtils.admin_files}${data.image}",
                                              fit: BoxFit.cover,
                                              height: 40,
                                              color:
                                                  ClassifiedAppTheme.of(context)
                                                      .primary,
                                              width: double.infinity,
                                              placeholderBuilder:
                                                  (BuildContext context) =>
                                                      Container(
                                                height: 40,
                                                color: Colors.grey.shade200,
                                                child: Center(
                                                  child:
                                                      CircularProgressIndicator(),
                                                ),
                                              ),
                                            )
                                          : CachedNetworkImage(
                                              imageUrl:
                                                  "${ApiUtils.admin_files}${data.image}",
                                              fit: BoxFit.cover,
                                              height: 40,
                                              width: double.infinity,
                                              errorWidget:
                                                  (context, url, error) =>
                                                      Container(
                                                height: 40,
                                                color: Colors.grey.shade200,
                                                child: Icon(Icons.broken_image,
                                                    size: 40,
                                                    color: Colors.grey),
                                              ),
                                            ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Text(
                                        "${data.category_name}",
                                        textAlign: TextAlign.center,
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          fontSize: 12.0,
                                          fontWeight: FontWeight.bold,
                                          color: ClassifiedAppTheme.of(context)
                                              .primary,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),

                  Padding(
                    padding:
                        EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 16.0, 0.0),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Expanded(
                          child: Text(
                            FFLocalizations.of(context).getText(
                              'pq7n9e7b' /* Recent uploaded items */,
                            ),
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 20.0,
                                  fontWeight: FontWeight.bold,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                        // Filter button
                        InkWell(
                          splashColor: Colors.transparent,
                          focusColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          onTap: () async {
                            appStore.filterIndex(0); // Home tab filter index
                            final result = await context.pushNamed(
                              'FilterPage',
                              extra: <String, dynamic>{
                                kTransitionInfoKey: TransitionInfo(
                                  hasTransition: true,
                                  transitionType:
                                      PageTransitionType.rightToLeft,
                                  duration: Duration(milliseconds: 300),
                                ),
                              },
                            );
                            if (result != null) {
                              final Map<String, dynamic> resultMap =
                                  result as Map<String, dynamic>;
                              // Apply filters globally
                              appStore.applyFilters(
                                sortBy: resultMap['sort'],
                                priceRange: resultMap['priceRange'],
                                condition: resultMap['conditions'],
                                city: resultMap['city'],
                                area: resultMap['area'],
                              );
                              setState(() {}); // Refresh UI
                            }
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 8.0, vertical: 4.0),
                            decoration: BoxDecoration(
                              color: ClassifiedAppTheme.of(context).primary,
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.filter_list,
                                  color: Colors.white,
                                  size: 16.0,
                                ),
                                SizedBox(width: 4.0),
                                Text(
                                  'Filter',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 14.0,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(width: 8.0),
                        InkWell(
                          splashColor: Colors.transparent,
                          focusColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          onTap: () async {
                            context.pushNamed(
                              'RecentUploadPage',
                              extra: <String, dynamic>{
                                kTransitionInfoKey: TransitionInfo(
                                  hasTransition: true,
                                  transitionType:
                                      PageTransitionType.rightToLeft,
                                  duration: Duration(milliseconds: 300),
                                ),
                              },
                            );
                          },
                          child: Text(
                            FFLocalizations.of(context).getText(
                              'nyhhxvu2' /* View all */,
                            ),
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  color: ClassifiedAppTheme.of(context)
                                      .primaryText,
                                  fontSize: 15.0,
                                  fontWeight: FontWeight.w500,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding:
                        EdgeInsetsDirectional.fromSTEB(0.0, 16.0, 0.0, 16.0),
                    child: Container(
                      width: double.infinity,
                      height: 260.0,
                      decoration: BoxDecoration(),
                      child: Padding(
                        padding: EdgeInsetsDirectional.fromSTEB(
                            16.0, 0.0, 16.0, 0.0),
                        child: GridView(
                                padding: EdgeInsets.zero,
                                gridDelegate:
                                    SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 1,
                                  crossAxisSpacing: 16.0,
                                  mainAxisSpacing: 0.5,
                                  childAspectRatio: 1.30,
                                ),
                                shrinkWrap: true,
                                scrollDirection: Axis.horizontal,
                                children: appStore.latestPost
                                    .take(5)
                                    .map<Widget>((data) {
                                  return postBoxWidget(data);
                                }).toList())
                            .animateOnPageLoad(
                                animationsMap['gridViewOnPageLoadAnimation']!),
                      ),
                    ),
                  ),
                  Padding(
                    padding:
                        EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 16.0, 0.0),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Expanded(
                          child: Text(
                            FFLocalizations.of(context).getText(
                              'ar56d135' /* Electronic  */,
                            ),
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 20.0,
                                  fontWeight: FontWeight.bold,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                        InkWell(
                          splashColor: Colors.transparent,
                          focusColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          onTap: () async {
                            context.pushNamed(
                              'CategoryProductPage',
                              queryParameters: {
                                'title': serializeParam(
                                  "Electronics",
                                  ParamType.String,
                                ),
                                'index': serializeParam(
                                  1,
                                  ParamType.int,
                                ),
                                'isMaincategory': serializeParam(
                                  true,
                                  ParamType.bool,
                                ),
                                'barter': serializeParam(
                                  false,
                                  ParamType.bool,
                                ),
                              }.withoutNulls,
                              extra: <String, dynamic>{
                                kTransitionInfoKey: TransitionInfo(
                                  hasTransition: true,
                                  transitionType:
                                      PageTransitionType.rightToLeft,
                                  duration: Duration(milliseconds: 300),
                                ),
                              },
                            );
                          },
                          child: Text(
                            FFLocalizations.of(context).getText(
                              'nyhhxvu2' /* View all */,
                            ),
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  color: ClassifiedAppTheme.of(context)
                                      .primaryText,
                                  fontSize: 15.0,
                                  fontWeight: FontWeight.w500,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding:
                        EdgeInsetsDirectional.fromSTEB(0.0, 16.0, 0.0, 16.0),
                    child: Container(
                      width: double.infinity,
                      height: 260.0,
                      decoration: BoxDecoration(),
                      child: Padding(
                        padding: EdgeInsetsDirectional.fromSTEB(
                            16.0, 0.0, 16.0, 0.0),
                        child: GridView(
                                padding: EdgeInsets.zero,
                                gridDelegate:
                                    SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 1,
                                  crossAxisSpacing: 16.0,
                                  mainAxisSpacing: 0.5,
                                  childAspectRatio: 1.30,
                                ),
                                shrinkWrap: true,
                                scrollDirection: Axis.horizontal,
                                children: appStore.electronics
                                    .take(5)
                                    .map<Widget>((data) {
                                  return postBoxWidget(data);
                                }).toList())
                            .animateOnPageLoad(
                                animationsMap['gridViewOnPageLoadAnimation']!),
                      ),
                    ),
                  ),
                  Padding(
                    padding:
                        EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 16.0, 0.0),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Expanded(
                          child: Text(
                            FFLocalizations.of(context).getText(
                              '836573j9' /* View all */,
                            ),
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 20.0,
                                  fontWeight: FontWeight.bold,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                        InkWell(
                          splashColor: Colors.transparent,
                          focusColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          onTap: () async {
                            context.pushNamed(
                              'CategoryProductPage',
                              queryParameters: {
                                'title': serializeParam(
                                  "Vehicles",
                                  ParamType.String,
                                ),
                                'index': serializeParam(
                                  6,
                                  ParamType.int,
                                ),
                                'isMaincategory': serializeParam(
                                  true,
                                  ParamType.bool,
                                ),
                                'barter': serializeParam(
                                  false,
                                  ParamType.bool,
                                ),
                              }.withoutNulls,
                              extra: <String, dynamic>{
                                kTransitionInfoKey: TransitionInfo(
                                  hasTransition: true,
                                  transitionType:
                                      PageTransitionType.rightToLeft,
                                  duration: Duration(milliseconds: 300),
                                ),
                              },
                            );
                          },
                          child: Text(
                            FFLocalizations.of(context).getText(
                              'nyhhxvu2' /* View all */,
                            ),
                            style: ClassifiedAppTheme.of(context)
                                .bodyMedium
                                .override(
                                  fontFamily: 'Satoshi',
                                  color: ClassifiedAppTheme.of(context)
                                      .primaryText,
                                  fontSize: 15.0,
                                  fontWeight: FontWeight.w500,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding:
                        EdgeInsetsDirectional.fromSTEB(0.0, 16.0, 0.0, 16.0),
                    child: Container(
                      width: double.infinity,
                      height: 260.0,
                      decoration: BoxDecoration(),
                      child: Padding(
                        padding: EdgeInsetsDirectional.fromSTEB(
                            16.0, 0.0, 16.0, 0.0),
                        child: GridView(
                                padding: EdgeInsets.zero,
                                gridDelegate:
                                    SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 1,
                                  crossAxisSpacing: 16.0,
                                  mainAxisSpacing: 0.5,
                                  childAspectRatio: 1.30,
                                ),
                                shrinkWrap: true,
                                scrollDirection: Axis.horizontal,
                                children: appStore.vehicles
                                    .take(5)
                                    .map<Widget>((data) {
                                  return postBoxWidget(data);
                                }).toList())
                            .animateOnPageLoad(
                                animationsMap['gridViewOnPageLoadAnimation']!),
                      ),
                    ),
                  ),
                  if (appStore.featurePost.isNotEmpty)
                    Padding(
                      padding: EdgeInsetsDirectional.fromSTEB(
                          16.0, 16.0, 16.0, 16.0),
                      child: Row(
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Expanded(
                            child: Text(
                              FFLocalizations.of(context).getText(
                                'ydm7qubc' /* Featured products */,
                              ),
                              style: ClassifiedAppTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    fontSize: 20.0,
                                    fontWeight: FontWeight.bold,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ),
                          InkWell(
                            splashColor: Colors.transparent,
                            focusColor: Colors.transparent,
                            hoverColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            onTap: () async {
                              context.pushNamed(
                                'FeaturedProductPage',
                                extra: <String, dynamic>{
                                  kTransitionInfoKey: TransitionInfo(
                                    hasTransition: true,
                                    transitionType:
                                        PageTransitionType.rightToLeft,
                                    duration: Duration(milliseconds: 300),
                                  ),
                                },
                              );
                            },
                            child: Text(
                              FFLocalizations.of(context).getText(
                                'l51ielu0' /* View all */,
                              ),
                              style: ClassifiedAppTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    color: ClassifiedAppTheme.of(context)
                                        .primaryText,
                                    fontSize: 15.0,
                                    fontWeight: FontWeight.w500,
                                    useGoogleFonts: false,
                                  ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  Padding(
                    padding:
                        EdgeInsetsDirectional.fromSTEB(0.0, 16.0, 0.0, 16.0),
                    child: Container(
                      width: double.infinity,
                      // height: 260.0,
                      decoration: BoxDecoration(),
                      child: Padding(
                        padding: EdgeInsetsDirectional.fromSTEB(
                            16.0, 0.0, 16.0, 0.0),
                        child: GridView(
                                padding: EdgeInsets.zero,
                                gridDelegate:
                                    SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 2,
                                  crossAxisSpacing: 16.0,
                                  mainAxisSpacing: 15.5,
                                  childAspectRatio: 0.65,
                                ),
                                shrinkWrap: true,
                                scrollDirection: Axis.vertical,
                                physics: ScrollPhysics(),
                                children:
                                    appStore.featurePost.take(10).map((data) {
                                  return GestureDetector(
                                    onTap: () {
                                      context.pushNamed(
                                        'ProductDetailPage',
                                        queryParameters: {
                                          'post': jsonEncode(data
                                              .toJson()), // Serialize PostModel
                                        }.withoutNulls,
                                        extra: <String, dynamic>{
                                          kTransitionInfoKey: TransitionInfo(
                                            hasTransition: true,
                                            transitionType:
                                                PageTransitionType.rightToLeft,
                                            duration:
                                                Duration(milliseconds: 300),
                                          ),
                                        },
                                      );
                                    },
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: ClassifiedAppTheme.of(context)
                                            .secondaryBackground,
                                        boxShadow: [
                                          BoxShadow(
                                            blurRadius: 4.0,
                                            color: Color(0x27000000),
                                            offset: Offset(0.0, 4.0),
                                          )
                                        ],
                                        borderRadius:
                                            BorderRadius.circular(12.0),
                                      ),
                                      child: Padding(
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            8.0, 8.0, 8.0, 8.0),
                                        child: Column(
                                          mainAxisSize: MainAxisSize.max,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Stack(
                                              alignment: AlignmentDirectional(
                                                  1.0, -1.0),
                                              children: [
                                                ClipRRect(
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          8.0),
                                                  child: CachedNetworkImage(
                                                    width: double.infinity,
                                                    height: 115.0,
                                                    fit: BoxFit.cover,
                                                    imageUrl:
                                                        "${ApiUtils.post_image}${data.image}",
                                                    placeholder:
                                                        (context, url) =>
                                                            Padding(
                                                      padding:
                                                          const EdgeInsets.all(
                                                              35.0),
                                                      child: Center(
                                                          child:
                                                              CircularProgressIndicator()),
                                                    ),
                                                    errorWidget:
                                                        (context, url, error) =>
                                                            Icon(Icons.error),
                                                  ),
                                                ),
                                                Align(
                                                  alignment:
                                                      AlignmentDirectional(
                                                          1.0, -1.0),
                                                  child: Padding(
                                                    padding:
                                                        EdgeInsetsDirectional
                                                            .fromSTEB(0.0, 8.0,
                                                                8.0, 0.0),
                                                    child: Container(
                                                      width: 24.0,
                                                      height: 24.0,
                                                      decoration: BoxDecoration(
                                                        color: ClassifiedAppTheme
                                                                .of(context)
                                                            .primaryBackground,
                                                        shape: BoxShape.circle,
                                                      ),
                                                      alignment:
                                                          AlignmentDirectional(
                                                              0.0, 0.0),
                                                      child: SvgPicture.asset(
                                                        data.fav
                                                            ? 'assets/images/heart_filled.svg'
                                                            : 'assets/images/heart.svg',
                                                        width: 13.0,
                                                        height: 13.0,
                                                        fit: BoxFit.contain,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                            Padding(
                                              padding: EdgeInsetsDirectional
                                                  .fromSTEB(0.0, 8.0, 0.0, 0.0),
                                              child: Text(
                                                "${data.post_name}",
                                                maxLines: 1,
                                                style: ClassifiedAppTheme.of(
                                                        context)
                                                    .bodyMedium
                                                    .override(
                                                      fontFamily: 'Satoshi',
                                                      fontSize: 15.0,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      useGoogleFonts: false,
                                                    ),
                                              ),
                                            ),
                                            Padding(
                                              padding: EdgeInsetsDirectional
                                                  .fromSTEB(0.0, 8.0, 0.0, 0.0),
                                              child: Text(
                                                "AED ${data.price}",
                                                style: ClassifiedAppTheme.of(
                                                        context)
                                                    .bodyMedium
                                                    .override(
                                                      fontFamily: 'Satoshi',
                                                      fontSize: 12.0,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      useGoogleFonts: false,
                                                    ),
                                              ),
                                            ),
                                            Padding(
                                              padding: EdgeInsetsDirectional
                                                  .fromSTEB(0.0, 8.0, 0.0, 0.0),
                                              child: Row(
                                                mainAxisSize: MainAxisSize.max,
                                                children: [
                                                  data.vendor_image == ''
                                                      ? ClipRRect(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(
                                                                      30.0),
                                                          child: Image.asset(
                                                            'assets/images/soho_icon.jpg',
                                                            width: 32.0,
                                                            height: 32.0,
                                                            fit: BoxFit.contain,
                                                          ),
                                                        )
                                                      : ClipRRect(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(
                                                                      30.0),
                                                          child:
                                                              CachedNetworkImage(
                                                            width: 32.0,
                                                            height: 32.0,
                                                            fit: BoxFit.cover,
                                                            imageUrl:
                                                                "${ApiUtils.profile_files}${data.vendor_image}",
                                                            placeholder:
                                                                (context,
                                                                        url) =>
                                                                    Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .all(
                                                                      35.0),
                                                              child: Center(
                                                                  child:
                                                                      CircularProgressIndicator()),
                                                            ),
                                                            errorWidget:
                                                                (context, url,
                                                                        error) =>
                                                                    Icon(Icons
                                                                        .error),
                                                          ),
                                                        ),
                                                  Padding(
                                                    padding:
                                                        EdgeInsetsDirectional
                                                            .fromSTEB(8.0, 0.0,
                                                                0.0, 0.0),
                                                    child: Column(
                                                      mainAxisSize:
                                                          MainAxisSize.max,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Container(
                                                          width: MediaQuery.of(
                                                                          context)
                                                                      .size
                                                                      .width >
                                                                  768
                                                              ? MediaQuery.of(
                                                                          context)
                                                                      .size
                                                                      .width *
                                                                  0.12 // For iPads (or larger screens)
                                                              : MediaQuery.of(
                                                                          context)
                                                                      .size
                                                                      .width *
                                                                  0.35, // Default (phones)

                                                          child: Text(
                                                            data.vendor_name
                                                                .toString(),
                                                            overflow:
                                                                TextOverflow
                                                                    .fade,
                                                            maxLines: 1,
                                                            softWrap: false,
                                                            style: ClassifiedAppTheme
                                                                    .of(context)
                                                                .bodyMedium
                                                                .override(
                                                                  fontFamily:
                                                                      'Satoshi',
                                                                  fontSize:
                                                                      12.0,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                  useGoogleFonts:
                                                                      false,
                                                                ),
                                                          ),
                                                        ),
                                                        Padding(
                                                          padding:
                                                              EdgeInsetsDirectional
                                                                  .fromSTEB(
                                                                      0.0,
                                                                      4.0,
                                                                      0.0,
                                                                      0.0),
                                                          child: Row(
                                                            mainAxisSize:
                                                                MainAxisSize
                                                                    .max,
                                                            children: [
                                                              SvgPicture.asset(
                                                                'assets/images/location-home.svg',
                                                                width: 14.0,
                                                                height: 14.0,
                                                                fit: BoxFit
                                                                    .cover,
                                                              ),
                                                              Padding(
                                                                padding:
                                                                    EdgeInsetsDirectional
                                                                        .fromSTEB(
                                                                            4.0,
                                                                            0.0,
                                                                            0.0,
                                                                            0.0),
                                                                child: Text(
                                                                  "${data.city}",
                                                                  style: ClassifiedAppTheme.of(
                                                                          context)
                                                                      .bodyMedium
                                                                      .override(
                                                                        fontFamily:
                                                                            'Satoshi',
                                                                        fontSize:
                                                                            12.0,
                                                                        fontWeight:
                                                                            FontWeight.w500,
                                                                        useGoogleFonts:
                                                                            false,
                                                                      ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                }).toList())
                            .animateOnPageLoad(
                                animationsMap['gridViewOnPageLoadAnimation']!),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget postBoxWidget(PostModel data) {
    return GestureDetector(
      onTap: () {
        print("${data.toJson()}");
        context.pushNamed(
          'ProductDetailPage',
          queryParameters: {
            'post': jsonEncode(data.toJson()), // Serialize PostModel
          }.withoutNulls,
          extra: <String, dynamic>{
            kTransitionInfoKey: TransitionInfo(
              hasTransition: true,
              transitionType: PageTransitionType.rightToLeft,
              duration: Duration(milliseconds: 300),
            ),
          },
        );
      },
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Container(
          decoration: BoxDecoration(
            color: ClassifiedAppTheme.of(context).secondaryBackground,
            boxShadow: [
              BoxShadow(
                blurRadius: 4.0,
                color: Color(0x27000000),
                offset: Offset(0.0, 4.0),
              )
            ],
            borderRadius: BorderRadius.circular(12.0),
          ),
          child: Padding(
            padding: EdgeInsetsDirectional.fromSTEB(8.0, 8.0, 8.0, 8.0),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Stack(
                  alignment: AlignmentDirectional(1.0, -1.0),
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8.0),
                      child: CachedNetworkImage(
                        width: double.infinity,
                        height: 115.0,
                        fit: BoxFit.cover,
                        imageUrl: "${ApiUtils.post_image}${data.image}",
                        placeholder: (context, url) => Padding(
                          padding: const EdgeInsets.all(35.0),
                          child: Center(child: CircularProgressIndicator()),
                        ),
                        errorWidget: (context, url, error) => Icon(Icons.error),
                      ),
                    ),
                    Align(
                      alignment: AlignmentDirectional(1.0, -1.0),
                      child: Padding(
                        padding:
                            EdgeInsetsDirectional.fromSTEB(0.0, 8.0, 8.0, 0.0),
                        child: Container(
                          width: 24.0,
                          height: 24.0,
                          decoration: BoxDecoration(
                            color: ClassifiedAppTheme.of(context)
                                .primaryBackground,
                            shape: BoxShape.circle,
                          ),
                          alignment: AlignmentDirectional(0.0, 0.0),
                          child: SvgPicture.asset(
                            data.fav
                                ? 'assets/images/heart_filled.svg'
                                : 'assets/images/heart.svg',
                            width: 13.0,
                            height: 13.0,
                            fit: BoxFit.contain,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(0.0, 8.0, 0.0, 0.0),
                  child: Text(
                    "${data.post_name}",
                    maxLines: 1,
                    style: ClassifiedAppTheme.of(context).bodyMedium.override(
                          fontFamily: 'Satoshi',
                          fontSize: 15.0,
                          fontWeight: FontWeight.bold,
                          useGoogleFonts: false,
                        ),
                  ),
                ),
                Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(0.0, 8.0, 0.0, 0.0),
                  child: Text(
                    "AED ${data.price}",
                    style: ClassifiedAppTheme.of(context).bodyMedium.override(
                          fontFamily: 'Satoshi',
                          fontSize: 12.0,
                          fontWeight: FontWeight.bold,
                          useGoogleFonts: false,
                        ),
                  ),
                ),
                Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(0.0, 8.0, 0.0, 0.0),
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      data.vendor_image == ''
                          ? ClipRRect(
                              borderRadius: BorderRadius.circular(30.0),
                              child: Image.asset(
                                'assets/images/soho_icon.jpg',
                                width: 32.0,
                                height: 32.0,
                                fit: BoxFit.contain,
                              ),
                            )
                          : ClipRRect(
                              borderRadius: BorderRadius.circular(30.0),
                              child: CachedNetworkImage(
                                width: 32.0,
                                height: 32.0,
                                fit: BoxFit.cover,
                                imageUrl:
                                    "${ApiUtils.profile_files}${data.vendor_image}",
                                placeholder: (context, url) => Padding(
                                  padding: const EdgeInsets.all(35.0),
                                  child: Center(
                                      child: CircularProgressIndicator()),
                                ),
                                errorWidget: (context, url, error) =>
                                    Icon(Icons.error),
                              ),
                            ),
                      Padding(
                        padding:
                            EdgeInsetsDirectional.fromSTEB(8.0, 0.0, 0.0, 0.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              width: MediaQuery.of(context).size.width > 768
                                  ? MediaQuery.of(context).size.width *
                                      0.12 // For iPads (or larger screens)
                                  : MediaQuery.of(context).size.width *
                                      0.35, // Default (phones)

                              child: Text(
                                data.vendor_name.toString(),
                                overflow: TextOverflow.fade,
                                maxLines: 1,
                                softWrap: false,
                                style: ClassifiedAppTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'Satoshi',
                                      fontSize: 12.0,
                                      fontWeight: FontWeight.bold,
                                      useGoogleFonts: false,
                                    ),
                              ),
                            ),
                            Padding(
                              padding: EdgeInsetsDirectional.fromSTEB(
                                  0.0, 4.0, 0.0, 0.0),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  SvgPicture.asset(
                                    'assets/images/location-home.svg',
                                    width: 14.0,
                                    height: 14.0,
                                    fit: BoxFit.cover,
                                  ),
                                  Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        4.0, 0.0, 0.0, 0.0),
                                    child: Text(
                                      "${data.city}",
                                      style: ClassifiedAppTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'Satoshi',
                                            fontSize: 12.0,
                                            fontWeight: FontWeight.w500,
                                            useGoogleFonts: false,
                                          ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
