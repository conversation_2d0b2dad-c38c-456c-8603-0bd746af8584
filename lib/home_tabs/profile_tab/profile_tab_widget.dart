import 'package:cached_network_image/cached_network_image.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:soho_souk/ApiUtils.dart';
import 'package:soho_souk/Classified_App/classified_app_language_selector.dart';
import 'package:soho_souk/main.dart';
import 'package:soho_souk/pages/app_bar/app_bar_widget.dart';
import 'package:soho_souk/pages/profile_page/profile_page_model.dart';

import '../../Classified_App/classified_app_animations.dart';
import '../../Classified_App/classified_app_theme.dart';
import '../../Classified_App/classified_app_util.dart';
import '/pages/logout_dialoge/logout_dialoge_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'profile_tab_model.dart';
export 'profile_tab_model.dart';

class ProfileTabWidget extends StatefulWidget {
  const ProfileTabWidget({super.key});

  @override
  State<ProfileTabWidget> createState() => _ProfileTabWidgetState();
}

class _ProfileTabWidgetState extends State<ProfileTabWidget>
    with TickerProviderStateMixin {
  late ProfileTabModel _model;
late ProfilePageModel _models;
final scaffoldKey = GlobalKey<ScaffoldState>();
  final animationsMap = {
    'columnOnPageLoadAnimation': AnimationInfo(
      trigger: AnimationTrigger.onPageLoad,
      effects: [
        FadeEffect(
          curve: Curves.easeInOut,
          delay: 50.ms,
          duration: 300.ms,
          begin: 0.15,
          end: 1.0,
        ),
      ],
    ),
  };

  

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => ProfileTabModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return  GestureDetector(
      onTap: () => _models.unfocusNode.canRequestFocus
          ? FocusScope.of(context).requestFocus(_models.unfocusNode)
          : FocusScope.of(context).unfocus(),
      child: Scaffold(
        key: scaffoldKey,
        backgroundColor: ClassifiedAppTheme.of(context).primaryBackground,
        body: SafeArea(
          top: true,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
               wrapWithModel(
                model: _model.appBarModel,
                updateCallback: () => setState(() {}),
                child: AppBarWidget(
                  title: FFLocalizations.of(context).getText(
                        'a0trpbbk' /* Profile */,
                      ),
                ),
              ),
             
              Expanded(
                child: Padding(
                  padding: EdgeInsetsDirectional.fromSTEB(10.0, 0.0, 10.0, 0.0),
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                       appStore.profile_image==null? Image.asset(
                          'assets/images/ProfileEmpty.png',
                          width: 100.0,
                          height: 100.0,
                          fit: BoxFit.cover,
                        ):ClipRRect(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              50.0),
                                                      child: CachedNetworkImage(
                                                     width: 100.0,
                                height: 100.0,
                                                        fit: BoxFit.cover,
                                                        imageUrl:
                                                            "${ApiUtils.profile_files}${appStore.profile_image}",
                                                        placeholder:
                                                            (context, url) =>
                                                                Padding(
                                                          padding:
                                                              const EdgeInsets
                                                                  .all(35.0),
                                                          child: Center(
                                                              child:
                                                                  CircularProgressIndicator()),
                                                        ),
                                                        errorWidget: (context,
                                                                url, error) =>
                                                            Icon(Icons.error),
                                                      ),
                                                    ),
                        Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(0.0, 8.0, 0.0, 0.0),
                          child: Text(
                            appStore.logined ? appStore.user_name.toString() : "Guest User",
                            style: ClassifiedAppTheme.of(context).bodyMedium.override(
                                  fontFamily: 'Satoshi',
                                  fontSize: 18.0,
                                  fontWeight: FontWeight.bold,
                                  useGoogleFonts: false,
                                ),
                          ),
                        ),
                        if(appStore.logined)
                        Text(
                          appStore.user_email.toString(),
                          style: ClassifiedAppTheme.of(context).bodyMedium.override(
                                fontFamily: 'Satoshi',
                                color: ClassifiedAppTheme.of(context).secondaryText,
                                fontSize: 17.0,
                                fontWeight: FontWeight.w500,
                                useGoogleFonts: false,
                              ),
                        ),
                        if (responsiveVisibility(
                          context: context,
                          phone: false,
                          tablet: false,
                          tabletLandscape: false,
                          desktop: false,
                        ))
                          Padding(
                            padding:
                                EdgeInsetsDirectional.fromSTEB(0.0, 16.0, 0.0, 0.0),
                            child: InkWell(
                              splashColor: Colors.transparent,
                              focusColor: Colors.transparent,
                              hoverColor: Colors.transparent,
                              highlightColor: Colors.transparent,
                              onTap: () async {
                                context.pushNamed(
                                  'SubscriptionPage',
                                  extra: <String, dynamic>{
                                    kTransitionInfoKey: TransitionInfo(
                                      hasTransition: true,
                                      transitionType: PageTransitionType.rightToLeft,
                                      duration: Duration(milliseconds: 300),
                                    ),
                                  },
                                );
                              },
                              child: Stack(
                                alignment: AlignmentDirectional(1.0, 0.0),
                                children: [
                                  Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        6.0, 0.0, 6.0, 0.0),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(8.0),
                                      child: Image.asset(
                                        'assets/images/ProfileBanner.png',
                                        width: double.infinity,
                                        height: 112.0,
                                        fit: BoxFit.contain,
                                      ),
                                    ),
                                  ),
                                  Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        0.0, 0.0, 21.0, 0.0),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.max,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          FFLocalizations.of(context).getText(
                                            'g1vodezb' /* Upgrade to pro */,
                                          ),
                                          style: ClassifiedAppTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'Satoshi',
                                                color: Colors.white,
                                                fontSize: 18.0,
                                                fontWeight: FontWeight.bold,
                                                useGoogleFonts: false,
                                              ),
                                        ),
                                        Padding(
                                          padding: EdgeInsetsDirectional.fromSTEB(
                                              0.0, 5.0, 0.0, 0.0),
                                          child: Text(
                                            FFLocalizations.of(context).getText(
                                              'rfvt5ob9' /* Enjoy all features & benefits ... */,
                                            ),
                                            style: ClassifiedAppTheme.of(context)
                                                .bodyMedium
                                                .override(
                                                  fontFamily: 'Satoshi',
                                                  color: Colors.white,
                                                  fontSize: 13.0,
                                                  useGoogleFonts: false,
                                                ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        if(appStore.logined)
                        Padding(
                          padding:
                              EdgeInsetsDirectional.fromSTEB(6.0, 16.0, 6.0, 0.0),
                          child: InkWell(
                            splashColor: Colors.transparent,
                            focusColor: Colors.transparent,
                            hoverColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            onTap: () async {
                               context.pushNamed(
                                  'EditProfilePage',
                                  extra: <String, dynamic>{
                                    kTransitionInfoKey: TransitionInfo(
                                      hasTransition: true,
                                      transitionType: PageTransitionType.rightToLeft,
                                      duration: Duration(milliseconds: 300),
                                    ),
                                  },
                                );
                              // context.pushNamed(
                              //   'MyProfilePage',
                              //   extra: <String, dynamic>{
                              //     kTransitionInfoKey: TransitionInfo(
                              //       hasTransition: true,
                              //       transitionType: PageTransitionType.rightToLeft,
                              //       duration: Duration(milliseconds: 300),
                              //     ),
                              //   },
                              // );
                            },
                            child: Container(
                              width: double.infinity,
                              height: 64.0,
                              decoration: BoxDecoration(
                                color:
                                    ClassifiedAppTheme.of(context).secondaryBackground,
                                boxShadow: [
                                  BoxShadow(
                                    blurRadius: 4.0,
                                    color: Color(0x33000000),
                                    offset: Offset(0.0, 2.0),
                                  )
                                ],
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              child: Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    16.0, 0.0, 16.0, 0.0),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    Container(
                                      width: 48.0,
                                      height: 48.0,
                                      decoration: BoxDecoration(
                                        color: ClassifiedAppTheme.of(context).tertiary,
                                        shape: BoxShape.circle,
                                      ),
                                      alignment: AlignmentDirectional(0.0, 0.0),
                                      child: SvgPicture.asset(
                                        'assets/images/profile.svg',
                                        width: 24.0,
                                        height: 24.0,
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                    Expanded(
                                      child: Padding(
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            8.0, 0.0, 0.0, 0.0),
                                        child: Text(
                                          FFLocalizations.of(context).getText(
                                            'iunb8nxz' /* My profile */,
                                          ),
                                          style: ClassifiedAppTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'Satoshi',
                                                fontSize: 17.0,
                                                fontWeight: FontWeight.w500,
                                                useGoogleFonts: false,
                                              ),
                                        ),
                                      ),
                                    ),
                                    SvgPicture.asset(
                                      'assets/images/arrow-right.svg',
                                      width: 24.0,
                                      height: 24.0,
                                      fit: BoxFit.cover,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      if(appStore.logined)
                        Padding(
                          padding:
                              EdgeInsetsDirectional.fromSTEB(6.0, 16.0, 6.0, 0.0),
                          child: InkWell(
                            splashColor: Colors.transparent,
                            focusColor: Colors.transparent,
                            hoverColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            onTap: () async {
                              context.pushNamed(
                                'MyProductsPage',
                                extra: <String, dynamic>{
                                  kTransitionInfoKey: TransitionInfo(
                                    hasTransition: true,
                                    transitionType: PageTransitionType.rightToLeft,
                                    duration: Duration(milliseconds: 300),
                                  ),
                                },
                              );
                            },
                            child: Container(
                              width: double.infinity,
                              height: 64.0,
                              decoration: BoxDecoration(
                                color:
                                    ClassifiedAppTheme.of(context).secondaryBackground,
                                boxShadow: [
                                  BoxShadow(
                                    blurRadius: 4.0,
                                    color: Color(0x33000000),
                                    offset: Offset(0.0, 2.0),
                                  )
                                ],
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              child: Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    16.0, 0.0, 16.0, 0.0),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    Container(
                                      width: 48.0,
                                      height: 48.0,
                                      decoration: BoxDecoration(
                                        color: ClassifiedAppTheme.of(context).tertiary,
                                        shape: BoxShape.circle,
                                      ),
                                      alignment: AlignmentDirectional(0.0, 0.0),
                                      child: SvgPicture.asset(
                                        'assets/images/products.svg',
                                        width: 24.0,
                                        height: 24.0,
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                    Expanded(
                                      child: Padding(
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            8.0, 0.0, 0.0, 0.0),
                                        child: Text(
                                          FFLocalizations.of(context).getText(
                                            's0cru49t' /* My products */,
                                          ),
                                          style: ClassifiedAppTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'Satoshi',
                                                fontSize: 17.0,
                                                fontWeight: FontWeight.w500,
                                                useGoogleFonts: false,
                                              ),
                                        ),
                                      ),
                                    ),
                                    SvgPicture.asset(
                                      'assets/images/arrow-right.svg',
                                      width: 24.0,
                                      height: 24.0,
                                      fit: BoxFit.cover,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      if(appStore.logined)
                        Padding(
                          padding:
                              EdgeInsetsDirectional.fromSTEB(6.0, 16.0, 6.0, 0.0),
                          child: InkWell(
                            splashColor: Colors.transparent,
                            focusColor: Colors.transparent,
                            hoverColor: Colors.transparent,
                            highlightColor: Colors.transparent,
                            onTap: () async {
                              Navigator.of(context, rootNavigator: true).pop(false);
                                setState(() {
                                FFAppState().selectedPageIndex = 2;
                              });
                               await appStore.pageViewController?.animateToPage(
                                FFAppState().selectedPageIndex,
                                duration: Duration(milliseconds: 500),
                                curve: Curves.ease,
                              );
                               
                            },
                            child: Container(
                              width: double.infinity,
                              height: 64.0,
                              decoration: BoxDecoration(
                                color:
                                    ClassifiedAppTheme.of(context).secondaryBackground,
                                boxShadow: [
                                  BoxShadow(
                                    blurRadius: 4.0,
                                    color: Color(0x33000000),
                                    offset: Offset(0.0, 2.0),
                                  )
                                ],
                                borderRadius: BorderRadius.circular(12.0),
                              ),
                              child: Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    16.0, 0.0, 16.0, 0.0),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  children: [
                                    Container(
                                      width: 48.0,
                                      height: 48.0,
                                      decoration: BoxDecoration(
                                        color: ClassifiedAppTheme.of(context).tertiary,
                                        shape: BoxShape.circle,
                                      ),
                                      alignment: AlignmentDirectional(0.0, 0.0),
                                      child: SvgPicture.asset(
                                        'assets/images/add-circle.svg',
                                        width: 24.0,
                                        height: 24.0,
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                    Expanded(
                                      child: Padding(
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            8.0, 0.0, 0.0, 0.0),
                                        child: Text(
                                          FFLocalizations.of(context).getText(
                                            'unique_key_6' /* add post */,
                                          ),
                                          style: ClassifiedAppTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'Satoshi',
                                                fontSize: 17.0,
                                                fontWeight: FontWeight.w500,
                                                useGoogleFonts: false,
                                              ),
                                        ),
                                      ),
                                    ),
                                    SvgPicture.asset(
                                      'assets/images/arrow-right.svg',
                                      width: 24.0,
                                      height: 24.0,
                                      fit: BoxFit.cover,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                       
                        // Padding(
                        //   padding:
                        //       EdgeInsetsDirectional.fromSTEB(6.0, 16.0, 6.0, 0.0),
                        //   child: InkWell(
                        //     splashColor: Colors.transparent,
                        //     focusColor: Colors.transparent,
                        //     hoverColor: Colors.transparent,
                        //     highlightColor: Colors.transparent,
                        //     onTap: () async {
                        //       context.pushNamed(
                        //         'SettingsPage',
                        //         extra: <String, dynamic>{
                        //           kTransitionInfoKey: TransitionInfo(
                        //             hasTransition: true,
                        //             transitionType: PageTransitionType.rightToLeft,
                        //             duration: Duration(milliseconds: 300),
                        //           ),
                        //         },
                        //       );
                        //     },
                        //     child: Container(
                        //       width: double.infinity,
                        //       height: 64.0,
                        //       decoration: BoxDecoration(
                        //         color:
                        //             ClassifiedAppTheme.of(context).secondaryBackground,
                        //         boxShadow: [
                        //           BoxShadow(
                        //             blurRadius: 4.0,
                        //             color: Color(0x33000000),
                        //             offset: Offset(0.0, 2.0),
                        //           )
                        //         ],
                        //         borderRadius: BorderRadius.circular(12.0),
                        //       ),
                        //       child: Padding(
                        //         padding: EdgeInsetsDirectional.fromSTEB(
                        //             16.0, 0.0, 16.0, 0.0),
                        //         child: Row(
                        //           mainAxisSize: MainAxisSize.max,
                        //           children: [
                        //             Container(
                        //               width: 48.0,
                        //               height: 48.0,
                        //               decoration: BoxDecoration(
                        //                 color: ClassifiedAppTheme.of(context).tertiary,
                        //                 shape: BoxShape.circle,
                        //               ),
                        //               alignment: AlignmentDirectional(0.0, 0.0),
                        //               child: SvgPicture.asset(
                        //                 'assets/images/settings.svg',
                        //                 width: 24.0,
                        //                 height: 24.0,
                        //                 fit: BoxFit.cover,
                        //               ),
                        //             ),
                        //             Expanded(
                        //               child: Padding(
                        //                 padding: EdgeInsetsDirectional.fromSTEB(
                        //                     8.0, 0.0, 0.0, 0.0),
                        //                 child: Text(
                        //                   FFLocalizations.of(context).getText(
                        //                     'ff71ledt' /* Settings */,
                        //                   ),
                        //                   style: ClassifiedAppTheme.of(context)
                        //                       .bodyMedium
                        //                       .override(
                        //                         fontFamily: 'Satoshi',
                        //                         fontSize: 17.0,
                        //                         fontWeight: FontWeight.w500,
                        //                         useGoogleFonts: false,
                        //                       ),
                        //                 ),
                        //               ),
                        //             ),
                        //             SvgPicture.asset(
                        //               'assets/images/arrow-right.svg',
                        //               width: 24.0,
                        //               height: 24.0,
                        //               fit: BoxFit.cover,
                        //             ),
                        //           ],
                        //         ),
                        //       ),
                        //     ),
                        //   ),
                        // ),
                        
                        appStore.logined?
                        Builder(
                          builder: (context) => Padding(
                            padding:
                                EdgeInsetsDirectional.fromSTEB(6.0, 16.0, 6.0, 0.0),
                            child: InkWell(
                              splashColor: Colors.transparent,
                              focusColor: Colors.transparent,
                              hoverColor: Colors.transparent,
                              highlightColor: Colors.transparent,
                              onTap: () async {
                                await showDialog(
                                  context: context,
                                  builder: (dialogContext) {
                                    return Dialog(
                                      elevation: 0,
                                      insetPadding: EdgeInsets.zero,
                                      backgroundColor: Colors.transparent,
                                      alignment: AlignmentDirectional(0.0, 0.0)
                                          .resolve(Directionality.of(context)),
                                      child: LogoutDialogeWidget(),
                                    );
                                  },
                                ).then((value) => setState(() {}));
                              },
                              child: Container(
                                width: double.infinity,
                                height: 64.0,
                                decoration: BoxDecoration(
                                  color: ClassifiedAppTheme.of(context)
                                      .secondaryBackground,
                                  boxShadow: [
                                    BoxShadow(
                                      blurRadius: 4.0,
                                      color: Color(0x33000000),
                                      offset: Offset(0.0, 2.0),
                                    )
                                  ],
                                  borderRadius: BorderRadius.circular(12.0),
                                ),
                                child: Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(
                                      16.0, 0.0, 16.0, 0.0),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    children: [
                                      Container(
                                        width: 48.0,
                                        height: 48.0,
                                        decoration: BoxDecoration(
                                          color:
                                              ClassifiedAppTheme.of(context).tertiary,
                                          shape: BoxShape.circle,
                                        ),
                                        alignment: AlignmentDirectional(0.0, 0.0),
                                        child: SvgPicture.asset(
                                          'assets/images/logout.svg',
                                          width: 24.0,
                                          height: 24.0,
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                      Expanded(
                                        child: Padding(
                                          padding: EdgeInsetsDirectional.fromSTEB(
                                              8.0, 0.0, 0.0, 0.0),
                                          child: Text(
                                            FFLocalizations.of(context).getText(
                                              'maxpseka' /* Log out */,
                                            ),
                                            style: ClassifiedAppTheme.of(context)
                                                .bodyMedium
                                                .override(
                                                  fontFamily: 'Satoshi',
                                                  fontSize: 17.0,
                                                  fontWeight: FontWeight.w500,
                                                  useGoogleFonts: false,
                                                ),
                                          ),
                                        ),
                                      ),
                                      SvgPicture.asset(
                                        'assets/images/arrow-right.svg',
                                        width: 24.0,
                                        height: 24.0,
                                        fit: BoxFit.cover,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        )
                    :
                        Builder(
                          builder: (context) => Padding(
                            padding:
                                EdgeInsetsDirectional.fromSTEB(6.0, 16.0, 6.0, 0.0),
                            child: InkWell(
                              splashColor: Colors.transparent,
                              focusColor: Colors.transparent,
                              hoverColor: Colors.transparent,
                              highlightColor: Colors.transparent,
                              onTap: () async {
                                 context.pushNamed(
                                  'LoginPage',
                                  extra: <String, dynamic>{
                                    kTransitionInfoKey: TransitionInfo(
                                      hasTransition: true,
                                      transitionType: PageTransitionType.rightToLeft,
                                      duration: Duration(milliseconds: 300),
                                    ),
                                  },
                                );
                                // await showDialog(
                                //   context: context,
                                //   builder: (dialogContext) {
                                //     return Dialog(
                                //       elevation: 0,
                                //       insetPadding: EdgeInsets.zero,
                                //       backgroundColor: Colors.transparent,
                                //       alignment: AlignmentDirectional(0.0, 0.0)
                                //           .resolve(Directionality.of(context)),
                                //       child: LogoutDialogeWidget(),
                                //     );
                                //   },
                                // ).then((value) => setState(() {}));
                              },
                              child: Container(
                                width: double.infinity,
                                height: 64.0,
                                decoration: BoxDecoration(
                                  color: ClassifiedAppTheme.of(context)
                                      .secondaryBackground,
                                  boxShadow: [
                                    BoxShadow(
                                      blurRadius: 4.0,
                                      color: Color(0x33000000),
                                      offset: Offset(0.0, 2.0),
                                    )
                                  ],
                                  borderRadius: BorderRadius.circular(12.0),
                                ),
                                child: Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(
                                      16.0, 0.0, 16.0, 0.0),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    children: [
                                      Container(
                                        width: 48.0,
                                        height: 48.0,
                                        decoration: BoxDecoration(
                                          color:
                                              ClassifiedAppTheme.of(context).tertiary,
                                          shape: BoxShape.circle,
                                        ),
                                        alignment: AlignmentDirectional(0.0, 0.0),
                                        child: SvgPicture.asset(
                                          'assets/images/login.svg',
                                          width: 24.0,
                                          height: 24.0,
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                      Expanded(
                                        child: Padding(
                                          padding: EdgeInsetsDirectional.fromSTEB(
                                              8.0, 0.0, 0.0, 0.0),
                                          child: Text(
                                            FFLocalizations.of(context).getText(
                                          'gwf8bwln' /* Contact us */,
                                        ),
                                            style: ClassifiedAppTheme.of(context)
                                                .bodyMedium
                                                .override(
                                                  fontFamily: 'Satoshi',
                                                  fontSize: 17.0,
                                                  fontWeight: FontWeight.w500,
                                                  useGoogleFonts: false,
                                                ),
                                          ),
                                        ),
                                      ),
                                      SvgPicture.asset(
                                        'assets/images/arrow-right.svg',
                                        width: 24.0,
                                        height: 24.0,
                                        fit: BoxFit.cover,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        Padding(
                        padding:
                            EdgeInsetsDirectional.fromSTEB(6.0, 16.0, 6.0, 0.0),
                        child: Container(
                          decoration: BoxDecoration(
                             boxShadow: [
                               BoxShadow(
                                 blurRadius: 5.0,
                                 color: Color(0x33000000),
                                 offset: Offset(0.0, 2.0),
                               )
                             ]
                          ),
                          child: ClassifiedAppLanguageSelector(
                            width: double.infinity,
                            height: 64.0,
                            backgroundColor:
                                ClassifiedAppTheme.of(context).secondaryBackground,
                            borderColor: Colors.transparent,
                            dropdownColor:
                                ClassifiedAppTheme.of(context).secondaryBackground,
                            dropdownIconColor:
                                ClassifiedAppTheme.of(context).primaryText,
                            borderRadius: 12.0,
                            textStyle: TextStyle(
                              fontFamily: 'Satoshi',
                              color: ClassifiedAppTheme.of(context).primaryText,
                              fontWeight: FontWeight.w500,
                              fontSize: 17.0,
                            ),
                            hideFlags: true,
                            flagSize: 24.0,
                            flagTextGap: 8.0,
                            currentLanguage:
                                FFLocalizations.of(context).languageCode,
                            languages: FFLocalizations.languages(),
                            onChanged: (lang) => setAppLanguage(context, lang),
                          ),
                        ),
                      ),
                      ]
                          .addToStart(SizedBox(height: 20.0))
                          .addToEnd(SizedBox(height: 20.0)),
                    ),
                  ).animateOnPageLoad(animationsMap['columnOnPageLoadAnimation']!),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
