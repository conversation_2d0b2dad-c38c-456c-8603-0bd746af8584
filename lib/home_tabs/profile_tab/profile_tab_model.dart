import 'package:soho_souk/pages/app_bar/app_bar_model.dart';

import '../../Classified_App/classified_app_model.dart';
import 'profile_tab_widget.dart' show ProfileTabWidget;
import 'package:flutter/material.dart';

class ProfileTabModel extends ClassifiedAppModel<ProfileTabWidget> {
  /// Initialization and disposal methods.
late AppBarModel appBarModel;
  @override
  void initState(BuildContext context) {
    appBarModel = createModel(context, () => AppBarModel());
  }

  @override
  void dispose() {
      appBarModel.dispose();
  }

  /// Action blocks are added here.

  /// Additional helper methods are added here.
}
