import 'dart:convert';
import 'dart:io';

import 'package:soho_souk/ApiUtils.dart';
import 'package:soho_souk/Classified_App/classified_app_radio_button.dart';
import 'package:soho_souk/main.dart';
import 'package:soho_souk/models/CategoryModel.dart';
import 'package:soho_souk/models/PostModel.dart';
import 'package:soho_souk/models/cityModel.dart';
import 'package:http/http.dart' as http;
import 'package:nb_utils/nb_utils.dart' as nb_utils;
import 'package:soho_souk/pages/app_bar/app_bar_widget.dart';
import 'package:soho_souk/service/serviceApi.dart';
import 'package:soho_souk/widget/loader.dart';
import '../../Classified_App/classified_app_drop_down.dart';
import '../../Classified_App/classified_app_theme.dart';
import '../../Classified_App/classified_app_util.dart';
import '../../Classified_App/form_field_controller.dart';
import '../../Classified_App/upload_data.dart';
import '/pages/app_button/app_button_widget.dart';
import 'dart:async';
import '/custom_code/actions/index.dart' as actions;
import 'package:flutter/material.dart';
// import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'add_product_tab_model.dart';
export 'add_product_tab_model.dart';

class EditPost extends StatefulWidget {
  const EditPost({Key? key, required this.id}) : super(key: key);
  final int id;
  @override
  State<EditPost> createState() => _EditPostState();
}

class _EditPostState extends State<EditPost> {
  late AddProductTabModel _model;

  StreamSubscription<bool>? _keyboardVisibilitySubscription;
  bool _isKeyboardVisible = false;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  bool loader = true;
  List? images = [];
  Map? list;
  String? category_name;
  String? city_name;
  List<FFUploadedFile> oldImages = [];
  int primaryImageDelete = 0;
  void deletePostImage(image_id) async {
    if (image_id == "0") {
      setState(() {
        primaryImageDelete = 1;
      });
    }
    final response = await http.get(
        Uri.parse("${ApiUtils.BASE_URL}delete-postad-image/${image_id}"),
        headers: {
          "Content-Type": "application/json",
          "Accept": "application/json",
          'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
        });
    if (response.statusCode == 200) {
      print('Image Deleted');
    } else {
      print('Error Occurred');
    }
  }

  getPostDetails() async {
    try {
      final response = await http.get(
          Uri.parse("${ApiUtils.BASE_URL}get-post-details/${widget.id}"),
          headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
          });
      final response_images = await http.get(
          Uri.parse("${ApiUtils.BASE_URL}get-post-images/${widget.id}"),
          headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
          });
      if (response.statusCode == 200) {
        setState(() {
          list = json.decode(response.body);
          List image = json.decode(response_images.body);
          _model.uploadedLocalFiles = image
              .map((item) => FFUploadedFile(
                  name: item['id'].toString() ?? '',
                  bytes: null,
                  path: item['image'],
                  blurHash: '0'))
              .toList();
          //image.map((e)=>images!.add(e['image'])).toList();

          loader = false;
          _model.textController1.text = list!['title'];
          _model.textController2.text = list!['price'].toString();
          _model.textController4.text = list!['description'];
          _model.dropDownValue1 =
              list!['post_type'] == 0 ? "Normal Ad" : "Feature Ad / Paid Ad";
          _model.dropDownValueController1 = FormFieldController<String>(
              list!['post_type'] == "0" ? "Normal Ad" : "Feature Ad / Paid Ad");
          _model.dropDownValue4 = list!['item_conditions'];
          _model.dropDownValueController4 =
              FormFieldController<String>(list!['item_conditions']);
          barter = list!['barter_enable'] == 1 ? "Yes" : "No";
          _model.radioButtonValueController1 = FormFieldController<String>(
              list!['barter_enable'] == 1 ? "Yes" : "No");
          _model.radioButtonValueController2 = FormFieldController<String>(
              list!['show_mobile'] == 0 ? "Yes" : "No");
          _model.radioButtonValueController3 = FormFieldController<String>(
              list!['enable_sms'] == 0 ? "Yes" : "No");
          FFAppState().selectedCategoryId = int.parse(list!['category']);
          FFAppState().selectedChildCategoryId =
              int.parse(list!['subcategory']);

          List<CategoryModels> category = appStore.categoryList
              .where((i) => i.id == int.parse(list!['category']))
              .toList();
          List<CategoryModels> subcategory = appStore.categoryList
              .where((i) => i.id == int.parse(list!['subcategory']))
              .toList();
          if (list!['childcategory'] != null) {
            FFAppState().selectedSecondChildCategoryId =
                int.parse(list!['childcategory']);
            List<CategoryModels> secondSubcategory = appStore.categoryList
                .where((i) => i.id == int.parse(list!['childcategory']))
                .toList();
            FFAppState().selectedSecondChildCategory =
                secondSubcategory[0].category_name!;
            FFAppState().haveSecondCat = true;
          } else {
            FFAppState().haveSecondCat = false;
          }
          FFAppState().selectedCategory = category[0].category_name!;
          FFAppState().selectedChildCategory = subcategory[0].category_name!;

          List<CityModels> selectCity =
              appStore.mainCity.where((i) => i.city == list!['city']).toList();
          _model.dropDownValueController2 =
              FormFieldController<String>(list!['city']);
          _model.dropDownValue2 = list!['city'];

          city = selectCity[0].id;
          // childCity =[];
          city_name = list!['city'];
          childCity = appStore.city.where((i) => i.parent_id == city).toList();
          final area_id =
              appStore.city.where((i) => i.city == list!['area']).toList();
          _model.dropDownValueController3 =
              FormFieldController<String>(list!['area']);
          _model.dropDownValue3 = list!['area'];
          area = area_id[0].id;
          // area = int.parse(list!['area']);
          // getArea(val);
        });
      } else {
        print('Error Occurred');
      }
    } catch (e) {
      print('Error Occurred product' + e.toString());
    }
  }

  // List<FFUploadedFile> getAllImages() {
  //   return [
  //     ...oldImages,
  //     ..._model.uploadedLocalFiles,
  //   ];
  // }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => AddProductTabModel());
    getPostDetails();
    if (!isWeb) {
      // _keyboardVisibilitySubscription =
      //     KeyboardVisibilityController().onChange.listen((bool visible) {
      //   setState(() {
      //     _isKeyboardVisible = visible;
      //   });
      // });
    }

    _model.textController1 ??= TextEditingController();
    _model.textFieldFocusNode1 ??= FocusNode();

    _model.textController2 ??= TextEditingController();
    _model.textFieldFocusNode2 ??= FocusNode();

    _model.textController3 ??= TextEditingController();
    _model.textFieldFocusNode3 ??= FocusNode();

    _model.textController4 ??= TextEditingController();
    _model.textFieldFocusNode4 ??= FocusNode();

    _model.textController5 ??= TextEditingController();
    _model.textFieldFocusNode5 ??= FocusNode();

    _model.textController6 ??= TextEditingController();
    _model.textFieldFocusNode6 ??= FocusNode();

    _model.textController7 ??= TextEditingController();
    _model.textFieldFocusNode7 ??= FocusNode();

    _model.textController8 ??= TextEditingController();
    _model.textFieldFocusNode8 ??= FocusNode();
  }

  @override
  void dispose() {
    _model.maybeDispose();

    if (!isWeb) {
      _keyboardVisibilitySubscription?.cancel();
    }
    super.dispose();
  }

  List<CityModels> childCity = [];
  int? city;
  int? area;
  String? barter;
  int primaryImage = 0;
  getArea(val) {
    List<CityModels> selectCity =
        appStore.mainCity.where((i) => i.city == val).toList();
    setState(() {
      city = selectCity[0].id;
      childCity = [];
      childCity = appStore.city.where((i) => i.parent_id == city).toList();
    });
  }

  Future<void> UpdatePost() async {
    try {
      showLoadingDialog(context);
      // Set up the URL
      var url = Uri.parse(
          '${ApiUtils.BASE_URL}update-postad'); // Replace with your Laravel API URL
      if (_model.uploadedLocalFiles.isEmpty ||
          _model.uploadedLocalFiles[0].path == null) {
        nb_utils.toast("No file selected or file path is null!",
            bgColor: Colors.red, textColor: Colors.black);
        Navigator.of(context, rootNavigator: true).pop(false);
        print('No file selected or file path is null');
        return;
      }
      // Create a multipart request
      var request = http.MultipartRequest('POST', url);
      // Loop through all files in `_model.uploadedLocalFiles`
      for (var fileData in _model.uploadedLocalFiles) {
        if (fileData.blurHash != '0') {
          final filePath = fileData.path;

          if (filePath == null || !File(filePath).existsSync()) {
            print('File does not exist or path is invalid: $filePath');
            continue;
          }

          // Add each file to the request
          request.files.add(await http.MultipartFile.fromPath(
            'images[]', // Use 'images[]' as the key for multiple files (update key based on backend requirement)
            filePath,
          ));
        }
      }
      //Attach the image file
      // print("Image Path: ${_model.uploadedLocalFiles[0].path}");
      // request.files.add(await http.MultipartFile.fromPath(
      //   'image', // Key name for the file in the backend
      //   _model.uploadedLocalFiles[0].path!, // File path
      // ));
      // Add fields (key-value pairs)
      request.fields['id'] = widget.id.toString();
      request.fields['post_type'] =
          _model.dropDownValue1 == "Normal Ad" ? "0" : "1";
      request.fields['category'] = FFAppState().selectedCategoryId.toString();
      request.fields['subcategory'] =
          FFAppState().selectedChildCategoryId.toString();
      request.fields['childcategory'] = FFAppState().haveSecondCat
          ? FFAppState().selectedSecondChildCategoryId.toString()
          : '0';
      request.fields['title'] =
          _model.textController1.text; // Replace with your input
      request.fields['price'] =
          _model.textController2.text; // Replace with your input
      request.fields['description'] =
          _model.textController4.text; // Replace with your input
      request.fields['city'] = city.toString();
      request.fields['area'] = area.toString();
      request.fields['city_name'] = _model.dropDownValue2.toString();
      request.fields['area_name'] = _model.dropDownValue3.toString();
      request.fields['vendor_name'] = appStore.user_name.toString();
      request.fields['primary_image'] = primaryImage.toString();
      request.fields['primaryImageDelete'] = primaryImageDelete.toString();
      request.fields['item_conditions'] = _model.dropDownValueController4!.value
          .toString(); // Example dropdown value
      request.fields['barter_enable'] = barter == "Yes" ? "1" : "0";
      request.fields['enable_sms'] =
          _model.radioButtonValueController3?.value == "Yes" ? "0" : "1";
      request.fields['show_mobile'] =
          _model.radioButtonValueController2?.value == "Yes" ? "0" : "1";
      print('Request fields: ${request.fields}');
      print('Request files: ${request.files}');

      // Send the request
      var response = await request.send();

      if (response.statusCode == 200) {
        Navigator.of(context, rootNavigator: true).pop(false);
        var responseData = await response.stream.bytesToString();
        debugPrint("Response: $responseData");
        nb_utils.toast("Post Update successfully!",
            bgColor: Colors.green, textColor: Colors.black);
        appStore.setAllPost(await ServiceApi().getAllPost());
        //appStore.setUpdatePost(PostModel.fromJson(jsonDecode(responseData)));

        //setState((){});
        context.safePop();
        // ScaffoldMessenger.of(context).showSnackBar(
        //   SnackBar(content: Text('Ad posted successfully!')),
        // );
      } else {
        Navigator.of(context, rootNavigator: true).pop(false);
        print("Failed with status: ${response.statusCode}");
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to post ad!')),
        );
      }
    } catch (e) {
      print("Error: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('An error occurred!')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return appStore.logined
        ? Scaffold(
            backgroundColor: Colors.white,
            body: SafeArea(
              top: true,
              child: Container(
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    wrapWithModel(
                      model: _model.appBarModel,
                      updateCallback: () => setState(() {}),
                      child: AppBarWidget(
                        title: "Edit Post",
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 16),
                      child: RichText(
                        textScaler: MediaQuery.of(context).textScaler,
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: FFLocalizations.of(context).getText(
                                'ajd6i3t3' /* Upload property images  */,
                              ),
                              style: ClassifiedAppTheme.of(context)
                                  .bodyMedium
                                  .override(
                                    fontFamily: 'Satoshi',
                                    color: Color(0xFF1A1A1A),
                                    fontSize: 16.0,
                                    fontWeight: FontWeight.bold,
                                    useGoogleFonts: false,
                                  ),
                            ),
                            TextSpan(
                              text: FFLocalizations.of(context).getText(
                                'i8qhw5bg' /* (max 10 photos) */,
                              ),
                              style: TextStyle(
                                fontFamily: 'Satoshi',
                                color: ClassifiedAppTheme.of(context).warning,
                                fontWeight: FontWeight.bold,
                                fontSize: 16.0,
                              ),
                            )
                          ],
                          style: ClassifiedAppTheme.of(context).bodyMedium,
                        ),
                      ),
                    ),
                    Padding(
                      padding:
                          EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 0.0, 0.0),
                      child: Row(
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Align(
                            alignment: AlignmentDirectional(-1.0, 0.0),
                            child: InkWell(
                              splashColor: Colors.transparent,
                              focusColor: Colors.transparent,
                              hoverColor: Colors.transparent,
                              highlightColor: Colors.transparent,
                              onTap: () async {
                                final selectedMedia = await selectMedia(
                                  imageQuality: 100,
                                  mediaSource: MediaSource.photoGallery,
                                  multiImage: true,
                                );
                                if (selectedMedia != null &&
                                    selectedMedia.every((m) =>
                                        validateFileFormat(
                                            m.storagePath, context))) {
                                  setState(() => _model.isDataUploading = true);
                                  var selectedUploadedFiles =
                                      <FFUploadedFile>[];

                                  try {
                                    showUploadMessage(
                                      context,
                                      'Uploading file...',
                                      showLoading: true,
                                    );
                                    selectedUploadedFiles = selectedMedia
                                        .map((m) => FFUploadedFile(
                                            name: m.storagePath.split('/').last,
                                            bytes: m.bytes,
                                            height: m.dimensions?.height,
                                            width: m.dimensions?.width,
                                            blurHash: m.blurHash,
                                            path: m.filePath))
                                        .toList();
                                  } finally {
                                    ScaffoldMessenger.of(context)
                                        .hideCurrentSnackBar();
                                    _model.isDataUploading = false;
                                  }
                                  if (selectedUploadedFiles.length ==
                                      selectedMedia.length) {
                                    setState(() {
                                      _model.uploadedLocalFiles
                                          .addAll(selectedUploadedFiles);
                                      // selectedUploadedFiles;
                                    });
                                    showUploadMessage(context, 'Success!');
                                  } else {
                                    setState(() {});
                                    showUploadMessage(
                                        context, 'Failed to upload data');
                                    return;
                                  }
                                }
                              },
                              child: Container(
                                width: 88.0,
                                height: 88.0,
                                decoration: BoxDecoration(
                                  color:
                                      ClassifiedAppTheme.of(context).tertiary,
                                  borderRadius: BorderRadius.circular(12.0),
                                ),
                                alignment: AlignmentDirectional(0.0, 0.0),
                                child: SvgPicture.asset(
                                  'assets/images/add.svg',
                                  width: 36.0,
                                  height: 36.0,
                                  fit: BoxFit.contain,
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            child: Padding(
                              padding: EdgeInsetsDirectional.fromSTEB(
                                  16.0, 0.0, 0.0, 20.0),
                              child: Container(
                                height: 90.0,
                                decoration: BoxDecoration(
                                  color: Colors.transparent,
                                ),
                                child: Visibility(
                                  visible: _model.uploadedLocalFiles.isNotEmpty,
                                  child: Builder(
                                    builder: (context) {
                                      List<FFUploadedFile> uplodeImageList =
                                          _model.uploadedLocalFiles
                                              .map((e) => e)
                                              .toList();
                                      return ListView.separated(
                                        padding: EdgeInsets.fromLTRB(
                                          0,
                                          0,
                                          16.0,
                                          0,
                                        ),
                                        scrollDirection: Axis.horizontal,
                                        itemCount: uplodeImageList.length,
                                        separatorBuilder: (_, __) =>
                                            SizedBox(width: 16.0),
                                        itemBuilder:
                                            (context, uplodeImageListIndex) {
                                          final uplodeImageListItem =
                                              uplodeImageList[
                                                  uplodeImageListIndex];
                                          print(
                                              "Image Path: ${uplodeImageListItem.path} ${uplodeImageListItem.path!.isNotEmpty}");

                                          return Stack(
                                            alignment:
                                                AlignmentDirectional(1.0, -1.0),
                                            children: [
                                              primaryImage ==
                                                      uplodeImageListIndex
                                                  ? ClipRRect(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              12.0),
                                                      child: Container(
                                                        width: 88.0,
                                                        height: 88.0,
                                                        decoration:
                                                            BoxDecoration(
                                                          image:
                                                              DecorationImage(
                                                            image: uplodeImageListItem
                                                                        .blurHash ==
                                                                    '0'
                                                                ? (uplodeImageListItem.path !=
                                                                            null &&
                                                                        uplodeImageListItem
                                                                            .path!
                                                                            .isNotEmpty
                                                                    ? NetworkImage("${ApiUtils.post_image}${uplodeImageListItem.path!}")
                                                                        as ImageProvider<
                                                                            Object>
                                                                    : const AssetImage('assets/images/placeholder.png')
                                                                        as ImageProvider<
                                                                            Object>)
                                                                : (uplodeImageListItem.bytes !=
                                                                            null &&
                                                                        uplodeImageListItem
                                                                            .bytes!
                                                                            .isNotEmpty
                                                                    ? MemoryImage(uplodeImageListItem.bytes!)
                                                                        as ImageProvider<Object>
                                                                    : const AssetImage('assets/images/placeholder.png') as ImageProvider<Object>),
                                                            fit: BoxFit.cover,
                                                            colorFilter:
                                                                ColorFilter
                                                                    .mode(
                                                              Colors.black
                                                                  .withOpacity(
                                                                      0.2),
                                                              BlendMode.darken,
                                                            ),
                                                          ),
                                                        ),
                                                        child: Column(
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .center,
                                                          children: const [
                                                            Icon(Icons.image,
                                                                color: Colors
                                                                    .white),
                                                            Text(
                                                              "Primary",
                                                              style: TextStyle(
                                                                  color: Colors
                                                                      .white),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    )
                                                  : GestureDetector(
                                                      onTap: () {
                                                        setState(() {
                                                          primaryImage =
                                                              uplodeImageListIndex;
                                                        });
                                                      },
                                                      child: ClipRRect(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(12.0),
                                                        child: Container(
                                                          width: 88.0,
                                                          height: 88.0,
                                                          decoration:
                                                              BoxDecoration(
                                                            image:
                                                                DecorationImage(
                                                              image: uplodeImageListItem
                                                                          .blurHash ==
                                                                      '0'
                                                                  ? (uplodeImageListItem.path !=
                                                                              null &&
                                                                          uplodeImageListItem
                                                                              .path!
                                                                              .isNotEmpty
                                                                      ? NetworkImage("${ApiUtils.post_image}${uplodeImageListItem.path!}")
                                                                          as ImageProvider<
                                                                              Object>
                                                                      : const AssetImage('assets/images/placeholder.png')
                                                                          as ImageProvider<
                                                                              Object>)
                                                                  : (uplodeImageListItem.bytes !=
                                                                              null &&
                                                                          uplodeImageListItem
                                                                              .bytes!
                                                                              .isNotEmpty
                                                                      ? MemoryImage(uplodeImageListItem.bytes!)
                                                                          as ImageProvider<Object>
                                                                      : const AssetImage('assets/images/placeholder.png') as ImageProvider<Object>),
                                                              fit: BoxFit.cover,
                                                              colorFilter:
                                                                  ColorFilter
                                                                      .mode(
                                                                Colors.black
                                                                    .withOpacity(
                                                                        0.2),
                                                                BlendMode
                                                                    .darken,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      )),
                                              Align(
                                                alignment: AlignmentDirectional(
                                                    1.0, -1.0),
                                                child: InkWell(
                                                  splashColor:
                                                      Colors.transparent,
                                                  focusColor:
                                                      Colors.transparent,
                                                  hoverColor:
                                                      Colors.transparent,
                                                  highlightColor:
                                                      Colors.transparent,
                                                  onTap: () {
                                                    FFAppState()
                                                        .update(() async {
                                                      // Remove the image at the current index
                                                      if (uplodeImageListItem
                                                              .blurHash ==
                                                          '0') {
                                                        deletePostImage(
                                                            uplodeImageListItem
                                                                .name);
                                                      }
                                                      _model.uploadedLocalFiles =
                                                          await actions.removeAtI(
                                                              uplodeImageListIndex,
                                                              _model
                                                                  .uploadedLocalFiles
                                                                  .toList());
                                                      uplodeImageList = _model
                                                          .uploadedLocalFiles
                                                          .toList();
                                                    });
                                                    setState(() {});
                                                  },
                                                  child: Container(
                                                    width: 30.0,
                                                    height: 30.0,
                                                    decoration: BoxDecoration(
                                                      color: ClassifiedAppTheme
                                                              .of(context)
                                                          .secondaryBackground,
                                                      shape: BoxShape.circle,
                                                    ),
                                                    child: Padding(
                                                      padding:
                                                          const EdgeInsets.all(
                                                              5.0),
                                                      child: ClipRRect(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(0.0),
                                                        child: SvgPicture.asset(
                                                          'assets/images/closeHardi.svg',
                                                          fit: BoxFit.cover,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          );
                                        },
                                      );
                                    },
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Form(
                        key: _model.formKey,
                        autovalidateMode: AutovalidateMode.disabled,
                        child: Padding(
                          padding: EdgeInsetsDirectional.fromSTEB(
                              16.0, 0.0, 16.0, 0.0),
                          child: ListView(
                            padding: EdgeInsets.fromLTRB(
                              0,
                              0,
                              0,
                              24.0,
                            ),
                            primary: false,
                            shrinkWrap: true,
                            scrollDirection: Axis.vertical,
                            children: [
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 16.0, 0.0, 0.0),
                                child: Text(
                                  FFLocalizations.of(context).getText(
                                    'prtc2xw5' /* Choose category */,
                                  ),
                                  style: ClassifiedAppTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        fontSize: 17.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 4.0, 0.0, 0.0),
                                child: InkWell(
                                  splashColor: Colors.transparent,
                                  focusColor: Colors.transparent,
                                  hoverColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                  onTap: () async {
                                    context.pushNamed(
                                      'ChooseCategoriesPage',
                                      extra: <String, dynamic>{
                                        kTransitionInfoKey: TransitionInfo(
                                          hasTransition: true,
                                          transitionType:
                                              PageTransitionType.rightToLeft,
                                          duration: Duration(milliseconds: 300),
                                        ),
                                      },
                                    );
                                  },
                                  child: Container(
                                    width: double.infinity,
                                    decoration: BoxDecoration(
                                      color: ClassifiedAppTheme.of(context)
                                          .secondaryBackground,
                                      borderRadius: BorderRadius.circular(12.0),
                                      border: Border.all(
                                        color:
                                            ClassifiedAppTheme.of(context).info,
                                      ),
                                    ),
                                    child: Padding(
                                      padding: EdgeInsetsDirectional.fromSTEB(
                                          18.0, 15.0, 18.0, 15.0),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.max,
                                        children: [
                                          Expanded(
                                            child: Text(
                                              valueOrDefault<String>(
                                                FFAppState().selectedCategory,
                                                'Select product category',
                                              ),
                                              style: ClassifiedAppTheme.of(
                                                      context)
                                                  .bodyMedium
                                                  .override(
                                                    fontFamily: 'Satoshi',
                                                    color:
                                                        ClassifiedAppTheme.of(
                                                                context)
                                                            .primaryText,
                                                    fontSize: 17.0,
                                                    fontWeight: FontWeight.w500,
                                                    useGoogleFonts: false,
                                                  ),
                                            ),
                                          ),
                                          SvgPicture.asset(
                                            'assets/images/arrow-right.svg',
                                            width: 20.0,
                                            height: 20.0,
                                            fit: BoxFit.cover,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              if (FFAppState().selectedCategoryId != 0)
                                Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(
                                      0.0, 16.0, 0.0, 0.0),
                                  child: Text(
                                    "Select Sub Category",
                                    style: ClassifiedAppTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'Satoshi',
                                          fontSize: 17.0,
                                          fontWeight: FontWeight.w500,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ),
                              if (FFAppState().selectedCategoryId != 0)
                                Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(
                                      0.0, 4.0, 0.0, 0.0),
                                  child: InkWell(
                                    splashColor: Colors.transparent,
                                    focusColor: Colors.transparent,
                                    hoverColor: Colors.transparent,
                                    highlightColor: Colors.transparent,
                                    onTap: () async {
                                      context.pushNamed(
                                        'ChooseSubCategoriesPage',
                                        extra: <String, dynamic>{
                                          kTransitionInfoKey: TransitionInfo(
                                            hasTransition: true,
                                            transitionType:
                                                PageTransitionType.rightToLeft,
                                            duration:
                                                Duration(milliseconds: 300),
                                          ),
                                        },
                                      );
                                    },
                                    child: Container(
                                      width: double.infinity,
                                      decoration: BoxDecoration(
                                        color: ClassifiedAppTheme.of(context)
                                            .secondaryBackground,
                                        borderRadius:
                                            BorderRadius.circular(12.0),
                                        border: Border.all(
                                          color: ClassifiedAppTheme.of(context)
                                              .info,
                                        ),
                                      ),
                                      child: Padding(
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            18.0, 15.0, 18.0, 15.0),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.max,
                                          children: [
                                            Expanded(
                                              child: Text(
                                                valueOrDefault<String>(
                                                  FFAppState()
                                                      .selectedChildCategory,
                                                  'Select product subCategory',
                                                ),
                                                style: ClassifiedAppTheme.of(
                                                        context)
                                                    .bodyMedium
                                                    .override(
                                                      fontFamily: 'Satoshi',
                                                      color:
                                                          ClassifiedAppTheme.of(
                                                                  context)
                                                              .primaryText,
                                                      fontSize: 17.0,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      useGoogleFonts: false,
                                                    ),
                                              ),
                                            ),
                                            SvgPicture.asset(
                                              'assets/images/arrow-right.svg',
                                              width: 20.0,
                                              height: 20.0,
                                              fit: BoxFit.cover,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              if (FFAppState().haveSecondCat)
                                Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(
                                      0.0, 16.0, 0.0, 0.0),
                                  child: Text(
                                    "Select Child Category",
                                    style: ClassifiedAppTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'Satoshi',
                                          fontSize: 17.0,
                                          fontWeight: FontWeight.w500,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ),
                              if (FFAppState().haveSecondCat)
                                Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(
                                      0.0, 4.0, 0.0, 0.0),
                                  child: InkWell(
                                    splashColor: Colors.transparent,
                                    focusColor: Colors.transparent,
                                    hoverColor: Colors.transparent,
                                    highlightColor: Colors.transparent,
                                    onTap: () async {
                                      context.pushNamed(
                                        'ChooseSecondSubCategoriesPage',
                                        extra: <String, dynamic>{
                                          kTransitionInfoKey: TransitionInfo(
                                            hasTransition: true,
                                            transitionType:
                                                PageTransitionType.rightToLeft,
                                            duration:
                                                Duration(milliseconds: 300),
                                          ),
                                        },
                                      );
                                    },
                                    child: Container(
                                      width: double.infinity,
                                      decoration: BoxDecoration(
                                        color: ClassifiedAppTheme.of(context)
                                            .secondaryBackground,
                                        borderRadius:
                                            BorderRadius.circular(12.0),
                                        border: Border.all(
                                          color: ClassifiedAppTheme.of(context)
                                              .info,
                                        ),
                                      ),
                                      child: Padding(
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            18.0, 15.0, 18.0, 15.0),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.max,
                                          children: [
                                            Expanded(
                                              child: Text(
                                                valueOrDefault<String>(
                                                  FFAppState()
                                                      .selectedSecondChildCategory,
                                                  'Select product childCategory',
                                                ),
                                                style: ClassifiedAppTheme.of(
                                                        context)
                                                    .bodyMedium
                                                    .override(
                                                      fontFamily: 'Satoshi',
                                                      color:
                                                          ClassifiedAppTheme.of(
                                                                  context)
                                                              .primaryText,
                                                      fontSize: 17.0,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      useGoogleFonts: false,
                                                    ),
                                              ),
                                            ),
                                            SvgPicture.asset(
                                              'assets/images/arrow-right.svg',
                                              width: 20.0,
                                              height: 20.0,
                                              fit: BoxFit.cover,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 16.0, 0.0, 0.0),
                                child: Text(
                                  FFLocalizations.of(context).getText(
                                    'h2jy6c4t' /* Product name */,
                                  ),
                                  style: ClassifiedAppTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        fontSize: 17.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 4.0, 0.0, 0.0),
                                child: TextFormField(
                                  controller: _model.textController1,
                                  focusNode: _model.textFieldFocusNode1,
                                  textInputAction: TextInputAction.next,
                                  obscureText: false,
                                  decoration: InputDecoration(
                                    labelStyle: ClassifiedAppTheme.of(context)
                                        .labelMedium,
                                    hintText:
                                        FFLocalizations.of(context).getText(
                                      '566r7knm' /* Enter product name */,
                                    ),
                                    hintStyle: ClassifiedAppTheme.of(context)
                                        .labelMedium
                                        .override(
                                          fontFamily: 'Satoshi',
                                          color: ClassifiedAppTheme.of(context)
                                              .secondaryText,
                                          fontSize: 17.0,
                                          fontWeight: FontWeight.w500,
                                          useGoogleFonts: false,
                                        ),
                                    errorStyle: ClassifiedAppTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'Satoshi',
                                          color: ClassifiedAppTheme.of(context)
                                              .error,
                                          useGoogleFonts: false,
                                        ),
                                    enabledBorder: OutlineInputBorder(
                                      borderSide: BorderSide(
                                        color:
                                            ClassifiedAppTheme.of(context).info,
                                        width: 1.0,
                                      ),
                                      borderRadius: BorderRadius.circular(12.0),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderSide: BorderSide(
                                        color: ClassifiedAppTheme.of(context)
                                            .primary,
                                        width: 1.0,
                                      ),
                                      borderRadius: BorderRadius.circular(12.0),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderSide: BorderSide(
                                        color: ClassifiedAppTheme.of(context)
                                            .error,
                                        width: 1.0,
                                      ),
                                      borderRadius: BorderRadius.circular(12.0),
                                    ),
                                    focusedErrorBorder: OutlineInputBorder(
                                      borderSide: BorderSide(
                                        color: ClassifiedAppTheme.of(context)
                                            .error,
                                        width: 1.0,
                                      ),
                                      borderRadius: BorderRadius.circular(12.0),
                                    ),
                                  ),
                                  style: ClassifiedAppTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        fontSize: 17.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                  keyboardType: TextInputType.name,
                                  cursorColor: Color(0xFF6753D6),
                                  validator: _model.textController1Validator
                                      .asValidator(context),
                                ),
                              ),
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 16.0, 0.0, 0.0),
                                child: Text(
                                  FFLocalizations.of(context).getText(
                                    'dza85s91' /* Type */,
                                  ),
                                  style: ClassifiedAppTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        fontSize: 17.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 8.0, 0.0, 0.0),
                                child: ClassifiedAppDropDown<String>(
                                  controller:
                                      _model.dropDownValueController1 ??=
                                          FormFieldController<String>(null),
                                  options: [
                                    "Normal Ad",
                                    "Feature Ad / Paid Ad"
                                  ],
                                  onChanged: (val) => setState(
                                      () => _model.dropDownValue1 = val),
                                  width: double.infinity,
                                  height: 56.0,
                                  textStyle: ClassifiedAppTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        fontSize: 17.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                  hintText: "Choose Post Type",
                                  icon: Icon(
                                    Icons.keyboard_arrow_down_rounded,
                                    color: ClassifiedAppTheme.of(context)
                                        .primaryText,
                                    size: 24.0,
                                  ),
                                  fillColor: ClassifiedAppTheme.of(context)
                                      .secondaryBackground,
                                  elevation: 2.0,
                                  borderColor:
                                      ClassifiedAppTheme.of(context).info,
                                  borderWidth: 1.0,
                                  borderRadius: 12.0,
                                  margin: EdgeInsetsDirectional.fromSTEB(
                                      16.0, 4.0, 16.0, 4.0),
                                  hidesUnderline: true,
                                  isOverButton: true,
                                  isSearchable: false,
                                  isMultiSelect: false,
                                ),
                              ),
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 16.0, 0.0, 0.0),
                                child: Text(
                                  "Item Conditions",
                                  style: ClassifiedAppTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        fontSize: 17.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 12.0, 0.0, 0.0),
                                child: ClassifiedAppDropDown<String>(
                                  controller:
                                      _model.dropDownValueController4 ??=
                                          FormFieldController<String>(null),
                                  options: appStore.condition
                                      .map((e) => e.toString())
                                      .toList(),
                                  onChanged: (val) => setState(
                                      () => _model.dropDownValue4 = val),
                                  width: double.infinity,
                                  height: 54.0,
                                  textStyle: ClassifiedAppTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        fontSize: 17.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                  hintText: "Select item Conditions",
                                  icon: Icon(
                                    Icons.keyboard_arrow_right_sharp,
                                    color: ClassifiedAppTheme.of(context)
                                        .secondaryText,
                                    size: 24.0,
                                  ),
                                  fillColor: ClassifiedAppTheme.of(context)
                                      .secondaryBackground,
                                  elevation: 2.0,
                                  borderColor:
                                      ClassifiedAppTheme.of(context).info,
                                  borderWidth: 1.0,
                                  borderRadius: 8.0,
                                  margin: EdgeInsetsDirectional.fromSTEB(
                                      16.0, 4.0, 16.0, 4.0),
                                  hidesUnderline: true,
                                  isOverButton: true,
                                  isSearchable: false,
                                  isMultiSelect: false,
                                ),
                              ),
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 16.0, 0.0, 0.0),
                                child: Text(
                                  FFLocalizations.of(context).getText(
                                    '3jr3k99b' /* Price */,
                                  ),
                                  style: ClassifiedAppTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        fontSize: 17.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 4.0, 0.0, 0.0),
                                child: TextFormField(
                                  controller: _model.textController2,
                                  focusNode: _model.textFieldFocusNode2,
                                  textInputAction: TextInputAction.next,
                                  obscureText: false,
                                  decoration: InputDecoration(
                                    labelStyle: ClassifiedAppTheme.of(context)
                                        .labelMedium,
                                    hintText:
                                        FFLocalizations.of(context).getText(
                                      'oemlegsq' /* Enter product price */,
                                    ),
                                    hintStyle: ClassifiedAppTheme.of(context)
                                        .labelMedium
                                        .override(
                                          fontFamily: 'Satoshi',
                                          color: ClassifiedAppTheme.of(context)
                                              .secondaryText,
                                          fontSize: 17.0,
                                          fontWeight: FontWeight.w500,
                                          useGoogleFonts: false,
                                        ),
                                    errorStyle: ClassifiedAppTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'Satoshi',
                                          color: ClassifiedAppTheme.of(context)
                                              .error,
                                          useGoogleFonts: false,
                                        ),
                                    enabledBorder: OutlineInputBorder(
                                      borderSide: BorderSide(
                                        color:
                                            ClassifiedAppTheme.of(context).info,
                                        width: 1.0,
                                      ),
                                      borderRadius: BorderRadius.circular(12.0),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderSide: BorderSide(
                                        color: ClassifiedAppTheme.of(context)
                                            .primary,
                                        width: 1.0,
                                      ),
                                      borderRadius: BorderRadius.circular(12.0),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderSide: BorderSide(
                                        color: ClassifiedAppTheme.of(context)
                                            .error,
                                        width: 1.0,
                                      ),
                                      borderRadius: BorderRadius.circular(12.0),
                                    ),
                                    focusedErrorBorder: OutlineInputBorder(
                                      borderSide: BorderSide(
                                        color: ClassifiedAppTheme.of(context)
                                            .error,
                                        width: 1.0,
                                      ),
                                      borderRadius: BorderRadius.circular(12.0),
                                    ),
                                  ),
                                  style: ClassifiedAppTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        fontSize: 17.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                  keyboardType: TextInputType.number,
                                  cursorColor: Color(0xFF6753D6),
                                  validator: _model.textController3Validator
                                      .asValidator(context),
                                ),
                              ),
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 16.0, 0.0, 0.0),
                                child: Text(
                                  FFLocalizations.of(context).getText(
                                    '96zicz46' /* Description */,
                                  ),
                                  style: ClassifiedAppTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        fontSize: 17.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 4.0, 0.0, 0.0),
                                child: TextFormField(
                                  controller: _model.textController4,
                                  focusNode: _model.textFieldFocusNode4,
                                  textInputAction: TextInputAction.next,
                                  obscureText: false,
                                  decoration: InputDecoration(
                                    labelStyle: ClassifiedAppTheme.of(context)
                                        .labelMedium,
                                    hintText:
                                        FFLocalizations.of(context).getText(
                                      'xgeu08dy' /* Enter product description */,
                                    ),
                                    hintStyle: ClassifiedAppTheme.of(context)
                                        .labelMedium
                                        .override(
                                          fontFamily: 'Satoshi',
                                          color: ClassifiedAppTheme.of(context)
                                              .secondaryText,
                                          fontSize: 17.0,
                                          fontWeight: FontWeight.w500,
                                          useGoogleFonts: false,
                                        ),
                                    errorStyle: ClassifiedAppTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'Satoshi',
                                          color: ClassifiedAppTheme.of(context)
                                              .error,
                                          useGoogleFonts: false,
                                        ),
                                    enabledBorder: OutlineInputBorder(
                                      borderSide: BorderSide(
                                        color:
                                            ClassifiedAppTheme.of(context).info,
                                        width: 1.0,
                                      ),
                                      borderRadius: BorderRadius.circular(12.0),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderSide: BorderSide(
                                        color: ClassifiedAppTheme.of(context)
                                            .primary,
                                        width: 1.0,
                                      ),
                                      borderRadius: BorderRadius.circular(12.0),
                                    ),
                                    errorBorder: OutlineInputBorder(
                                      borderSide: BorderSide(
                                        color: ClassifiedAppTheme.of(context)
                                            .error,
                                        width: 1.0,
                                      ),
                                      borderRadius: BorderRadius.circular(12.0),
                                    ),
                                    focusedErrorBorder: OutlineInputBorder(
                                      borderSide: BorderSide(
                                        color: ClassifiedAppTheme.of(context)
                                            .error,
                                        width: 1.0,
                                      ),
                                      borderRadius: BorderRadius.circular(12.0),
                                    ),
                                  ),
                                  style: ClassifiedAppTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        fontSize: 17.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                  maxLines: 4,
                                  cursorColor: Color(0xFF6753D6),
                                  validator: _model.textController4Validator
                                      .asValidator(context),
                                ),
                              ),
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 16.0, 0.0, 0.0),
                                child: Text(
                                  "Select City",
                                  style: ClassifiedAppTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        fontSize: 17.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 12.0, 0.0, 0.0),
                                child: ClassifiedAppDropDown<String>(
                                  controller:
                                      _model.dropDownValueController2 ??=
                                          FormFieldController<String>(null),
                                  options: appStore.mainCity
                                      .map((data) => data.city.toString())
                                      .toList(),
                                  onChanged: (val) {
                                    getArea(val);
                                    setState(() => _model.dropDownValue2 = val);
                                  },
                                  width: double.infinity,
                                  height: 54.0,
                                  textStyle: ClassifiedAppTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        fontSize: 17.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                  hintText: "Select City",
                                  icon: Icon(
                                    Icons.keyboard_arrow_right_sharp,
                                    color: ClassifiedAppTheme.of(context)
                                        .secondaryText,
                                    size: 24.0,
                                  ),
                                  fillColor: ClassifiedAppTheme.of(context)
                                      .secondaryBackground,
                                  elevation: 2.0,
                                  borderColor:
                                      ClassifiedAppTheme.of(context).info,
                                  borderWidth: 1.0,
                                  borderRadius: 8.0,
                                  margin: EdgeInsetsDirectional.fromSTEB(
                                      16.0, 4.0, 16.0, 4.0),
                                  hidesUnderline: true,
                                  isOverButton: true,
                                  isSearchable: false,
                                  isMultiSelect: false,
                                ),
                              ),
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 16.0, 0.0, 0.0),
                                child: Text(
                                  "Select Area",
                                  style: ClassifiedAppTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        fontSize: 17.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 12.0, 0.0, 0.0),
                                child: ClassifiedAppDropDown<String>(
                                  controller:
                                      _model.dropDownValueController3 ??=
                                          FormFieldController<String>(null),
                                  options: childCity
                                      .map((data) => data.city.toString())
                                      .toList(),
                                  onChanged: (val) => setState(() {
                                    _model.dropDownValue3 = val;
                                    List<CityModels> selectArea = appStore.city
                                        .where((i) => i.city == val)
                                        .toList();
                                    area = selectArea[0].id;
                                  }),
                                  width: double.infinity,
                                  height: 54.0,
                                  textStyle: ClassifiedAppTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        fontSize: 17.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                  hintText: "",
                                  icon: Icon(
                                    Icons.keyboard_arrow_right_sharp,
                                    color: ClassifiedAppTheme.of(context)
                                        .secondaryText,
                                    size: 24.0,
                                  ),
                                  fillColor: ClassifiedAppTheme.of(context)
                                      .secondaryBackground,
                                  elevation: 2.0,
                                  borderColor:
                                      ClassifiedAppTheme.of(context).info,
                                  borderWidth: 1.0,
                                  borderRadius: 8.0,
                                  margin: EdgeInsetsDirectional.fromSTEB(
                                      16.0, 4.0, 16.0, 4.0),
                                  hidesUnderline: true,
                                  isOverButton: true,
                                  isSearchable: false,
                                  isMultiSelect: false,
                                ),
                              ),
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 16.0, 0.0, 0.0),
                                child: Text(
                                  " Do you like to enable barter?",
                                  style: ClassifiedAppTheme.of(context)
                                      .bodyMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        fontSize: 18.0,
                                        fontWeight: FontWeight.bold,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 12.0, 0.0, 0.0),
                                child: ClassifiedAppRadioButton(
                                  options: ["Yes", "No"].toList(),
                                  onChanged: (val) => setState(() {
                                    barter = val;
                                  }),
                                  controller:
                                      _model.radioButtonValueController1 ??=
                                          FormFieldController<String>("Yes"),
                                  optionHeight: 24.0,
                                  textStyle: ClassifiedAppTheme.of(context)
                                      .labelMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        color: ClassifiedAppTheme.of(context)
                                            .primaryText,
                                        fontSize: 17.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                  textPadding: EdgeInsetsDirectional.fromSTEB(
                                      0.0, 0.0, 16.0, 0.0),
                                  buttonPosition: RadioButtonPosition.left,
                                  direction: Axis.horizontal,
                                  radioButtonColor:
                                      ClassifiedAppTheme.of(context).primary,
                                  inactiveRadioButtonColor:
                                      ClassifiedAppTheme.of(context).info,
                                  toggleable: false,
                                  horizontalAlignment: WrapAlignment.start,
                                  verticalAlignment: WrapCrossAlignment.start,
                                ),
                              ),
                              SizedBox(
                                height: 20,
                              ),
                              Row(
                                children: [
                                  Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        0.0, 16.0, 0.0, 0.0),
                                    child: Text(
                                      FFLocalizations.of(context).getText(
                                        'unique_key_4' /* enable call? */,
                                      ),
                                      style: ClassifiedAppTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'Satoshi',
                                            fontSize: 18.0,
                                            fontWeight: FontWeight.bold,
                                            useGoogleFonts: false,
                                          ),
                                    ),
                                  ),
                                  if (appStore.user_mobile != null)
                                    Padding(
                                      padding: EdgeInsetsDirectional.fromSTEB(
                                          0.0, 16.0, 0.0, 0.0),
                                      child: Text(
                                        ' (+971 ${appStore.user_mobile})',
                                        style: ClassifiedAppTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              fontFamily: 'Satoshi',
                                              fontSize: 16.0,
                                              color: Colors.red,
                                              fontWeight: FontWeight.normal,
                                              useGoogleFonts: false,
                                            ),
                                      ),
                                    ),
                                ],
                              ),
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 12.0, 0.0, 0.0),
                                child: ClassifiedAppRadioButton(
                                  options: ["Yes", "No"].toList(),
                                  onChanged: (val) => setState(() {
                                    // barter =val;
                                  }),
                                  controller:
                                      _model.radioButtonValueController2 ??=
                                          FormFieldController<String>("Yes"),
                                  optionHeight: 24.0,
                                  textStyle: ClassifiedAppTheme.of(context)
                                      .labelMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        color: ClassifiedAppTheme.of(context)
                                            .primaryText,
                                        fontSize: 17.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                  textPadding: EdgeInsetsDirectional.fromSTEB(
                                      0.0, 0.0, 16.0, 0.0),
                                  buttonPosition: RadioButtonPosition.left,
                                  direction: Axis.horizontal,
                                  radioButtonColor:
                                      ClassifiedAppTheme.of(context).primary,
                                  inactiveRadioButtonColor:
                                      ClassifiedAppTheme.of(context).info,
                                  toggleable: false,
                                  horizontalAlignment: WrapAlignment.start,
                                  verticalAlignment: WrapCrossAlignment.start,
                                ),
                              ),
                              SizedBox(
                                height: 0,
                              ),
                              Row(
                                children: [
                                  Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        0.0, 16.0, 0.0, 0.0),
                                    child: Text(
                                      FFLocalizations.of(context).getText(
                                        'unique_key_5' /* enable call? */,
                                      ),
                                      style: ClassifiedAppTheme.of(context)
                                          .bodyMedium
                                          .override(
                                            fontFamily: 'Satoshi',
                                            fontSize: 18.0,
                                            fontWeight: FontWeight.bold,
                                            useGoogleFonts: false,
                                          ),
                                    ),
                                  ),
                                  if (appStore.user_mobile != null)
                                    Padding(
                                      padding: EdgeInsetsDirectional.fromSTEB(
                                          0.0, 16.0, 0.0, 0.0),
                                      child: Text(
                                        ' (+971 ${appStore.user_mobile})',
                                        style: ClassifiedAppTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              fontFamily: 'Satoshi',
                                              fontSize: 16.0,
                                              color: Colors.red,
                                              fontWeight: FontWeight.normal,
                                              useGoogleFonts: false,
                                            ),
                                      ),
                                    ),
                                ],
                              ),
                              Padding(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    0.0, 12.0, 0.0, 0.0),
                                child: ClassifiedAppRadioButton(
                                  options: ["Yes", "No"].toList(),
                                  onChanged: (val) => setState(() {
                                    // barter =val;
                                  }),
                                  controller:
                                      _model.radioButtonValueController3 ??=
                                          FormFieldController<String>("Yes"),
                                  optionHeight: 24.0,
                                  textStyle: ClassifiedAppTheme.of(context)
                                      .labelMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        color: ClassifiedAppTheme.of(context)
                                            .primaryText,
                                        fontSize: 17.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                  textPadding: EdgeInsetsDirectional.fromSTEB(
                                      0.0, 0.0, 16.0, 0.0),
                                  buttonPosition: RadioButtonPosition.left,
                                  direction: Axis.horizontal,
                                  radioButtonColor:
                                      ClassifiedAppTheme.of(context).primary,
                                  inactiveRadioButtonColor:
                                      ClassifiedAppTheme.of(context).info,
                                  toggleable: false,
                                  horizontalAlignment: WrapAlignment.start,
                                  verticalAlignment: WrapCrossAlignment.start,
                                ),
                              ),
                              SizedBox(
                                height: 20,
                              ),
                              Opacity(
                                opacity: (isWeb
                                        ? MediaQuery.viewInsetsOf(context)
                                                .bottom >
                                            0
                                        : _isKeyboardVisible)
                                    ? 0.0
                                    : 1.0,
                                child: Padding(
                                  padding: EdgeInsetsDirectional.fromSTEB(
                                      0.0, 0.0, 0.0, 16.0),
                                  child: wrapWithModel(
                                    model: _model.appButtonModel,
                                    updateCallback: () => setState(() {}),
                                    child: AppButtonWidget(
                                      text: 'Update post',
                                      action: () async {
                                        if (_model.formKey.currentState ==
                                                null ||
                                            !_model.formKey.currentState!
                                                .validate()) {
                                          nb_utils.toast(
                                              "Please fill all the fields",
                                              bgColor: Colors.red,
                                              textColor: Colors.black);
                                          return;
                                        }

                                        if (_model.dropDownValue1 == null) {
                                          return;
                                        }
                                        if (_model.dropDownValue2 == null) {
                                          return;
                                        }
                                        if (_model.dropDownValue3 == null) {
                                          return;
                                        }
                                        if (_model.dropDownValue4 == null) {
                                          return;
                                        }
                                        if (FFAppState().selectedCategoryId ==
                                            0) {
                                          toster("Please Choose Category");
                                          return;
                                        }
                                        if (FFAppState()
                                                .selectedChildCategoryId ==
                                            null) {
                                          toster("Please Choose Sub Category");
                                          return;
                                        }
                                        if (FFAppState().haveSecondCat) {
                                          if (FFAppState()
                                                  .selectedSecondChildCategoryId ==
                                              null) {
                                            toster(
                                                "Please Choose Child Category");
                                            return;
                                          }
                                        }
                                        // if (_model.dropDownValue5 == null) {
                                        //   return;
                                        // }
                                        UpdatePost();

                                        // context.goNamed(
                                        //   'HomeBottomBarPage',
                                        //   extra: <String, dynamic>{
                                        //     kTransitionInfoKey: TransitionInfo(
                                        //       hasTransition: true,
                                        //       transitionType: PageTransitionType.rightToLeft,
                                        //       duration: Duration(milliseconds: 300),
                                        //     ),
                                        //   },
                                        // );
                                        // setState(() {
                                        //   FFAppState().selectedPageIndex = 0;
                                        // });
                                      },
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          )
        : Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                'assets/images/access-denied.png',
                width: 120.0,
                height: 120.0,
                fit: BoxFit.contain,
              ),
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(0.0, 28.0, 0.0, 0.0),
                child: Text(
                  "Invalid Access",
                  style: ClassifiedAppTheme.of(context).bodyMedium.override(
                        fontFamily: 'Satoshi',
                        fontSize: 24.0,
                        fontWeight: FontWeight.bold,
                        useGoogleFonts: false,
                      ),
                ),
              ),
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(0.0, 16.0, 0.0, 0.0),
                child: Text(
                  "Please Login Create Your Post!",
                  textAlign: TextAlign.center,
                  style: ClassifiedAppTheme.of(context).bodyMedium.override(
                        fontFamily: 'Satoshi',
                        fontSize: 17.0,
                        fontWeight: FontWeight.w500,
                        useGoogleFonts: false,
                      ),
                ),
              ),
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(54.0, 28.0, 54.0, 0.0),
                child: wrapWithModel(
                  model: _model.appButtonModel,
                  updateCallback: () => setState(() {}),
                  child: AppButtonWidget(
                    text: 'Login Now',
                    action: () async {},
                  ),
                ),
              ),
            ],
          );
  }

  void toster(message) {
    nb_utils.toast("${message}", bgColor: Colors.red, textColor: Colors.black);
  }
}
