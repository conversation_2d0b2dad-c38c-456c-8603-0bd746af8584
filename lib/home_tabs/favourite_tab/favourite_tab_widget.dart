
import 'package:nb_utils/nb_utils.dart';
import 'package:soho_souk/ApiUtils.dart';
import 'package:soho_souk/main.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:soho_souk/widget/loader.dart';
import '../../Classified_App/classified_app_theme.dart';
import '../../Classified_App/classified_app_util.dart';
import '/pages/favourite_empty_component/favourite_empty_component_widget.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'favourite_tab_model.dart';
export 'favourite_tab_model.dart';

class FavouriteTabWidget extends StatefulWidget {
  const FavouriteTabWidget({super.key});

  @override
  State<FavouriteTabWidget> createState() => _FavouriteTabWidgetState();
}

class _FavouriteTabWidgetState extends State<FavouriteTabWidget> {
  late FavouriteTabModel _model;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => FavouriteTabModel());
  }

  @override
  void dispose() {
    _model.maybeDispose();

    super.dispose();
  }

  void removePost(id){
     showLoadingDialog(context);
     http
        .post(
      Uri.parse("${ApiUtils.BASE_URL}delete-favourite-post"),
      headers: <String, String>{
        'Content-Type': 'application/json; charset=UTF-8',
        'APP_KEY': "8Shm171pe2oTGvJlql7nxe2Ys/tHJaiiVq6vr5wIu5EJhEEmI3gVi"
      },
      body: jsonEncode(<String, dynamic>{
        'id':  id,
      }),
    )
        .then((response) {
             Navigator.of(context, rootNavigator: true).pop(false);
            Map lists = json.decode(response.body);
          if(response.statusCode==200){
              toasty(context, "${lists['message']}",
                bgColor: Colors.green, textColor: Colors.black);
                 appStore.removeFavPost(
                  id
                );
            
            setState((){

            });
          }else{
            toasty(context, "Something wrong!",
                bgColor: Colors.green, textColor: Colors.black);
              
          }
        });
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        Padding(
          padding: EdgeInsetsDirectional.fromSTEB(16.0, 24.0, 16.0, 0.0),
          child: Text(
            FFLocalizations.of(context).getText(
              'kdent1c2' /* Favorite */,
            ),
            style: ClassifiedAppTheme.of(context).bodyMedium.override(
                  fontFamily: 'Satoshi',
                  fontSize: 24.0,
                  fontWeight: FontWeight.bold,
                  useGoogleFonts: false,
                ),
          ),
        ),
        Expanded(
          child: Padding(
            padding: EdgeInsetsDirectional.fromSTEB(10.0, 0.0, 10.0, 0.0),
            child: Builder(
              builder: (context) {
                final favouriteItemsList =
                    appStore.favList.toList();
                if (favouriteItemsList.isEmpty) {
                  return FavouriteEmptyComponentWidget();
                }
                return GridView.builder(
                  padding: EdgeInsets.fromLTRB(
                    0,
                    16.0,
                    0,
                    70.0,
                  ),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: () {
                      if (MediaQuery.sizeOf(context).width <
                          kBreakpointSmall) {
                        return 2;
                      } else if (MediaQuery.sizeOf(context).width <
                          kBreakpointMedium) {
                        return 4;
                      } else if (MediaQuery.sizeOf(context).width <
                          kBreakpointLarge) {
                        return 6;
                      } else {
                        return 6;
                      }
                    }(),
                   crossAxisSpacing: 10.0,
                          mainAxisSpacing: 16.0,
                          childAspectRatio: 0.8,
                  ),
                  scrollDirection: Axis.vertical,
                  itemCount: appStore.favList.length,
                  itemBuilder: (context, favouriteItemsListIndex) {
                    final favouriteItemsListItem =
                        appStore.favList[favouriteItemsListIndex];
                      
                    return GestureDetector(
                        onTap: (){
                                    context.pushNamed(
                                'ProductDetailPage',
                                queryParameters: {
                                  'post': jsonEncode(favouriteItemsListItem.toJson()), // Serialize PostModel
                                 
                                }.withoutNulls,
                                extra: <String, dynamic>{
                                  kTransitionInfoKey: TransitionInfo(
                                    hasTransition: true,
                                    transitionType:
                                        PageTransitionType.rightToLeft,
                                    duration: Duration(milliseconds: 300),
                                  ),
                                },
                              );
                            },
                      child: Padding(
                        padding:
                            EdgeInsetsDirectional.fromSTEB(6.0, 0.0, 6.0, 0.0),
                        child: Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: ClassifiedAppTheme.of(context)
                                .secondaryBackground,
                            boxShadow: [
                              BoxShadow(
                                blurRadius: 4.0,
                                color: Color(0x27000000),
                                offset: Offset(0.0, 4.0),
                              )
                            ],
                            borderRadius: BorderRadius.circular(12.0),
                          ),
                          child: Padding(
                            padding: EdgeInsetsDirectional.fromSTEB(
                                8.0, 8.0, 8.0, 8.0),
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: Stack(
                                    alignment: AlignmentDirectional(1.0, -1.0),
                                    children: [
                                      ClipRRect(
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                        child: CachedNetworkImage(
                                          fadeInDuration:
                                              Duration(milliseconds: 500),
                                          fadeOutDuration:
                                              Duration(milliseconds: 500),
                                          imageUrl:
                                              "${ApiUtils.post_image}${favouriteItemsListItem.image}",
                                          width: double.infinity,
                                          height: 115.0,
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                      Padding(
                                        padding: EdgeInsetsDirectional.fromSTEB(
                                            0.0, 8.0, 8.0, 0.0),
                                        child: GestureDetector(
                                          onTap: () {
                                            removePost(favouriteItemsListItem.id);
                                          },
                                          child: Container(
                                            width: 24.0,
                                            height: 24.0,
                                            decoration: BoxDecoration(
                                              color: ClassifiedAppTheme.of(context)
                                                  .secondaryBackground,
                                              shape: BoxShape.circle,
                                            ),
                                            alignment:
                                                AlignmentDirectional(0.0, 0.0),
                                            child: SvgPicture.asset(
                                              'assets/images/trash.svg',
                                              width: 13.0,
                                              height: 13.0,
                                              fit: BoxFit.contain,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Expanded(
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceEvenly,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      AutoSizeText(
                                        favouriteItemsListItem.post_name!,
                                        maxLines: 1,
                                        style: ClassifiedAppTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              fontFamily: 'Satoshi',
                                              fontSize: 15.0,
                                              fontWeight: FontWeight.bold,
                                              useGoogleFonts: false,
                                            ),
                                        minFontSize: 13.0,
                                      ),
                                      Text(
                                        "AED  ${favouriteItemsListItem.price}",
                                        maxLines: 1,
                                        style: ClassifiedAppTheme.of(context)
                                            .bodyMedium
                                            .override(
                                              fontFamily: 'Satoshi',
                                              fontSize: 12.0,
                                              fontWeight: FontWeight.bold,
                                              useGoogleFonts: false,
                                            ),
                                      ),
                                      Row(
                                        mainAxisSize: MainAxisSize.max,
                                        children: [
                                         favouriteItemsListItem.vendor_image == ''
                                                  ? ClipRRect(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              30.0),
                                                      child: Image.asset(
                                                        'assets/images/soho_icon.jpg',
                                                        width: 32.0,
                                                        height: 32.0,
                                                        fit: BoxFit.contain,
                                                      ),
                                                    )
                                                  :  ClipRRect(
                                    borderRadius: BorderRadius.circular(30.0),
                                    child: CachedNetworkImage(
                                      
                                     width: 32.0,
                                        height: 32.0,
                                      fit: BoxFit.cover,
                                        imageUrl:
                                            "${ApiUtils.profile_files}${favouriteItemsListItem.vendor_image}",
                                        placeholder: (context, url) => Padding(
                                          padding: const EdgeInsets.all(35.0),
                                          child: Center(child: CircularProgressIndicator()),
                                        ),
                                        errorWidget: (context, url, error) =>
                                            Icon(Icons.error),
                                      ),
                                     
                                  ),
                                          Padding(
                                            padding:
                                                EdgeInsetsDirectional.fromSTEB(
                                                    8.0, 0.0, 0.0, 0.0),
                                            child: Column(
                                              mainAxisSize: MainAxisSize.max,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  favouriteItemsListItem
                                                      .vendor_name!,
                                                  style: ClassifiedAppTheme.of(
                                                          context)
                                                      .bodyMedium
                                                      .override(
                                                        fontFamily: 'Satoshi',
                                                        fontSize: 12.0,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        useGoogleFonts: false,
                                                      ),
                                                ),
                                                Padding(
                                                  padding: EdgeInsetsDirectional
                                                      .fromSTEB(
                                                          0.0, 4.0, 0.0, 0.0),
                                                  child: Row(
                                                    mainAxisSize:
                                                        MainAxisSize.max,
                                                    children: [
                                                      SvgPicture.asset(
                                                        'assets/images/location-home.svg',
                                                        width: 14.0,
                                                        height: 14.0,
                                                        fit: BoxFit.cover,
                                                      ),
                                                      Padding(
                                                        padding:
                                                            EdgeInsetsDirectional
                                                                .fromSTEB(
                                                                    4.0,
                                                                    0.0,
                                                                    0.0,
                                                                    0.0),
                                                        child: Text(
                                                          favouriteItemsListItem
                                                              .city!,
                                                          style: ClassifiedAppTheme
                                                                  .of(context)
                                                              .bodyMedium
                                                              .override(
                                                                fontFamily:
                                                                    'Satoshi',
                                                                fontSize: 12.0,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500,
                                                                useGoogleFonts:
                                                                    false,
                                                              ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
