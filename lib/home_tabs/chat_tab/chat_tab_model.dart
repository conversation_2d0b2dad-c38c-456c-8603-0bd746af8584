import '../../Classified_App/classified_app_model.dart';
import 'chat_tab_widget.dart' show ChatTabWidget;
import 'package:flutter/material.dart';

class ChatTabModel extends ClassifiedAppModel<ChatTabWidget> {
  ///  State fields for stateful widgets in this component.

  // State field(s) for TabBar widget.
  TabController? tabBarController;
  int get tabBarCurrentIndex =>
      tabBarController != null ? tabBarController!.index : 0;

  /// Initialization and disposal methods.

  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {
    tabBarController?.dispose();
  }

  /// Action blocks are added here.

  /// Additional helper methods are added here.
}
