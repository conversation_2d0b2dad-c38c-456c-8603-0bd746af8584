
// Imports other custom actions
// Imports custom functions
// Begin custom action code
// DO NOT REMOVE OR MODIFY THE CODE ABOVE!

// Set your action name, define your arguments and return parameter,
// and then add the boilerplate code using the green button on the right!
import 'package:shared_preferences/shared_preferences.dart';

Future<bool> getLogin() async {
  // Add your function code here!
  SharedPreferences prefs = await SharedPreferences.getInstance();
  bool intValue = prefs.getBool("com.mycompany.classifiedapp") ?? false;
  return intValue;
}
