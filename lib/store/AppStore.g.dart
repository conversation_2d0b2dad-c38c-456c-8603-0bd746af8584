// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'AppStore.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$AppStore on _AppStore, Store {
  late final _$sliderListAtom =
      Atom(name: '_AppStore.sliderList', context: context);

  @override
  List<SliderModel> get sliderList {
    _$sliderListAtom.reportRead();
    return super.sliderList;
  }

  @override
  set sliderList(List<SliderModel> value) {
    _$sliderListAtom.reportWrite(value, super.sliderList, () {
      super.sliderList = value;
    });
  }

  late final _$categoryListAtom =
      Atom(name: '_AppStore.categoryList', context: context);

  @override
  List<CategoryModels> get categoryList {
    _$categoryListAtom.reportRead();
    return super.categoryList;
  }

  @override
  set categoryList(List<CategoryModels> value) {
    _$categoryListAtom.reportWrite(value, super.categoryList, () {
      super.categoryList = value;
    });
  }

  late final _$latestPostAtom =
      Atom(name: '_AppStore.latestPost', context: context);

  @override
  List<PostModel> get latestPost {
    _$latestPostAtom.reportRead();
    return super.latestPost;
  }

  @override
  set latestPost(List<PostModel> value) {
    _$latestPostAtom.reportWrite(value, super.latestPost, () {
      super.latestPost = value;
    });
  }

  late final _$featurePostAtom =
      Atom(name: '_AppStore.featurePost', context: context);

  @override
  List<PostModel> get featurePost {
    _$featurePostAtom.reportRead();
    return super.featurePost;
  }

  @override
  set featurePost(List<PostModel> value) {
    _$featurePostAtom.reportWrite(value, super.featurePost, () {
      super.featurePost = value;
    });
  }

  late final _$_AppStoreActionController =
      ActionController(name: '_AppStore', context: context);

  @override
  void setSliderData(dynamic data) {
    final _$actionInfo = _$_AppStoreActionController.startAction(
        name: '_AppStore.setSliderData');
    try {
      return super.setSliderData(data);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setCategoryData(dynamic data) {
    final _$actionInfo = _$_AppStoreActionController.startAction(
        name: '_AppStore.setCategoryData');
    try {
      return super.setCategoryData(data);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  dynamic getMainCategoryData(int parent_id) {
    final _$actionInfo = _$_AppStoreActionController.startAction(
        name: '_AppStore.getMainCategoryData');
    try {
      return super.getMainCategoryData(parent_id);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setLatestPost(dynamic data) {
    final _$actionInfo = _$_AppStoreActionController.startAction(
        name: '_AppStore.setLatestPost');
    try {
      return super.setLatestPost(data);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setFeaturePost(dynamic data) {
    final _$actionInfo = _$_AppStoreActionController.startAction(
        name: '_AppStore.setFeaturePost');
    try {
      return super.setFeaturePost(data);
    } finally {
      _$_AppStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
sliderList: ${sliderList},
categoryList: ${categoryList},
latestPost: ${latestPost},
featurePost: ${featurePost}
    ''';
  }
}
