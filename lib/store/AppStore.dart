import 'package:flutter/material.dart';
import 'package:mobx/mobx.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:soho_souk/models/CategoryModel.dart';
import 'package:soho_souk/models/PostModel.dart';
import 'package:soho_souk/models/SiteModel.dart';
import 'package:soho_souk/models/SliderModel.dart';
import 'package:soho_souk/models/cityModel.dart';
import 'package:soho_souk/service/serviceApi.dart';
part 'AppStore.g.dart';

class AppStore = _AppStore with _$AppStore;

abstract class _AppStore with Store {
  @observable
  List<SliderModel> sliderList = ObservableList.of([]);
  @observable
  List<CategoryModels> categoryList = ObservableList.of([]);
  @observable
  List<CategoryModels> parentCategoryList = ObservableList.of([]);
  @observable
  List<PostModel> allPost = ObservableList.of([]);
  @observable
  List<PostModel> latestPost = ObservableList.of([]);
  @observable
  List<PostModel> featurePost = ObservableList.of([]);
  @observable
  List<PostModel> barterPost = ObservableList.of([]);
  @observable
  List<PostModel> electronics = ObservableList.of([]);
  @observable
  List<PostModel> vehicles = ObservableList.of([]);
  @observable
  List<PostModel> userPosts = ObservableList.of([]);
  @observable
  List<PostModel> favList = ObservableList.of([]);
  @observable
  List<CityModels> city = ObservableList.of([]);
  @observable
  List<CityModels> mainCity = ObservableList.of([]);
  @observable
  List<SiteModel> siteDetails = ObservableList.of([]);
  @observable
  bool logined = false;
  @observable
  String? user_name;
  @observable
  String? user_email, profile_image, user_mobile, firebase_key;
  @observable
  int? user_id;
  @observable
  List condition = [];
  @observable
  PageController? pageViewController;
  @observable
  int currentIndex = 0;

  @action
  void filterIndex(int index) {
    currentIndex = index;
  }

//slider
  @action
  void setSliderData(data) {
    sliderList = data;
  }

//site
  @action
  void setSiteData(data) {
    siteDetails = data;
  }

//category
  @action
  void setCategoryData(data) {
    categoryList = data;
    parentCategoryList = categoryList.where((i) => i.parent_id == 0).toList();
  }

  @action
  getMainCategoryData(int parent_id) {
    return categoryList.where((i) => i.parent_id == parent_id).toList();
  }

  //city
  @action
  void setCityData(data) {
    city = data;
    mainCity = city.where((i) => i.parent_id == 0).toList();
  }

  @action
  getMainCityData(int parent_id) {
    return city.where((i) => i.parent_id == parent_id).toList();
  }

//post
  @action
  void setAllPost(data) {
    allPost.clear();
    allPost.addAll(data);

    latestPost.clear();
    latestPost.addAll(allPost);

    featurePost.clear();
    featurePost.addAll(allPost.where((i) => i.post_type == 1));

    barterPost.clear();
    barterPost.addAll(allPost.where((i) => i.barter_enable == 1));

    electronics.clear();
    electronics.addAll(allPost.where((i) => i.category == 1));

    vehicles.clear();
    vehicles.addAll(allPost.where((i) => i.category == 6));

    if (user_id != null) {
      userPosts.clear();
      userPosts.addAll(allPost.where((i) => i.vendor_id == user_id));
    }
  }

  @action
  void setNewPost(PostModel data) {
    allPost = [...allPost, data]; // Create a new list with the updated data
    // allPost.add(data);
  }

  @action
  void setUpdatePost(PostModel data) {
    final index =
        allPost.indexWhere((post) => post.id == data.id); // Find post by ID
    if (index != -1) {
      allPost[index] = data; // Update the post in ObservableList.
      allPost = ObservableList.of(allPost);
    }
    userPosts.clear();
    userPosts.addAll(allPost.where((i) => i.vendor_id == user_id));
    featurePost.clear();
    featurePost.addAll(allPost.where((i) => i.post_type == 1));

    barterPost.clear();
    barterPost.addAll(allPost.where((i) => i.barter_enable == 1));
  }

  @action
  List<PostModel> cateroryPostList(id, bool isMain) {
    return allPost
        .where((i) => isMain ? i.category == id : i.sub_category == id)
        .toList();
  }

  @action
  void setLatestPost(data) {
    latestPost = data;
  }

  @action
  void deletePost(post_id) {
    allPost.removeWhere((element) => element.id == post_id);
    userPosts.clear();
    userPosts.addAll(allPost.where((i) => i.vendor_id == user_id));
    featurePost.clear();
    featurePost.addAll(allPost.where((i) => i.post_type == 1));
    latestPost.clear();
    latestPost.addAll(allPost);
    barterPost.clear();
    barterPost.addAll(allPost.where((i) => i.barter_enable == 1));
  }

  @action
  void setFeaturePost(data) {
    featurePost = data;
  }

  //barter
  @action
  void setBarterPost(data) {
    barterPost = data;
  }

  //barter
  @action
  void setElecPost(data) {
    electronics = data;
  }

  //barter
  @action
  void setVehiclesPost(data) {
    vehicles = data;
  }

  //fav
  @action
  void setFavPost(data) {
    favList.add(data);
    allPost = allPost.map((post) {
      post.fav = favList.any((favorite) => favorite.id == post.id);
      return post;
    }).toList();
  }

  @action
  void removeFavPost(int data) {
    favList.removeWhere((fav) => fav.id == data);
    //favList.remove(data);
    allPost = allPost.map((post) {
      post.fav = favList.any((favorite) => favorite.id == post.id);
      return post;
    }).toList();
  }

  //fav
  @action
  void getFavPost(data) {
    if (logined) {
      favList = data;
      allPost = allPost.map((post) {
        post.fav = favList.any((favorite) => favorite.id == post.id);
        return post;
      }).toList();
    }
  }

// @action
// void getFavPost(List<FavModel> data) {
//   favList = data;
//   for (var post in allPost) {
//     post.fav = favList.any((favorite) => favorite.id == post.id);
//   }
//   // Notify MobX about the changes in `allPost`
//   allPost = ObservableList.of(allPost);
// }

//item conditions
  @action
  void setItemConditions(data) {
    condition = data;
  }

  @action
  Future storeUserData() async {
    final prefs = await getSharedPref();
    final _user_id = await prefs.getInt('_id');
    final _user_name = await prefs.getString('name');
    final _user_email = await prefs.getString('email');
    final _user_image = await prefs.getString('profile_image');
    final _mobile = await prefs.getString('mobile');

    if (_user_id != null) {
      logined = true;
      user_id = _user_id;
      user_name = _user_name;
      user_email = _user_email;
      profile_image = _user_image;
      user_mobile = _mobile;

      // Update user posts when user data is loaded
      updateUserPostsForCurrentUser();
    } else {
      logined = false;
      // Clear user data when not logged in
      clearUserSpecificData();
    }
  }

  @action
  Future<void> logout() async {
    final prefs = await getSharedPref();
    prefs.remove('name');
    prefs.remove('email');
    prefs.remove('_id');
    prefs.remove('mobile');
    prefs.remove('profile_image');
    logined = false;
    user_id = null;
    user_name = null;
    user_email = null;
    profile_image = null;
    user_mobile = null;

    // Clear all user-specific post data
    clearUserSpecificData();
  }

  @action
  void StoreFirebasekey(key) {
    firebase_key = key;
  }

  @action
  Future<void> loginStore(
      name, id, msg, email, user_image, mobile, context) async {
    final prefs = await getSharedPref();
    await prefs.setString('name', name);
    await prefs.setInt('_id', id);
    await prefs.setString('email', email);
    if (mobile != null) {
      await prefs.setString('mobile', mobile);
    }
    if (user_image != null) {
      await prefs.setString('profile_image', user_image);
    }

    toasty(context, msg, bgColor: Colors.green, textColor: Colors.black);
    await storeUserData();

    // Refresh posts for the new user
    await refreshPostsForCurrentUser();
  }

  @action
  void updateProfile(name, email, user_image, mobile, context) async {
    final prefs = await getSharedPref();
    await prefs.setString('name', name);
    await prefs.setString('email', email);

    if (mobile != null) {
      await prefs.setString('mobile', mobile);
      user_mobile = mobile;
    }
    if (user_image != null) {
      await prefs.setString('profile_image', user_image);
      profile_image = user_image;
    }
    user_name = name;
    user_email = email;

    // context.safePop();
  }

  // Clear all user-specific data when logging out
  @action
  void clearUserSpecificData() {
    userPosts.clear();
    favList.clear();
    // Clear any other user-specific cached data
  }

  // Refresh posts and filter for current user
  @action
  Future<void> refreshPostsForCurrentUser() async {
    try {
      // Import ServiceApi if not already imported
      final serviceApi = ServiceApi();

      // Refresh all posts from server
      final freshPosts = await serviceApi.getAllPost();
      setAllPost(freshPosts);

      // Refresh favorites for current user
      if (user_id != null) {
        final freshFavorites = await serviceApi.getFav();
        getFavPost(freshFavorites);
      }
    } catch (e) {
      print('Error refreshing posts for current user: $e');
    }
  }

  // Update user posts when user_id changes
  @action
  void updateUserPostsForCurrentUser() {
    if (user_id != null) {
      userPosts.clear();
      userPosts.addAll(allPost.where((i) => i.vendor_id == user_id));
    } else {
      userPosts.clear();
    }
  }

  // Manual refresh method that can be called from UI
  @action
  Future<void> manualRefreshPosts() async {
    await refreshPostsForCurrentUser();
  }
}
