name: soho_souk
description: A new Flutter project.

publish_to: "none" # Remove this line if you wish to publish to pub.dev

version: 1.3.0+14

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  auto_size_text: 3.0.0
  cached_network_image: ^3.4.1
  carousel_slider: ^5.0.0
  cloud_firestore: ^5.6.0
  firebase_auth: ^5.4.1
  google_sign_in: ^6.2.2
  sign_in_with_apple: ^6.1.4
  collection: ^1.19.1
  cupertino_icons: null
  dio: ^5.7.0
  dropdown_button2: 2.3.9
  emoji_flag_converter: 1.1.0
  expandable: 5.0.1
  file_picker: ^10.1.2
  firebase_core: ^3.9.0
  firebase_messaging: ^15.1.6
  flutter:
    sdk: flutter
  flutter_animate: null
  flutter_cache_manager: ^3.4.1
  flutter_local_notifications: ^18.0.1
  flutter_localizations:
    sdk: flutter
  flutter_map: ^4.0.0
  flutter_mobx: ^2.2.1+1
  flutter_plugin_android_lifecycle: ^2.0.23
  flutter_spinkit: ^5.2.1
  flutter_svg: null
  flutter_widget_from_html: ^0.15.3
  font_awesome_flutter: null
  from_css_color: 2.0.0
  geocoding: ^2.1.0
  go_router: null
  google_fonts: null
  image_cropper: ^9.1.0
  image_picker: ^1.1.2
  intl: null
  json_path: null
  latlong2: ^0.8.1
  mime_type: ^1.0.1
  mobx: ^2.4.0
  nb_utils: ^7.0.7
  page_transition: 2.1.0
  path_provider: null
  path_provider_android: null
  path_provider_foundation: null
  path_provider_platform_interface: null
  photo_view: ^0.15.0
  pin_code_fields: 8.0.1
  plugin_platform_interface: 2.1.8
  provider: null
  shared_preferences: ^2.3.4
  shared_preferences_android: ^2.3.3
  shared_preferences_foundation: null
  shared_preferences_platform_interface: null
  shared_preferences_web: null
  smooth_page_indicator: ^1.2.0+3
  sqflite: null
  timeago: null
  url_launcher: ^6.3.1
  url_launcher_android: null
  url_launcher_ios: null
  url_launcher_platform_interface: null
  # video_player: null
  # video_player_android: null
  # video_player_avfoundation: null
  # video_player_platform_interface: null
  # video_player_web: null

dependency_overrides:
  win32: ^5.5.4
  http: null
  uuid: null

dev_dependencies:
  build_runner: ^2.4.8
  flutter_launcher_icons: ^0.11.0
  flutter_test:
    sdk: flutter
  mobx_codegen: ^2.6.2
flutter_icons:
  image_path: "assets/images/soho_icon.jpg"
  android: "launcher_icon"
  ios: true     # Enable iOS launcher icon generation
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/fonts/
    - assets/images/
    - assets/videos/
    - assets/audios/
    - assets/lottie_animations/
    - assets/rive_animations/
    - assets/pdfs/
  fonts:
    - family: "Satoshi"
      fonts:
        - asset: assets/fonts/Satoshi-Light.ttf
          weight: 300
        - asset: assets/fonts/Satoshi-Regular.ttf
        - asset: assets/fonts/Satoshi-Medium.ttf
          weight: 500
        - asset: assets/fonts/Satoshi-Bold.ttf
          weight: 700
        - asset: assets/fonts/Satoshi-Black.ttf
          weight: 900

    - family: Icomoon
      fonts:
        - asset: assets/fonts/icomoon.ttf
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
